#!/bin/bash

# Uru Workspace Platform - Local Integration Testing Script
# This script sets up and runs comprehensive tests for the integration system

set -e  # Exit on any error

echo "🧪 Uru Integration System - Local Testing Suite"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found. Please create one with the required environment variables."
    echo "Required variables:"
    echo "  - SUPABASE_URL"
    echo "  - SUPABASE_KEY"
    echo "  - JWT_SECRET"
    echo "  - ENCRYPTION_KEY"
    echo "  - GOOGLE_CLIENT_ID (optional)"
    echo "  - GOOGLE_CLIENT_SECRET (optional)"
    exit 1
fi

print_status "Loading environment variables from .env file..."
source .env

# Check required environment variables
required_vars=("SUPABASE_URL" "SUPABASE_KEY" "JWT_SECRET" "ENCRYPTION_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    exit 1
fi

print_success "All required environment variables are set"

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to check Docker and Docker Compose
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are available"
}

# Function to clean up previous containers
cleanup() {
    print_status "Cleaning up previous containers..."
    docker-compose -f docker-compose.local-test.yml down --remove-orphans || true
    docker system prune -f || true
    print_success "Cleanup completed"
}

# Function to build and start services
start_services() {
    print_status "Building and starting services..."
    
    # Build all services
    docker-compose -f docker-compose.local-test.yml build
    
    # Start services in background
    docker-compose -f docker-compose.local-test.yml up -d
    
    print_success "Services started"
}

# Function to check service health
check_services() {
    print_status "Checking service health..."
    
    # Wait for each service to be ready
    wait_for_service "Auth Service" "http://localhost:8000/health"
    wait_for_service "Composio Service" "http://localhost:8001/health"
    wait_for_service "MCP Proxy" "http://localhost:3001/health"
    wait_for_service "Frontend" "http://localhost:3000"
    
    print_success "All services are healthy"
}

# Function to setup database
setup_database() {
    print_status "Setting up integration database tables..."
    
    # Run the setup script
    python3 scripts/setup-integration-system.py
    
    if [ $? -eq 0 ]; then
        print_success "Database setup completed"
    else
        print_warning "Database setup had issues, but continuing with tests..."
    fi
}

# Function to run tests
run_tests() {
    print_status "Running integration system tests..."
    
    # Run the test script
    python3 scripts/test-integration-system.py
    
    local test_result=$?
    
    if [ $test_result -eq 0 ]; then
        print_success "All tests passed!"
        return 0
    else
        print_error "Some tests failed"
        return 1
    fi
}

# Function to show logs
show_logs() {
    print_status "Showing service logs (last 50 lines each)..."
    
    echo ""
    echo "=== Auth Service Logs ==="
    docker-compose -f docker-compose.local-test.yml logs --tail=50 auth-service
    
    echo ""
    echo "=== Composio Service Logs ==="
    docker-compose -f docker-compose.local-test.yml logs --tail=50 composio-service
    
    echo ""
    echo "=== MCP Proxy Logs ==="
    docker-compose -f docker-compose.local-test.yml logs --tail=50 mcp-proxy
    
    echo ""
    echo "=== Frontend Logs ==="
    docker-compose -f docker-compose.local-test.yml logs --tail=50 frontend
}

# Function to run containerized tests
run_containerized_tests() {
    print_status "Running containerized tests..."
    
    # Run tests in container
    docker-compose -f docker-compose.local-test.yml --profile test run --rm test-runner
    
    local test_result=$?
    
    if [ $test_result -eq 0 ]; then
        print_success "Containerized tests passed!"
        return 0
    else
        print_error "Containerized tests failed"
        return 1
    fi
}

# Main execution
main() {
    local skip_cleanup=false
    local show_logs_flag=false
    local containerized=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                skip_cleanup=true
                shift
                ;;
            --show-logs)
                show_logs_flag=true
                shift
                ;;
            --containerized)
                containerized=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --skip-cleanup    Skip cleanup of previous containers"
                echo "  --show-logs       Show service logs after tests"
                echo "  --containerized   Run tests in container instead of locally"
                echo "  --help           Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_docker
    
    # Cleanup if not skipped
    if [ "$skip_cleanup" = false ]; then
        cleanup
    fi
    
    # Start services
    start_services
    
    # Check service health
    check_services
    
    # Setup database
    setup_database
    
    # Run tests
    local test_success=false
    if [ "$containerized" = true ]; then
        if run_containerized_tests; then
            test_success=true
        fi
    else
        if run_tests; then
            test_success=true
        fi
    fi
    
    # Show logs if requested
    if [ "$show_logs_flag" = true ]; then
        show_logs
    fi
    
    # Final status
    echo ""
    echo "================================================"
    if [ "$test_success" = true ]; then
        print_success "🎉 Integration system testing completed successfully!"
        echo ""
        echo "Next steps:"
        echo "1. Review the test results above"
        echo "2. Test the frontend at http://localhost:3000/app/settings"
        echo "3. Deploy to production when ready"
        echo ""
        echo "To stop services: docker-compose -f docker-compose.local-test.yml down"
    else
        print_error "❌ Integration system testing failed!"
        echo ""
        echo "Troubleshooting:"
        echo "1. Check service logs: $0 --show-logs"
        echo "2. Verify environment variables in .env file"
        echo "3. Ensure all required OAuth credentials are configured"
        echo "4. Check Supabase connection and database setup"
    fi
    
    return $test_success
}

# Trap to cleanup on exit
trap 'print_warning "Script interrupted. Cleaning up..."; docker-compose -f docker-compose.local-test.yml down' INT TERM

# Run main function
main "$@"
