#!/usr/bin/env python3
"""
OAuth Migration Rollback Script
Rollback the OAuth storage migration by restoring from backup
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Setup logging with UTF-8 encoding for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rollback.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OAuthRollback:
    def __init__(self):
        """Initialize rollback with Supabase connection"""
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        
        if not all([self.supabase_url, self.supabase_key]):
            raise ValueError("Missing required environment variables: SUPABASE_URL, SUPABASE_KEY")
        
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        logger.info("🔧 Rollback initialized successfully")
    
    def list_backups(self) -> List[str]:
        """List available backup files"""
        backup_files = []
        for file in os.listdir('.'):
            if file.startswith('oauth_migration_backup_') and file.endswith('.json'):
                backup_files.append(file)
        
        backup_files.sort(reverse=True)  # Most recent first
        return backup_files
    
    def load_backup(self, backup_file: str) -> Dict:
        """Load backup data from file"""
        try:
            with open(backup_file, 'r') as f:
                backup_data = json.load(f)
            
            logger.info(f"📦 Loaded backup from {backup_file}")
            logger.info(f"   - Backup timestamp: {backup_data.get('timestamp')}")
            logger.info(f"   - Composio connections: {len(backup_data.get('composio_connections', []))}")
            logger.info(f"   - OAuth tokens: {len(backup_data.get('oauth_tokens', []))}")
            
            return backup_data
            
        except Exception as e:
            logger.error(f"❌ Failed to load backup: {e}")
            raise
    
    def restore_composio_connections(self, backup_data: Dict) -> int:
        """Restore composio_connections table from backup"""
        logger.info("🔄 Restoring composio_connections table...")
        
        try:
            composio_records = backup_data.get('composio_connections', [])
            
            if not composio_records:
                logger.info("ℹ️ No composio_connections to restore")
                return 0
            
            # Clear existing composio_connections
            logger.info("🗑️ Clearing existing composio_connections...")
            self.supabase.table('composio_connections').delete().neq('id', '00000000-0000-0000-0000-000000000000').execute()
            
            # Restore records
            restored_count = 0
            for record in composio_records:
                try:
                    # Remove any auto-generated fields that might cause conflicts
                    clean_record = {k: v for k, v in record.items() if k not in ['id']}
                    
                    self.supabase.table('composio_connections').insert(clean_record).execute()
                    restored_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Failed to restore composio_connections record: {e}")
                    continue
            
            logger.info(f"✅ Restored {restored_count} composio_connections records")
            return restored_count
            
        except Exception as e:
            logger.error(f"❌ Failed to restore composio_connections: {e}")
            raise
    
    def remove_migrated_oauth_tokens(self) -> int:
        """Remove OAuth tokens that were created during migration"""
        logger.info("🗑️ Removing migrated OAuth tokens...")
        
        try:
            # Delete oauth_tokens with composio_ provider prefix
            result = self.supabase.table('oauth_tokens').delete().like('provider', 'composio_%').execute()
            
            deleted_count = len(result.data) if result.data else 0
            logger.info(f"✅ Removed {deleted_count} migrated OAuth tokens")
            return deleted_count
            
        except Exception as e:
            logger.error(f"❌ Failed to remove migrated tokens: {e}")
            raise
    
    def restore_oauth_tokens(self, backup_data: Dict) -> int:
        """Restore original oauth_tokens table from backup"""
        logger.info("🔄 Restoring original oauth_tokens...")
        
        try:
            oauth_records = backup_data.get('oauth_tokens', [])
            
            if not oauth_records:
                logger.info("ℹ️ No original oauth_tokens to restore")
                return 0
            
            # Restore original oauth_tokens (non-composio ones)
            restored_count = 0
            for record in oauth_records:
                try:
                    # Skip composio tokens if they exist in backup
                    if record.get('provider', '').startswith('composio_'):
                        continue
                    
                    # Check if record already exists
                    existing = self.supabase.table('oauth_tokens').select("id").eq(
                        'employee_id', record['employee_id']
                    ).eq('provider', record['provider']).execute()
                    
                    if existing.data:
                        # Update existing record
                        clean_record = {k: v for k, v in record.items() if k not in ['id']}
                        self.supabase.table('oauth_tokens').update(clean_record).eq(
                            'employee_id', record['employee_id']
                        ).eq('provider', record['provider']).execute()
                    else:
                        # Insert new record
                        clean_record = {k: v for k, v in record.items() if k not in ['id']}
                        self.supabase.table('oauth_tokens').insert(clean_record).execute()
                    
                    restored_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Failed to restore oauth_tokens record: {e}")
                    continue
            
            logger.info(f"✅ Restored {restored_count} original oauth_tokens records")
            return restored_count
            
        except Exception as e:
            logger.error(f"❌ Failed to restore oauth_tokens: {e}")
            raise
    
    def verify_rollback(self, backup_data: Dict) -> bool:
        """Verify rollback was successful"""
        logger.info("🔍 Verifying rollback...")
        
        try:
            # Check composio_connections count
            composio_result = self.supabase.table('composio_connections').select("id").execute()
            composio_count = len(composio_result.data)
            expected_composio = len(backup_data.get('composio_connections', []))
            
            # Check oauth_tokens count (should not have composio_ providers)
            oauth_result = self.supabase.table('oauth_tokens').select("id").like('provider', 'composio_%').execute()
            migrated_tokens = len(oauth_result.data)
            
            logger.info(f"📊 Rollback verification:")
            logger.info(f"   - Composio connections: {composio_count} (expected: {expected_composio})")
            logger.info(f"   - Migrated OAuth tokens remaining: {migrated_tokens} (should be 0)")
            
            success = (composio_count == expected_composio) and (migrated_tokens == 0)
            
            if success:
                logger.info("✅ Rollback verification passed")
            else:
                logger.warning("⚠️ Rollback verification failed")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Rollback verification failed: {e}")
            return False

def main():
    """Main rollback function"""
    logger.info("🔄 Starting OAuth Migration Rollback...")
    
    try:
        rollback = OAuthRollback()
        
        # List available backups
        backups = rollback.list_backups()
        
        if not backups:
            logger.error("❌ No backup files found")
            return
        
        print("\n📦 Available backups:")
        for i, backup in enumerate(backups):
            print(f"   {i + 1}. {backup}")
        
        # Select backup
        while True:
            try:
                choice = input(f"\nSelect backup to restore (1-{len(backups)}): ").strip()
                backup_index = int(choice) - 1
                
                if 0 <= backup_index < len(backups):
                    selected_backup = backups[backup_index]
                    break
                else:
                    print("Invalid selection. Please try again.")
            except ValueError:
                print("Please enter a valid number.")
        
        # Load backup
        backup_data = rollback.load_backup(selected_backup)
        
        # Confirm rollback
        print(f"\n⚠️ About to rollback using backup: {selected_backup}")
        print("This will:")
        print("   1. Remove migrated OAuth tokens (composio_* providers)")
        print("   2. Restore original composio_connections table")
        print("   3. Restore original oauth_tokens table")
        
        confirm = input("\nContinue with rollback? (y/N): ").lower().strip()
        
        if confirm != 'y':
            logger.info("❌ Rollback cancelled by user")
            return
        
        # Perform rollback
        logger.info("🔄 Starting rollback process...")
        
        # Step 1: Remove migrated tokens
        removed_count = rollback.remove_migrated_oauth_tokens()
        
        # Step 2: Restore composio_connections
        restored_composio = rollback.restore_composio_connections(backup_data)
        
        # Step 3: Restore original oauth_tokens
        restored_oauth = rollback.restore_oauth_tokens(backup_data)
        
        # Step 4: Verify rollback
        if rollback.verify_rollback(backup_data):
            logger.info("🎉 Rollback completed successfully!")
            print(f"\n✅ Rollback summary:")
            print(f"   - Removed migrated tokens: {removed_count}")
            print(f"   - Restored composio_connections: {restored_composio}")
            print(f"   - Restored oauth_tokens: {restored_oauth}")
            print("\n📝 Next steps:")
            print("   1. Revert composio-service code to use composio_connections")
            print("   2. Test the reverted service")
            print("   3. Investigate migration issues if needed")
        else:
            logger.error("❌ Rollback verification failed")
            
    except Exception as e:
        logger.error(f"💥 Rollback failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
