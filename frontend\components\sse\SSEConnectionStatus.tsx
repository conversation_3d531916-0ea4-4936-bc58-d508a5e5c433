// frontend/components/sse/SSEConnectionStatus.tsx
// Component for displaying SSE connection status and health

import React, { useState } from 'react';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw,
  Activity,
  Clock,
  Zap
} from 'lucide-react';
import { useSSEConnection, SSEConnectionConfig } from '../../hooks/useSSEConnection';

interface SSEConnectionStatusProps {
  config: SSEConnectionConfig;
  showDetails?: boolean;
  showMessages?: boolean;
  className?: string;
}

export const SSEConnectionStatus: React.FC<SSEConnectionStatusProps> = ({
  config,
  showDetails = false,
  showMessages = false,
  className = ''
}) => {
  const { state, messages, reconnect, clearMessages } = useSSEConnection(config);
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  const getStatusColor = () => {
    if (state.connected) return 'text-green-500';
    if (state.connecting) return 'text-yellow-500';
    if (state.error) return 'text-red-500';
    return 'text-gray-500';
  };

  const getStatusIcon = () => {
    if (state.connected) return <CheckCircle className="w-4 h-4" />;
    if (state.connecting) return <Loader2 className="w-4 h-4 animate-spin" />;
    if (state.error) return <AlertCircle className="w-4 h-4" />;
    return <WifiOff className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (state.connected) return 'Connected';
    if (state.connecting) return 'Connecting...';
    if (state.error) return 'Disconnected';
    return 'Not Connected';
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    if (score >= 40) return 'text-orange-500';
    return 'text-red-500';
  };

  const formatTimestamp = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleTimeString();
  };

  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`${getStatusColor()}`}>
              {getStatusIcon()}
            </div>
            <div>
              <h3 className="text-sm font-medium text-white">SSE Connection</h3>
              <p className={`text-xs ${getStatusColor()}`}>
                {getStatusText()}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {state.healthScore > 0 && (
              <div className="text-right">
                <div className={`text-sm font-medium ${getHealthScoreColor(state.healthScore)}`}>
                  {state.healthScore}/100
                </div>
                <div className="text-xs text-gray-400">Health</div>
              </div>
            )}
            
            <button
              onClick={reconnect}
              disabled={state.connecting}
              className="p-2 text-gray-400 hover:text-white transition-colors disabled:opacity-50"
              title="Reconnect"
            >
              <RefreshCw className={`w-4 h-4 ${state.connecting ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <div className="p-3 bg-red-900/20 border-b border-gray-700">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-300">{state.error}</span>
          </div>
        </div>
      )}

      {/* Details */}
      {showDetails && (
        <div className="p-4 space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-400">Last Connected</div>
              <div className="text-white">{formatTimestamp(state.lastConnected)}</div>
            </div>
            <div>
              <div className="text-gray-400">Reconnect Attempts</div>
              <div className="text-white">{state.reconnectAttempts}</div>
            </div>
          </div>

          {/* Health Score Bar */}
          {state.healthScore > 0 && (
            <div>
              <div className="flex items-center justify-between text-sm mb-1">
                <span className="text-gray-400">Connection Health</span>
                <span className={getHealthScoreColor(state.healthScore)}>
                  {state.healthScore}%
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    state.healthScore >= 80 ? 'bg-green-500' :
                    state.healthScore >= 60 ? 'bg-yellow-500' :
                    state.healthScore >= 40 ? 'bg-orange-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.max(state.healthScore, 5)}%` }}
                />
              </div>
            </div>
          )}

          {/* Diagnostics Toggle */}
          <button
            onClick={() => setShowDiagnostics(!showDiagnostics)}
            className="w-full text-left text-sm text-blue-400 hover:text-blue-300 transition-colors"
          >
            {showDiagnostics ? 'Hide' : 'Show'} Diagnostics
          </button>

          {/* Diagnostics */}
          {showDiagnostics && (
            <div className="bg-gray-900 rounded p-3 text-xs space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">URL:</span>
                <span className="text-white font-mono">{config.url}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Max Retries:</span>
                <span className="text-white">{config.maxReconnectAttempts || 10}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Reconnect Interval:</span>
                <span className="text-white">{config.reconnectInterval || 5000}ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Heartbeat Timeout:</span>
                <span className="text-white">{config.heartbeatTimeout || 60000}ms</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Messages */}
      {showMessages && (
        <div className="border-t border-gray-700">
          <div className="p-3 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-400">
                Recent Messages ({messages.length})
              </span>
            </div>
            {messages.length > 0 && (
              <button
                onClick={clearMessages}
                className="text-xs text-gray-400 hover:text-white transition-colors"
              >
                Clear
              </button>
            )}
          </div>
          
          <div className="max-h-48 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="p-3 text-center text-gray-500 text-sm">
                No messages received
              </div>
            ) : (
              <div className="space-y-1">
                {messages.slice(-10).map((message, index) => (
                  <div key={index} className="px-3 py-2 border-t border-gray-700 first:border-t-0">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-400">{message.type}</span>
                      <span className="text-gray-500 flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{formatTimestamp(message.timestamp)}</span>
                      </span>
                    </div>
                    <div className="mt-1 text-sm text-white font-mono bg-gray-900 rounded p-2 overflow-x-auto">
                      {typeof message.data === 'string' 
                        ? message.data 
                        : JSON.stringify(message.data, null, 2)
                      }
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// SSE Context for global connection management
export const SSEContext = React.createContext<{
  isConnected: boolean;
  healthScore: number;
  reconnect: () => void;
} | null>(null);

export const useSSEContext = () => {
  const context = React.useContext(SSEContext);
  if (!context) {
    throw new Error('useSSEContext must be used within SSEProvider');
  }
  return context;
};

export default SSEConnectionStatus;
