-- ===========================================
-- REMEMBER ME TOKENS TABLE CREATION
-- Secure persistent authentication tokens for "Stay signed in" functionality
-- ===========================================

-- Create remember_me_tokens table for long-term authentication
CREATE TABLE IF NOT EXISTS remember_me_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID NOT NULL,
  workspace_id UUID NOT NULL,
  token_hash VARCHAR(255) NOT NULL UNIQUE,
  selector VARCHAR(64) NOT NULL UNIQUE,
  device_fingerprint JSONB DEFAULT '{}',
  user_agent TEXT,
  ip_address INET,
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_revoked BOOLEAN DEFAULT false,
  revoked_at TIMESTAMP WITH TIME ZONE,
  revoked_reason VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_employee_id ON remember_me_tokens(employee_id);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_workspace_id ON remember_me_tokens(workspace_id);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_selector ON remember_me_tokens(selector);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_token_hash ON remember_me_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_expires_at ON remember_me_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_last_used ON remember_me_tokens(last_used_at DESC);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_active ON remember_me_tokens(employee_id, is_revoked, expires_at) WHERE is_revoked = false;

-- Add foreign key constraints
ALTER TABLE remember_me_tokens 
ADD CONSTRAINT fk_remember_me_tokens_employee_id 
FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE;

-- Add check constraints for data validation
ALTER TABLE remember_me_tokens 
ADD CONSTRAINT chk_remember_me_tokens_selector_not_empty 
CHECK (length(trim(selector)) > 0);

ALTER TABLE remember_me_tokens 
ADD CONSTRAINT chk_remember_me_tokens_token_hash_not_empty 
CHECK (length(trim(token_hash)) > 0);

ALTER TABLE remember_me_tokens 
ADD CONSTRAINT chk_remember_me_tokens_expires_future 
CHECK (expires_at > created_at);

-- Create RLS (Row Level Security) policies for multi-tenant isolation
ALTER TABLE remember_me_tokens ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access remember me tokens from their own workspace
CREATE POLICY remember_me_tokens_workspace_isolation ON remember_me_tokens
  FOR ALL
  USING (
    workspace_id IN (
      SELECT workspace_id 
      FROM employees 
      WHERE id = auth.uid()
    )
  );

-- Policy: Service role can access all remember me tokens
CREATE POLICY remember_me_tokens_service_access ON remember_me_tokens
  FOR ALL
  TO service_role
  USING (true);

-- ===========================================
-- REMEMBER ME TOKEN MANAGEMENT FUNCTIONS
-- ===========================================

-- Function to clean up expired remember me tokens
CREATE OR REPLACE FUNCTION cleanup_expired_remember_me_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM remember_me_tokens 
  WHERE expires_at < NOW() OR is_revoked = true;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Update statistics after cleanup
  ANALYZE remember_me_tokens;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to revoke all remember me tokens for an employee
CREATE OR REPLACE FUNCTION revoke_employee_remember_me_tokens(p_employee_id UUID, p_reason VARCHAR(100) DEFAULT 'manual_revocation')
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER;
BEGIN
  UPDATE remember_me_tokens 
  SET 
    is_revoked = true,
    revoked_at = NOW(),
    revoked_reason = p_reason,
    updated_at = NOW()
  WHERE 
    employee_id = p_employee_id 
    AND is_revoked = false 
    AND expires_at > NOW();
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to revoke remember me tokens by device fingerprint
CREATE OR REPLACE FUNCTION revoke_remember_me_tokens_by_device(p_employee_id UUID, p_device_fingerprint JSONB, p_reason VARCHAR(100) DEFAULT 'device_revocation')
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER;
BEGIN
  UPDATE remember_me_tokens 
  SET 
    is_revoked = true,
    revoked_at = NOW(),
    revoked_reason = p_reason,
    updated_at = NOW()
  WHERE 
    employee_id = p_employee_id 
    AND device_fingerprint = p_device_fingerprint
    AND is_revoked = false 
    AND expires_at > NOW();
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===========================================
-- REMEMBER ME TOKEN VIEWS AND MONITORING
-- ===========================================

-- Create a view for active remember me tokens
CREATE OR REPLACE VIEW active_remember_me_tokens AS
SELECT 
  rmt.id,
  rmt.employee_id,
  rmt.workspace_id,
  rmt.selector,
  rmt.device_fingerprint,
  rmt.user_agent,
  rmt.ip_address,
  rmt.last_used_at,
  rmt.expires_at,
  rmt.created_at,
  e.email as employee_email,
  e.name as employee_name,
  w.name as workspace_name,
  w.slug as workspace_slug,
  EXTRACT(EPOCH FROM (rmt.expires_at - NOW())) / 86400 as days_until_expiry
FROM remember_me_tokens rmt
JOIN employees e ON rmt.employee_id = e.id
JOIN workspaces w ON rmt.workspace_id = w.id
WHERE 
  rmt.is_revoked = false 
  AND rmt.expires_at > NOW()
ORDER BY rmt.last_used_at DESC;

-- Create a view for remember me token statistics
CREATE OR REPLACE VIEW remember_me_token_stats AS
SELECT 
  workspace_id,
  COUNT(*) as total_tokens,
  COUNT(*) FILTER (WHERE is_revoked = false AND expires_at > NOW()) as active_tokens,
  COUNT(*) FILTER (WHERE is_revoked = true) as revoked_tokens,
  COUNT(*) FILTER (WHERE expires_at <= NOW()) as expired_tokens,
  COUNT(DISTINCT employee_id) as unique_employees,
  AVG(EXTRACT(EPOCH FROM (expires_at - created_at)) / 86400) as avg_token_duration_days,
  MIN(created_at) as first_token_created,
  MAX(last_used_at) as last_token_used
FROM remember_me_tokens
GROUP BY workspace_id;

-- ===========================================
-- TRIGGERS AND AUTOMATION
-- ===========================================

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_remember_me_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_remember_me_tokens_updated_at 
BEFORE UPDATE ON remember_me_tokens 
FOR EACH ROW EXECUTE FUNCTION update_remember_me_tokens_updated_at();

-- ===========================================
-- PERMISSIONS
-- ===========================================

-- Grant appropriate permissions
GRANT SELECT ON remember_me_tokens TO authenticated;
GRANT SELECT ON active_remember_me_tokens TO authenticated;
GRANT SELECT ON remember_me_token_stats TO authenticated;
GRANT ALL ON remember_me_tokens TO service_role;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION cleanup_expired_remember_me_tokens() TO service_role;
GRANT EXECUTE ON FUNCTION revoke_employee_remember_me_tokens(UUID, VARCHAR) TO service_role;
GRANT EXECUTE ON FUNCTION revoke_remember_me_tokens_by_device(UUID, JSONB, VARCHAR) TO service_role;

-- ===========================================
-- SCHEDULED CLEANUP (OPTIONAL - REQUIRES pg_cron EXTENSION)
-- ===========================================

-- Create a scheduled job to run cleanup every 6 hours (requires pg_cron extension)
-- Uncomment the following line if pg_cron is available:
-- SELECT cron.schedule('remember-me-cleanup', '0 */6 * * *', 'SELECT cleanup_expired_remember_me_tokens();');

-- ===========================================
-- INITIAL DATA VALIDATION
-- ===========================================

-- Validate the table was created successfully
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'remember_me_tokens') THEN
        RAISE NOTICE 'Remember me tokens table created successfully';
    ELSE
        RAISE EXCEPTION 'Failed to create remember me tokens table';
    END IF;
END $$;
