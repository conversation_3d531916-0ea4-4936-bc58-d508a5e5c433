// frontend/components/auth/AuthContext.tsx
// Authentication context for managing user state

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../../utils/api';

interface Employee {
  id: string;
  email: string;
  name: string;
  role: string;
  workspace: {
    id: string;
    name: string;
    slug: string;
  };
}

interface AuthContextType {
  employee: Employee | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string, workspaceSlug: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  revokeAllRememberMeTokens: () => Promise<boolean>;
  rotateRememberMeToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!employee && apiService.isAuthenticated();

  // Check for existing authentication on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      if (apiService.isAuthenticated()) {
        const user = await apiService.getCurrentUser();
        setEmployee(user);
      } else {
        // Try to validate remember me token if no active session
        const rememberMeValid = await apiService.validateRememberMeToken();
        if (rememberMeValid) {
          // Remember me token was valid, get user data
          const user = await apiService.getCurrentUser();
          setEmployee(user);
        }
      }
    } catch (error: unknown) {
      // Enhanced error handling for token refresh scenarios
      if (error instanceof Error) {
        // If it's a token-related error, try to handle it gracefully
        if (error.message.includes('token') || error.message.includes('authentication')) {
          console.log('Authentication token issue detected, clearing session');
        }
      }
      // Clear invalid token and reset state
      await apiService.logout();
      setEmployee(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string, workspaceSlug: string, rememberMe: boolean = false) => {
    setIsLoading(true);
    try {
      const result = await apiService.login(email, password, workspaceSlug, rememberMe);
      setEmployee(result.employee);
    } catch (error: unknown) {
      // Enhanced error handling for better user feedback
      if (error instanceof Error) {
        // Check for specific error patterns
        if (error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        } else if (error.message.includes('authentication_unavailable')) {
          throw new Error('Authentication service is temporarily unavailable. Please try again in a few moments.');
        } else if (error.message.includes('Invalid credentials')) {
          throw new Error('Invalid email or password. Please check your credentials and try again.');
        } else if (error.message.includes('Account not activated')) {
          throw new Error('Your account is not yet activated. Please check your email for activation instructions.');
        }
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await apiService.logout();
      setEmployee(null);
    } catch (error: unknown) {
      // Even if API logout fails, clear local state
      setEmployee(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      if (apiService.isAuthenticated()) {
        const user = await apiService.getCurrentUser();
        setEmployee(user);
      }
    } catch (error: unknown) {
      // If refresh fails, user might need to re-authenticate
      await logout();
    }
  };

  const refreshToken = async () => {
    try {
      if (apiService.isAuthenticated()) {
        // This will automatically refresh the token if needed
        await apiService.getCurrentUser();
        return true;
      }
      return false;
    } catch (error: unknown) {
      console.warn('Token refresh failed:', error);
      await logout();
      return false;
    }
  };

  const revokeAllRememberMeTokens = async (): Promise<boolean> => {
    try {
      return await apiService.revokeAllRememberMeTokens();
    } catch (error: unknown) {
      console.warn('Failed to revoke all remember me tokens:', error);
      return false;
    }
  };

  const rotateRememberMeToken = async (): Promise<boolean> => {
    try {
      return await apiService.rotateRememberMeToken();
    } catch (error: unknown) {
      console.warn('Failed to rotate remember me token:', error);
      return false;
    }
  };

  const value: AuthContextType = {
    employee,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshUser,
    refreshToken,
    revokeAllRememberMeTokens,
    rotateRememberMeToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
