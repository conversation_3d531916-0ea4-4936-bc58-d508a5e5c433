#!/usr/bin/env python3
"""
OAuth Storage Migration Script
Migrates data from composio_connections table to oauth_tokens table
for standardized OAuth token storage across all services.
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional
from supabase import create_client, Client
from cryptography.fernet import Fernet
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Setup logging with UTF-8 encoding for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OAuthMigration:
    def __init__(self):
        """Initialize migration with Supabase connection and encryption"""
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.encryption_key = os.getenv("ENCRYPTION_KEY")
        
        if not all([self.supabase_url, self.supabase_key, self.encryption_key]):
            raise ValueError("Missing required environment variables: SUPABASE_URL, SUPABASE_KEY, ENCRYPTION_KEY")
        
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        self.cipher_suite = Fernet(self.encryption_key.encode())
        
        logger.info("🔧 Migration initialized successfully")
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt data using Fernet encryption"""
        try:
            if encrypted_data is None:
                return None
            return self.cipher_suite.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            raise
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt data using Fernet encryption"""
        try:
            return self.cipher_suite.encrypt(data.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to encrypt data: {e}")
            raise
    
    def backup_tables(self) -> Dict[str, List]:
        """Create backup of existing data"""
        logger.info("📦 Creating backup of existing tables...")
        
        backup = {
            'composio_connections': [],
            'oauth_tokens': [],
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        try:
            # Backup composio_connections
            composio_result = self.supabase.table('composio_connections').select("*").execute()
            backup['composio_connections'] = composio_result.data
            logger.info(f"✅ Backed up {len(composio_result.data)} composio_connections records")
            
            # Backup oauth_tokens
            oauth_result = self.supabase.table('oauth_tokens').select("*").execute()
            backup['oauth_tokens'] = oauth_result.data
            logger.info(f"✅ Backed up {len(oauth_result.data)} oauth_tokens records")
            
            # Save backup to file
            backup_filename = f"oauth_migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_filename, 'w') as f:
                json.dump(backup, f, indent=2, default=str)
            
            logger.info(f"💾 Backup saved to {backup_filename}")
            return backup
            
        except Exception as e:
            logger.error(f"❌ Backup failed: {e}")
            raise
    
    def analyze_data(self) -> Dict:
        """Analyze existing data to understand migration scope"""
        logger.info("🔍 Analyzing existing data...")
        
        try:
            # Get composio_connections data
            composio_result = self.supabase.table('composio_connections').select("*").execute()
            composio_data = composio_result.data
            
            # Get oauth_tokens data
            oauth_result = self.supabase.table('oauth_tokens').select("*").execute()
            oauth_data = oauth_result.data
            
            analysis = {
                'composio_connections_count': len(composio_data),
                'oauth_tokens_count': len(oauth_data),
                'unique_employees_composio': len(set(record['employee_id'] for record in composio_data)),
                'unique_employees_oauth': len(set(record['employee_id'] for record in oauth_data)),
                'app_types': list(set(record['app_name'] for record in composio_data)),
                'oauth_providers': list(set(record['provider'] for record in oauth_data))
            }
            
            logger.info(f"📊 Analysis complete:")
            logger.info(f"   - Composio connections: {analysis['composio_connections_count']}")
            logger.info(f"   - OAuth tokens: {analysis['oauth_tokens_count']}")
            logger.info(f"   - Unique employees (composio): {analysis['unique_employees_composio']}")
            logger.info(f"   - Unique employees (oauth): {analysis['unique_employees_oauth']}")
            logger.info(f"   - App types: {analysis['app_types']}")
            logger.info(f"   - OAuth providers: {analysis['oauth_providers']}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            raise
    
    def migrate_composio_to_oauth(self) -> int:
        """Migrate composio_connections data to oauth_tokens table"""
        logger.info("🔄 Starting migration from composio_connections to oauth_tokens...")
        
        try:
            # Get all composio_connections
            result = self.supabase.table('composio_connections').select("*").execute()
            composio_records = result.data
            
            migrated_count = 0
            skipped_count = 0
            
            for record in composio_records:
                try:
                    # Check if oauth_tokens record already exists
                    existing = self.supabase.table('oauth_tokens').select("*").eq(
                        'employee_id', record['employee_id']
                    ).eq('provider', f"composio_{record['app_name']}").execute()
                    
                    if existing.data:
                        logger.info(f"⏭️ Skipping {record['employee_id']}/{record['app_name']} - already exists in oauth_tokens")
                        skipped_count += 1
                        continue
                    
                    # Decrypt tokens from composio_connections (handle None values)
                    access_token = None
                    if record.get('access_token'):
                        access_token = self.decrypt_data(record['access_token'])

                    refresh_token = None
                    if record.get('refresh_token'):
                        refresh_token = self.decrypt_data(record['refresh_token'])

                    # Skip records with no access token
                    if not access_token:
                        logger.warning(f"⏭️ Skipping {record['employee_id']}/{record['app_name']} - no access token")
                        skipped_count += 1
                        continue
                    
                    # Create oauth_tokens record
                    oauth_record = {
                        'employee_id': record['employee_id'],
                        'provider': f"composio_{record['app_name']}",  # Prefix to distinguish from direct OAuth
                        'access_token': self.encrypt_data(access_token),
                        'refresh_token': self.encrypt_data(refresh_token) if refresh_token else None,
                        'expires_at': record.get('expires_at'),
                        'scopes': record.get('scopes', []),
                        'created_at': record.get('created_at'),
                        'updated_at': datetime.now(timezone.utc).isoformat(),
                        'metadata': {
                            'migrated_from': 'composio_connections',
                            'original_app_name': record['app_name'],
                            'original_status': record.get('status'),
                            'user_info': record.get('user_info'),
                            'composio_metadata': record.get('metadata', {})
                        }
                    }
                    
                    # Insert into oauth_tokens
                    self.supabase.table('oauth_tokens').insert(oauth_record).execute()
                    
                    logger.info(f"✅ Migrated {record['employee_id']}/{record['app_name']}")
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Failed to migrate record {record.get('id', 'unknown')}: {e}")
                    continue
            
            logger.info(f"🎉 Migration complete: {migrated_count} migrated, {skipped_count} skipped")
            return migrated_count
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            raise
    
    def verify_migration(self) -> bool:
        """Verify migration was successful"""
        logger.info("🔍 Verifying migration...")
        
        try:
            # Get counts
            composio_result = self.supabase.table('composio_connections').select("id").execute()
            oauth_result = self.supabase.table('oauth_tokens').select("id").like('provider', 'composio_%').execute()
            
            composio_count = len(composio_result.data)
            migrated_count = len(oauth_result.data)
            
            logger.info(f"📊 Verification results:")
            logger.info(f"   - Original composio_connections: {composio_count}")
            logger.info(f"   - Migrated oauth_tokens (composio_*): {migrated_count}")
            
            if migrated_count >= composio_count:
                logger.info("✅ Migration verification passed")
                return True
            else:
                logger.warning("⚠️ Migration verification failed - some records may be missing")
                return False
                
        except Exception as e:
            logger.error(f"❌ Verification failed: {e}")
            return False

def main():
    """Main migration function"""
    logger.info("🚀 Starting OAuth Storage Migration...")
    
    try:
        migration = OAuthMigration()
        
        # Step 1: Analyze current data
        analysis = migration.analyze_data()
        
        # Step 2: Create backup
        backup = migration.backup_tables()
        
        # Step 3: Confirm migration
        if analysis['composio_connections_count'] == 0:
            logger.info("ℹ️ No composio_connections to migrate")
            return
        
        print(f"\n⚠️ About to migrate {analysis['composio_connections_count']} composio_connections to oauth_tokens")
        confirm = input("Continue? (y/N): ").lower().strip()
        
        if confirm != 'y':
            logger.info("❌ Migration cancelled by user")
            return
        
        # Step 4: Perform migration
        migrated_count = migration.migrate_composio_to_oauth()
        
        # Step 5: Verify migration
        if migration.verify_migration():
            logger.info("🎉 Migration completed successfully!")
            print(f"\n✅ Successfully migrated {migrated_count} records")
            print("📝 Next steps:")
            print("   1. Update composio-service to use oauth_tokens table")
            print("   2. Test the updated service")
            print("   3. Remove composio_connections table when confident")
        else:
            logger.error("❌ Migration verification failed")
            
    except Exception as e:
        logger.error(f"💥 Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
