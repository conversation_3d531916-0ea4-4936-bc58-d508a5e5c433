# auth-service/oauth_flow.py
# Google OAuth Flow Implementation

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import secrets
import sys
from urllib.parse import urlencode, parse_qs
from fastapi import HTTPException, status
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from supabase import Client
import os
import httpx

# Add shared directory to path for environment utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))
try:
    from environment import env_config
except ImportError:
    env_config = None

class GoogleOAuthFlow:
    def __init__(self, supabase: Client):
        self.supabase = supabase
        self.client_id = os.getenv("GOOGLE_CLIENT_ID")
        self.client_secret = os.getenv("GOOGLE_CLIENT_SECRET")

        # Use environment-aware redirect URI configuration
        if env_config:
            self.redirect_uri = env_config.get_google_redirect_uri()
            print(f" Using environment-aware redirect URI: {self.redirect_uri}")
        else:
            self.redirect_uri = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:8000/oauth/google/callback")
            print(f" Using fallback redirect URI: {self.redirect_uri}")
        
        # OAuth scopes for different services
        self.scopes = {
            "drive": [
                "https://www.googleapis.com/auth/drive.readonly",
                "https://www.googleapis.com/auth/drive.metadata.readonly"
            ],
            "gmail": [
                "https://www.googleapis.com/auth/gmail.readonly",
                "https://www.googleapis.com/auth/gmail.send"
            ],
            "calendar": [
                "https://www.googleapis.com/auth/calendar.readonly",
                "https://www.googleapis.com/auth/calendar.events"
            ]
        }

        # Basic scopes that are commonly available and don't require special approval
        self.basic_scopes = [
            "https://www.googleapis.com/auth/userinfo.email",
            "https://www.googleapis.com/auth/userinfo.profile"
        ]

        # Default scopes - start with basic scopes to avoid invalid_scope errors
        # Full scopes can be enabled after proper Google Cloud Console configuration
        self.default_scopes = self.basic_scopes

        # Full scopes for production use (requires Google Cloud Console configuration)
        self.full_scopes = (
            self.scopes["drive"] +
            self.scopes["gmail"] +
            self.scopes["calendar"]
        )

    def enable_full_scopes(self):
        """Enable full scopes after Google Cloud Console is properly configured"""
        self.default_scopes = self.full_scopes
        print(" Full OAuth scopes enabled. Ensure these are configured in Google Cloud Console:")
        for scope in self.full_scopes:
            print(f"   - {scope}")

    def get_available_scopes(self):
        """Get information about available scopes"""
        return {
            "basic_scopes": self.basic_scopes,
            "full_scopes": self.full_scopes,
            "current_default": self.default_scopes,
            "note": "Use enable_full_scopes() after configuring Google Cloud Console"
        }
    
    async def initiate_oauth(
        self, 
        employee_id: str, 
        services: List[str] = None,
        redirect_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """Initiate Google OAuth flow for employee"""
        
        try:
            # Determine scopes based on requested services
            if services:
                requested_scopes = []
                for service in services:
                    if service in self.scopes:
                        requested_scopes.extend(self.scopes[service])
                scopes = requested_scopes or self.default_scopes
            else:
                scopes = self.default_scopes
            
            # Generate secure state token
            state_token = secrets.token_urlsafe(32)
            expires_at = datetime.utcnow() + timedelta(minutes=10)  # OAuth state expires quickly
            
            # Store OAuth state with requested scopes
            oauth_state_data = {
                "employee_id": employee_id,
                "state_token": state_token,
                "provider": "google",
                "redirect_url": redirect_url,
                "expires_at": expires_at.isoformat()
            }

            # AGGRESSIVE SCHEMA CACHE REFRESH: Always try to refresh schema first
            print(" Proactively refreshing schema cache before insertion...")
            try:
                # Force schema refresh with a simple query that includes requested_scopes
                refresh_result = self.supabase.table('oauth_states').select('id,requested_scopes').limit(1).execute()
                print(f" Schema cache refreshed successfully")
            except Exception as refresh_error:
                print(f"  Schema refresh query failed: {refresh_error}")

            # Now try to include requested_scopes
            try:
                oauth_state_data["requested_scopes"] = scopes
                response = self.supabase.table('oauth_states').insert(oauth_state_data).execute()
                print(f" OAuth state stored successfully with requested_scopes: {scopes}")
            except Exception as e:
                # If still failing, try one more aggressive refresh
                if "requested_scopes" in str(e) or "PGRST204" in str(e):
                    print(f"  Still getting schema cache issue, trying aggressive refresh: {e}")

                    try:
                        # Multiple refresh attempts with different queries
                        print(" Attempting multiple schema refresh queries...")
                        self.supabase.table('oauth_states').select('*').limit(1).execute()
                        self.supabase.table('oauth_states').select('requested_scopes').limit(1).execute()

                        # Final retry with requested_scopes
                        print(" Final retry with requested_scopes...")
                        response = self.supabase.table('oauth_states').insert(oauth_state_data).execute()
                        print(f" OAuth state stored successfully after aggressive refresh with requested_scopes: {scopes}")

                    except Exception as final_error:
                        print(f" All cache refresh attempts failed, falling back: {final_error}")
                        oauth_state_data.pop("requested_scopes", None)
                        response = self.supabase.table('oauth_states').insert(oauth_state_data).execute()
                        print(f"  OAuth state stored without requested_scopes (final fallback)")
                else:
                    raise e
            
            # Create OAuth flow
            flow = Flow.from_client_config({
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            }, scopes=scopes)
            
            flow.redirect_uri = self.redirect_uri
            
            # Generate authorization URL
            authorization_url, _ = flow.authorization_url(
                access_type='offline',  # Get refresh token
                include_granted_scopes='true',
                state=state_token,
                prompt='consent'  # Force consent to get refresh token
            )
            
            return {
                "authorization_url": authorization_url,
                "state_token": state_token,
                "scopes": scopes,
                "expires_at": expires_at.isoformat(),
                "message": "Visit the authorization URL to connect your Google account"
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initiate OAuth: {str(e)}"
            )
    
    async def handle_callback(
        self,
        code: str,
        state: str,
        error: Optional[str] = None,
        scope: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle Google OAuth callback"""
        
        if error:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"OAuth error: {error}"
            )
        
        try:
            # Try to verify state token from database
            oauth_state = None
            employee_id = None
            requested_scopes = self.default_scopes

            print(f" Processing callback with state: {state[:20]}...")

            try:
                result = self.supabase.table('oauth_states').select("*").eq(
                    "state_token", state
                ).single().execute()

                if result.data:
                    oauth_state = result.data
                    employee_id = oauth_state["employee_id"]

                    # Check if state is expired
                    expires_at = datetime.fromisoformat(oauth_state["expires_at"].replace('Z', '+00:00'))
                    if datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="OAuth state expired"
                        )

                    employee_id = oauth_state["employee_id"]

                    # CRITICAL FIX: Use granted scopes from callback URL instead of database
                    # This completely bypasses the database schema cache issue

                    if scope:
                        # Use the scopes that Google actually granted (from callback URL)
                        requested_scopes = scope.split()
                        print(f" Using granted scopes from callback URL: {requested_scopes}")
                        print(f"   - This bypasses the database schema cache issue")
                    else:
                        # Fallback to stored scopes (will be default scopes due to missing column)
                        requested_scopes = oauth_state.get("requested_scopes", self.default_scopes)
                        if requested_scopes is None:
                            requested_scopes = self.default_scopes
                        print(f"  No scopes in callback URL, using fallback: {requested_scopes}")

                    print(f" OAuth state verified for employee: {employee_id[:8]}...")
                    print(f"   - Final scopes to use: {requested_scopes}")
                    print(f"   - Scope count: {len(requested_scopes)}")

            except Exception as db_error:
                # OAuth state not found in database (likely from test endpoint)
                print(f"  OAuth state not found in database (test mode?): {db_error}")
                # For test flows, we'll use default scopes and skip employee storage
                oauth_state = None
                employee_id = None
                requested_scopes = self.default_scopes

            # Exchange code for tokens - use flexible scope handling with detailed error logging
            print(f" Attempting token exchange with redirect_uri: {self.redirect_uri}")
            print(f" Using client_id: {self.client_id[:20]}...")
            print(f" Authorization code length: {len(code)}")

            try:
                # First try with originally requested scopes
                flow = Flow.from_client_config({
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "redirect_uris": [self.redirect_uri]
                    }
                }, scopes=requested_scopes)

                flow.redirect_uri = self.redirect_uri
                print(f" Flow redirect_uri set to: {flow.redirect_uri}")

                # Add detailed error handling for token exchange
                try:
                    flow.fetch_token(code=code)
                    print(" Token exchange successful")
                except Exception as token_error:
                    print(f" Token exchange failed: {str(token_error)}")
                    print(f" Error type: {type(token_error).__name__}")

                    # Check for specific OAuth errors
                    error_str = str(token_error).lower()
                    if "invalid_grant" in error_str:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"OAuth authorization code is invalid or expired. This usually means the code was used already or took too long to exchange. Please try the OAuth flow again. Error: {str(token_error)}"
                        )
                    elif "redirect_uri_mismatch" in error_str:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Redirect URI mismatch. Expected: {self.redirect_uri}. Please ensure your Google OAuth app is configured with the correct redirect URI. Error: {str(token_error)}"
                        )
                    elif "invalid_client" in error_str:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Invalid OAuth client configuration. Please check your Google Client ID and Secret. Error: {str(token_error)}"
                        )
                    elif "invalid_scope" in error_str:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Invalid OAuth scopes requested. The requested scopes are not enabled in your Google Cloud Console OAuth application. Please configure the required scopes in Google Cloud Console > APIs & Services > Credentials. Error: {str(token_error)}"
                        )
                    else:
                        # Re-raise the original error for other cases
                        raise token_error

            except HTTPException:
                # Re-raise HTTP exceptions (our custom errors)
                raise
            except Exception as scope_error:
                # If scope mismatch, try with the scopes that were actually granted
                if "Scope has changed" in str(scope_error):
                    print(f"  Scope mismatch detected: {scope_error}")

                    # Extract the actual granted scopes from the error message
                    error_str = str(scope_error)
                    if 'to "' in error_str:
                        # Parse the granted scopes from the error message
                        granted_scopes_str = error_str.split('to "')[1].split('"')[0]
                        granted_scopes = granted_scopes_str.split(' ')
                        print(f" Retrying with granted scopes: {granted_scopes}")

                        flow = Flow.from_client_config({
                            "web": {
                                "client_id": self.client_id,
                                "client_secret": self.client_secret,
                                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                                "token_uri": "https://oauth2.googleapis.com/token",
                                "redirect_uris": [self.redirect_uri]
                            }
                        }, scopes=granted_scopes)

                        flow.redirect_uri = self.redirect_uri
                        flow.fetch_token(code=code)
                        print(" Token exchange successful with granted scopes")
                    else:
                        print(" Could not parse granted scopes from error message")
                        raise scope_error
                else:
                    raise scope_error
            
            credentials = flow.credentials
            
            # Get user info
            user_info = await self._get_user_info(credentials.token)

            # Store tokens only if we have a valid employee_id (not in test mode)
            if employee_id:
                from auth import EmployeeAuth
                auth_system = EmployeeAuth(self.supabase)

                await auth_system.store_oauth_tokens(
                    employee_id=employee_id,
                    provider="google",
                    access_token=credentials.token,
                    refresh_token=credentials.refresh_token,
                    expires_at=credentials.expiry,
                    scopes=credentials.scopes
                )

                # Log the connection
                self.supabase.table('audit_logs').insert({
                    "employee_id": employee_id,
                    "action": "oauth_connected",
                    "details": {
                        "provider": "google",
                        "user_email": user_info.get("email"),
                        "scopes": credentials.scopes
                    }
                }).execute()

            # Clean up OAuth state if it exists
            if oauth_state and oauth_state.get("id"):
                self.supabase.table('oauth_states').delete().eq("id", oauth_state["id"]).execute()

            # Determine redirect URL
            redirect_url = "http://localhost:3000/app/settings"  # Default for test mode
            if oauth_state and oauth_state.get("redirect_url"):
                redirect_url = oauth_state["redirect_url"]

            return {
                "message": "Google account connected successfully" + (" (test mode)" if not employee_id else ""),
                "user_info": {
                    "email": user_info.get("email"),
                    "name": user_info.get("name"),
                    "picture": user_info.get("picture")
                },
                "scopes": credentials.scopes,
                "expires_at": credentials.expiry.isoformat() if credentials.expiry else None,
                "redirect_url": redirect_url,
                "test_mode": employee_id is None
            }
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"OAuth callback failed: {str(e)}"
            )
    
    async def refresh_tokens(self, employee_id: str) -> Dict[str, Any]:
        """Refresh Google OAuth tokens for employee"""
        
        try:
            # Get current tokens using EmployeeAuth
            from auth import EmployeeAuth
            auth_system = EmployeeAuth(self.supabase)
            
            tokens = await auth_system.get_oauth_tokens(employee_id, "google")
            
            if not tokens["refresh_token"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No refresh token available. User needs to re-authorize."
                )
            
            # Create credentials object
            credentials = Credentials(
                token=tokens["access_token"],
                refresh_token=tokens["refresh_token"],
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=tokens["scopes"]
            )
            
            # Refresh tokens
            credentials.refresh(Request())
            
            # Store new tokens using EmployeeAuth (which handles encryption and upsert)
            await auth_system.store_oauth_tokens(
                employee_id=employee_id,
                provider="google",
                access_token=credentials.token,
                refresh_token=credentials.refresh_token,
                expires_at=credentials.expiry,
                scopes=credentials.scopes
            )
            
            return {
                "message": "Tokens refreshed successfully",
                "expires_at": credentials.expiry.isoformat() if credentials.expiry else None
            }
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Token refresh failed: {str(e)}"
            )
    
    async def disconnect_oauth(self, employee_id: str) -> Dict[str, Any]:
        """Disconnect Google OAuth for employee"""
        
        try:
            # Get tokens to revoke them
            from auth import EmployeeAuth
            auth_system = EmployeeAuth(self.supabase)
            
            try:
                tokens = await auth_system.get_oauth_tokens(employee_id, "google")
                
                # Revoke tokens with Google
                async with httpx.AsyncClient() as client:
                    await client.post(
                        "https://oauth2.googleapis.com/revoke",
                        params={"token": tokens["access_token"]}
                    )
            except HTTPException:
                # Tokens might not exist, that's okay
                pass
            
            # Delete tokens from database
            self.supabase.table('oauth_tokens').delete().eq(
                "employee_id", employee_id
            ).eq("provider", "google").execute()
            
            # Log the disconnection
            self.supabase.table('audit_logs').insert({
                "employee_id": employee_id,
                "action": "oauth_disconnected",
                "details": {"provider": "google"}
            }).execute()
            
            return {
                "message": "Google account disconnected successfully"
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to disconnect OAuth: {str(e)}"
            )
    
    async def check_oauth_status(self, employee_id: str) -> Dict[str, Any]:
        """Check OAuth connection status for employee"""
        
        try:
            result = self.supabase.table('oauth_tokens').select("*").eq(
                "employee_id", employee_id
            ).eq("provider", "google").execute()
            
            if not result.data:
                return {
                    "connected": False,
                    "message": "No Google account connected"
                }
            
            token_data = result.data[0]
            expires_at = None
            if token_data["expires_at"]:
                expires_at = datetime.fromisoformat(token_data["expires_at"].replace('Z', '+00:00'))
                is_expired = datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at
            else:
                is_expired = False
            
            # Get user info if tokens are valid
            user_info = None
            if not is_expired:
                try:
                    from auth import EmployeeAuth
                    auth_system = EmployeeAuth(self.supabase)
                    tokens = await auth_system.get_oauth_tokens(employee_id, "google")
                    user_info = await self._get_user_info(tokens["access_token"])
                except:
                    is_expired = True
            
            return {
                "connected": True,
                "expired": is_expired,
                "scopes": token_data["scopes"],
                "expires_at": token_data["expires_at"],
                "user_info": user_info,
                "needs_refresh": is_expired and token_data["refresh_token"] is not None
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to check OAuth status: {str(e)}"
            )


    async def _get_user_info(self, access_token: str) -> Dict[str, Any]:
        """Get user info from Google with better error handling"""
        
        async with httpx.AsyncClient() as client:
            # Try the userinfo endpoint first
            try:
                response = await client.get(
                    "https://www.googleapis.com/oauth2/v2/userinfo",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    print(f"Userinfo API failed with status {response.status_code}: {response.text}")
                    
            except Exception as e:
                print(f"Userinfo API error: {str(e)}")
            
            # Fallback: try the tokeninfo endpoint
            try:
                response = await client.get(
                    f"https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={access_token}"
                )
                
                if response.status_code == 200:
                    token_info = response.json()
                    # Extract user info from token info
                    return {
                        "email": token_info.get("email"),
                        "name": token_info.get("email", "").split("@")[0],  # Use email prefix as name
                        "verified_email": token_info.get("verified_email", True),
                        "scope": token_info.get("scope", "")
                    }
                else:
                    print(f"Tokeninfo API failed with status {response.status_code}: {response.text}")
                    
            except Exception as e:
                print(f"Tokeninfo API error: {str(e)}")
            
            # If both fail, return minimal info (OAuth still succeeded)
            print("Warning: Could not get user info from Google, using minimal info")
            return {
                "email": "<EMAIL>",
                "name": "Unknown User", 
                "note": "User info could not be retrieved, but OAuth tokens are valid"
            }