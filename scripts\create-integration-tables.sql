-- Create integration management tables for Uru Workspace Platform
-- This script creates the necessary tables to support multiple Composio integrations

-- Integration definitions table - stores available integrations
CREATE TABLE IF NOT EXISTS integration_definitions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL,
  composio_app_name VARCHAR(100) NOT NULL UNIQUE,
  auth_type VARCHAR(20) NOT NULL CHECK (auth_type IN ('OAUTH2', 'API_KEY', 'BEARER_TOKEN')),
  default_scopes JSONB DEFAULT '[]',
  capabilities JSONB DEFAULT '[]',
  tier INTEGER NOT NULL DEFAULT 3 CHECK (tier BETWEEN 1 AND 5),
  is_enabled BOOLEAN DEFAULT true,
  logo_url TEXT,
  documentation_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee integrations table - tracks which integrations each employee has enabled
CREATE TABLE IF NOT EXISTS employee_integrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID NOT NULL,
  integration_id UUID NOT NULL REFERENCES integration_definitions(id) ON DELETE CASCADE,
  is_enabled BOOLEAN DEFAULT true,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(employee_id, integration_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_integration_definitions_category ON integration_definitions(category);
CREATE INDEX IF NOT EXISTS idx_integration_definitions_composio_app ON integration_definitions(composio_app_name);
CREATE INDEX IF NOT EXISTS idx_integration_definitions_enabled ON integration_definitions(is_enabled);
CREATE INDEX IF NOT EXISTS idx_employee_integrations_employee ON employee_integrations(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_integrations_enabled ON employee_integrations(is_enabled);

-- Insert initial integration definitions for Tier 1 integrations
INSERT INTO integration_definitions (name, description, category, composio_app_name, auth_type, default_scopes, capabilities, tier, logo_url) VALUES
('Slack', 'Team communication, channel management, file sharing', 'Communication', 'slack', 'OAUTH2', '["channels:read", "chat:write", "files:read"]', '["send_message", "list_channels", "upload_file", "read_messages"]', 1, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg'),
('Microsoft Teams', 'Enterprise communication, meetings, collaboration', 'Communication', 'microsoft_teams', 'OAUTH2', '["https://graph.microsoft.com/Chat.ReadWrite", "https://graph.microsoft.com/Team.ReadBasic.All"]', '["send_message", "list_teams", "create_meeting", "manage_channels"]', 1, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/microsoft-teams-logo.jpeg'),
('Notion', 'Knowledge management, documentation, project planning', 'Productivity', 'notion', 'OAUTH2', '["read", "write"]', '["create_page", "update_page", "search_pages", "manage_databases"]', 1, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg'),
('Airtable', 'Database management, project tracking, workflow automation', 'Productivity', 'airtable', 'OAUTH2', '["data.records:read", "data.records:write", "schema.bases:read"]', '["create_record", "update_record", "list_records", "manage_tables"]', 1, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/airtable.svg'),
('Asana', 'Project management, task tracking, team coordination', 'Project Management', 'asana', 'OAUTH2', '["default"]', '["create_task", "update_task", "list_projects", "manage_teams"]', 1, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/asana.png'),
('HubSpot', 'CRM, marketing automation, sales pipeline management', 'CRM', 'hubspot', 'OAUTH2', '["crm.objects.contacts.read", "crm.objects.deals.read", "crm.objects.companies.read"]', '["manage_contacts", "track_deals", "create_companies", "send_emails"]', 2, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/hubspot.webp'),
('Salesforce', 'Enterprise CRM, lead management, sales analytics', 'CRM', 'salesforce', 'OAUTH2', '["api", "refresh_token", "offline_access"]', '["manage_leads", "track_opportunities", "create_accounts", "run_reports"]', 2, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/salesforce.svg'),
('Trello', 'Kanban-style project management', 'Project Management', 'trello', 'OAUTH1', '["read", "write"]', '["create_card", "update_card", "manage_boards", "track_progress"]', 3, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/trello.svg'),
('Calendly', 'Meeting scheduling, appointment booking', 'Scheduling', 'calendly', 'OAUTH2', '["default"]', '["schedule_meeting", "list_events", "manage_availability", "send_invites"]', 3, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/calendly.svg'),
('Zoom', 'Video conferencing, webinars, recordings', 'Communication', 'zoom', 'OAUTH2', '["meeting:write", "meeting:read", "webinar:write"]', '["create_meeting", "start_meeting", "manage_webinars", "access_recordings"]', 2, 'https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zoom.svg')
ON CONFLICT (composio_app_name) DO NOTHING;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_integration_definitions_updated_at BEFORE UPDATE ON integration_definitions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_employee_integrations_updated_at BEFORE UPDATE ON employee_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
