# auth-service/main.py
# Uru Platform Authentication Service - User Identity Management

from fastapi import FastAPI, HTTPException, Depends, status, Form, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from supabase import create_client, Client
from typing import Optional, List
from datetime import datetime, timedelta
import asyncio
import logging
import secrets
import jwt
import os
import sys
from dotenv import load_dotenv
from contextlib import asynccontextmanager

# Add shared directory to path for environment utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))
try:
    from environment import env_config
    print(f"Environment detected: {env_config.environment.value}")
except ImportError:
    print("Environment utilities not found, using fallback configuration")
    env_config = None

# Import our custom modules
from auth import EmployeeAuth

logging.basicConfig(level=logging.INFO)

# ===========================================
# SECURITY MIDDLEWARE
# ===========================================

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to all responses"""

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

        # HSTS for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        # CSP for API endpoints
        response.headers["Content-Security-Policy"] = "default-src 'none'; frame-ancestors 'none';"

        return response

class RequestIDMiddleware(BaseHTTPMiddleware):
    """Add unique request ID for tracing"""

    async def dispatch(self, request: Request, call_next):
        import uuid
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id

        return response

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Sanitize error responses for production"""

    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Log full error details
            request_id = getattr(request.state, 'request_id', 'unknown')
            logging.error(f"Request {request_id} failed: {str(e)}", exc_info=True)

            # Return sanitized error response
            if isinstance(e, HTTPException):
                return JSONResponse(
                    status_code=e.status_code,
                    content={
                        "error": e.detail,
                        "request_id": request_id,
                        "timestamp": datetime.now().isoformat()
                    }
                )
            else:
                # Generic error for unexpected exceptions
                return JSONResponse(
                    status_code=500,
                    content={
                        "error": "Internal server error",
                        "request_id": request_id,
                        "timestamp": datetime.now().isoformat()
                    }
                )

# ===========================================
# HTTP PROTECTION MIDDLEWARE
# ===========================================

class RequestSizeLimitMiddleware(BaseHTTPMiddleware):
    """Limit request body size to prevent DoS attacks"""

    def __init__(self, app, max_size: int = 5 * 1024 * 1024):  # 5MB default for auth
        super().__init__(app)
        self.max_size = max_size

    async def dispatch(self, request: Request, call_next):
        # Check Content-Length header
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                if size > self.max_size:
                    return JSONResponse(
                        status_code=413,
                        content={
                            "error": "Request entity too large",
                            "max_size_mb": self.max_size / (1024 * 1024),
                            "timestamp": datetime.now().isoformat()
                        }
                    )
            except ValueError:
                pass  # Invalid Content-Length, let it through for now

        return await call_next(request)

class TimeoutMiddleware(BaseHTTPMiddleware):
    """Add request timeout protection"""

    def __init__(self, app, timeout_seconds: int = 30):
        super().__init__(app)
        self.timeout_seconds = timeout_seconds

    async def dispatch(self, request: Request, call_next):
        try:
            import asyncio
            return await asyncio.wait_for(
                call_next(request),
                timeout=self.timeout_seconds
            )
        except asyncio.TimeoutError:
            return JSONResponse(
                status_code=408,
                content={
                    "error": "Request timeout",
                    "timeout_seconds": self.timeout_seconds,
                    "timestamp": datetime.now().isoformat()
                }
            )

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan - start/stop background tasks"""
    # Startup
    print("Starting Uru Authentication Service...")
    
    yield

    # Shutdown
    print("Shutting down Authentication Service...")

# Create FastAPI app
app = FastAPI(
    title="Uru Authentication Service",
    version="2025.1",
    description="User authentication and identity management for Uru Platform",
    lifespan=lifespan
)

# CORS configuration
if env_config:
    allowed_origins = env_config.get_cors_origins()
else:
    allowed_origins = [
        "http://localhost:3000",
        "https://app.uruenterprises.com",
        "https://auth.uruenterprises.com"
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Apply security middleware
app.add_middleware(ErrorHandlingMiddleware)
app.add_middleware(RequestIDMiddleware)
app.add_middleware(SecurityHeadersMiddleware)

# Apply HTTP protection middleware
app.add_middleware(TimeoutMiddleware, timeout_seconds=30)
app.add_middleware(RequestSizeLimitMiddleware, max_size=5 * 1024 * 1024)  # 5MB limit for auth

# Global variables for auth systems
supabase = None
employee_auth = None
auth_initialized = False

# Initialize Supabase
def initialize_supabase():
    """Initialize Supabase client with retry logic"""
    global supabase
    
    try:
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            print("⚠️ Supabase credentials not found in environment")
            return None
            
        supabase = create_client(supabase_url, supabase_key)
        print("✅ Supabase client initialized successfully")
        return supabase
        
    except Exception as e:
        print(f"❌ Supabase initialization failed: {e}")
        return None

# Initialize auth systems
def initialize_auth_systems():
    """Initialize authentication systems with retry logic"""
    global employee_auth

    if supabase:
        try:
            employee_auth = EmployeeAuth(supabase)
            print("✅ Authentication systems initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Auth system initialization failed: {e}")
            employee_auth = None
            return False
    else:
        print("⚠️ Auth systems disabled due to Supabase connection failure")
        employee_auth = None
        return False

# Initialize systems on startup
supabase = initialize_supabase()
auth_initialized = initialize_auth_systems()

# Security
security = HTTPBearer(auto_error=False)

async def get_current_employee(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated employee"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    
    try:
        employee = await employee_auth.get_current_employee(credentials.credentials)
        if not employee:
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication token"
            )
        return employee
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail=f"Authentication failed: {str(e)}"
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Enhanced health check endpoint with comprehensive dependency validation"""
    start_time = datetime.now()

    # Database health check
    db_healthy = True
    db_response_time = 0
    try:
        db_start = datetime.now()
        result = supabase.table('employees').select('id').limit(1).execute()
        db_response_time = (datetime.now() - db_start).total_seconds() * 1000
    except Exception as e:
        db_healthy = False
        logging.error(f"Database health check failed: {e}")

    # Authentication system health check
    auth_healthy = True
    try:
        # Test JWT token creation
        test_payload = {"test": True, "exp": datetime.now() + timedelta(minutes=1)}
        test_token = employee_auth.create_token(test_payload) if employee_auth else None
        auth_healthy = bool(test_token)
    except Exception as e:
        auth_healthy = False
        logging.error(f"Auth system health check failed: {e}")

    # Workspace table health check
    workspace_healthy = True
    try:
        supabase.table('workspaces').select('id').limit(1).execute()
    except Exception as e:
        workspace_healthy = False
        logging.error(f"Workspace table health check failed: {e}")

    # Overall health determination
    overall_healthy = db_healthy and auth_healthy and workspace_healthy

    health_status = {
        "status": "healthy" if overall_healthy else "unhealthy",
        "service": "auth-service",
        "version": "2025.1",
        "timestamp": datetime.now().isoformat(),
        "response_time_ms": (datetime.now() - start_time).total_seconds() * 1000,
        "checks": {
            "database": {
                "healthy": db_healthy,
                "response_time_ms": round(db_response_time, 2)
            },
            "authentication": {
                "healthy": auth_healthy,
                "jwt_creation": "operational" if auth_healthy else "failed"
            },
            "workspaces": {
                "healthy": workspace_healthy
            }
        },
        "environment": {
            "supabase_configured": bool(supabase),
            "jwt_secret_configured": bool(employee_auth and hasattr(employee_auth, 'secret_key'))
        }
    }

    status_code = 200 if overall_healthy else 503
    return JSONResponse(content=health_status, status_code=status_code)

# Authentication endpoints
@app.post("/auth/login")
async def login_employee(
    request: Request,
    email: str = Form(...),
    password: str = Form(...),
    workspace_slug: str = Form(...),
    remember_me: bool = Form(False)
):
    """Employee login endpoint"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )

    try:
        # Get client information for remember me tokens
        user_agent = request.headers.get("user-agent")
        client_ip = request.client.host if request.client else None

        result = await employee_auth.login(
            email,
            password,
            workspace_slug,
            remember_me,
            user_agent,
            client_ip
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Login failed: {str(e)}"
        )

@app.get("/auth/me")
async def get_current_user(employee = Depends(get_current_employee)):
    """Get current authenticated user information"""
    # Handle both formats: with workspaces join or without
    workspace_data = employee.get("workspaces", {})

    # If no workspace join data, fetch workspace separately
    if not workspace_data and employee.get("workspace_id"):
        try:
            workspace_result = supabase.table('workspaces').select("*").eq('id', employee["workspace_id"]).single().execute()
            if workspace_result.data:
                workspace_data = workspace_result.data
        except Exception as e:
            print(f"Warning: Could not fetch workspace data: {e}")
            workspace_data = {}

    return {
        "employee": {
            "id": employee["id"],
            "email": employee["email"],
            "name": employee.get("name") or f"{employee.get('first_name', '')} {employee.get('last_name', '')}".strip(),
            "role": employee.get("role", "employee"),
            "workspace": {
                "id": workspace_data.get("id") if workspace_data else employee.get("workspace_id"),
                "name": workspace_data.get("name", "Default Workspace") if workspace_data else "Default Workspace",
                "slug": workspace_data.get("slug", "default") if workspace_data else "default"
            }
        }
    }

@app.post("/auth/logout")
async def logout_employee(employee = Depends(get_current_employee)):
    """Employee logout endpoint"""
    # For JWT-based auth, logout is typically handled client-side
    # But we can invalidate tokens if needed in the future
    return {"message": "Logged out successfully"}

@app.post("/auth/refresh")
async def refresh_token(employee = Depends(get_current_employee)):
    """Refresh authentication token"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    try:
        # Generate new token for the employee
        new_token = await employee_auth.generate_token(employee["id"])
        return {"access_token": new_token, "token_type": "bearer"}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Token refresh failed: {str(e)}"
        )

# Employee management endpoints
@app.post("/employees/invite")
async def invite_employee(
    email: str = Form(...),
    first_name: str = Form(...),
    last_name: str = Form(...),
    role: str = Form("employee"),
    employee = Depends(get_current_employee)
):
    """Invite new employee to workspace"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    # Check if current employee has permission to invite
    if employee.get("role") not in ["admin", "owner"]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to invite employees"
        )
    
    try:
        result = await employee_auth.invite_employee(
            email, first_name, last_name, role, employee["workspace_id"]
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Employee invitation failed: {str(e)}"
        )

@app.post("/employees/accept-invitation")
async def accept_invitation(
    token: str = Form(...),
    password: str = Form(...)
):
    """Accept employee invitation and set password"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    try:
        result = await employee_auth.accept_invitation(token, password)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Invitation acceptance failed: {str(e)}"
        )

# Admin endpoints
@app.post("/admin/retry-auth-init")
async def retry_auth_initialization():
    """Retry authentication system initialization (admin endpoint)"""
    global supabase, employee_auth, auth_initialized

    # Try to reinitialize Supabase
    supabase = initialize_supabase()

    # Try to reinitialize auth systems
    auth_initialized = initialize_auth_systems()

    return {
        "supabase_connected": supabase is not None,
        "auth_systems_initialized": auth_initialized,
        "employee_auth_available": employee_auth is not None,
        "message": "Reinitialization attempt completed"
    }

@app.post("/dev/test-invitation")
async def create_test_invitation():
    """Create a test invitation for development (dev endpoint)"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )

    try:
        # Create or get test workspace
        workspace_result = supabase.table('workspaces').select("*").eq('slug', 'test-workspace').execute()

        if not workspace_result.data:
            # Create test workspace
            workspace_data = {
                "name": "Test Workspace",
                "slug": "test-workspace",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            workspace_result = supabase.table('workspaces').insert(workspace_data).execute()

        workspace = workspace_result.data[0]

        # Create test invitation
        result = await employee_auth.create_employee_invitation(
            workspace["id"],
            "<EMAIL>",
            "Dev User",
            "admin"
        )

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Test invitation creation failed: {str(e)}"
        )

@app.post("/dev/test-user")
async def create_test_user():
    """Create a test user with known credentials for development (dev endpoint)"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )

    try:
        # Create or get test workspace
        workspace_result = supabase.table('workspaces').select("*").eq('slug', 'test-workspace').execute()

        if not workspace_result.data:
            # Create test workspace
            workspace_data = {
                "name": "Test Workspace",
                "slug": "test-workspace",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            workspace_result = supabase.table('workspaces').insert(workspace_data).execute()

        workspace = workspace_result.data[0]

        # Check if test user already exists
        existing_user = supabase.table('employees').select("*").eq('email', '<EMAIL>').execute()

        if existing_user.data:
            return {
                "message": "Test user already exists",
                "email": "<EMAIL>",
                "workspace_slug": "test-workspace",
                "password": "testpassword123"
            }

        # Create test user directly with password
        password_hash = employee_auth._hash_password("testpassword123")

        test_user_data = {
            "workspace_id": workspace["id"],
            "email": "<EMAIL>",
            "name": "Test User",
            "role": "admin",
            "status": "active",
            "password_hash": password_hash,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        user_result = supabase.table('employees').insert(test_user_data).execute()

        if user_result.data:
            return {
                "message": "Test user created successfully",
                "email": "<EMAIL>",
                "workspace_slug": "test-workspace",
                "password": "testpassword123",
                "user_id": user_result.data[0]["id"]
            }
        else:
            raise Exception("Failed to create test user")

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Test user creation failed: {str(e)}"
        )

@app.post("/admin/claude-desktop-token")
async def generate_claude_desktop_token(
    employee_email: str = Form(...),
    workspace_slug: str = Form(...),
    days_valid: int = Form(90)
):
    """Generate Claude Desktop token for employee (admin endpoint)"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )

    try:
        # Find employee by email and workspace
        employee_result = supabase.table('employees').select(
            "id, email, name, role, workspace_id, workspaces(slug)"
        ).eq('email', employee_email).execute()

        if not employee_result.data:
            raise HTTPException(
                status_code=404,
                detail=f"Employee not found: {employee_email}"
            )

        employee = employee_result.data[0]

        # Verify workspace matches
        if employee.get('workspaces', {}).get('slug') != workspace_slug:
            raise HTTPException(
                status_code=400,
                detail=f"Employee {employee_email} is not in workspace {workspace_slug}"
            )

        # Generate Claude Desktop token
        result = await employee_auth._create_claude_desktop_token(employee["id"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Claude Desktop token generation failed: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    import os
    port = int(os.getenv("APP_PORT", 8003))
    uvicorn.run(app, host="0.0.0.0", port=port)
