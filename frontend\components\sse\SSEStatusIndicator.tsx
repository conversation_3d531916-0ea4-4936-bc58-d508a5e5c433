// frontend/components/sse/SSEStatusIndicator.tsx
// Simple status indicator for SSE connection in the UI

import React, { useState } from 'react';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  X
} from 'lucide-react';
import { useSSEOptional } from './SSEProvider';

interface SSEStatusIndicatorProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  showDetails?: boolean;
  className?: string;
}

export const SSEStatusIndicator: React.FC<SSEStatusIndicatorProps> = ({
  position = 'top-right',
  showDetails = false,
  className = ''
}) => {
  const sse = useSSEOptional();
  const [showTooltip, setShowTooltip] = useState(false);
  const [showDetailPanel, setShowDetailPanel] = useState(false);

  // Don't render if SSE is not available
  if (!sse) {
    return null;
  }

  const getStatusColor = () => {
    if (sse.isConnected) return 'bg-green-500';
    if (sse.isConnecting) return 'bg-yellow-500';
    if (sse.error) return 'bg-red-500';
    return 'bg-gray-500';
  };

  const getStatusIcon = () => {
    if (sse.isConnected) return <Wifi className="w-3 h-3 text-white" />;
    if (sse.isConnecting) return <Loader2 className="w-3 h-3 text-white animate-spin" />;
    if (sse.error) return <WifiOff className="w-3 h-3 text-white" />;
    return <WifiOff className="w-3 h-3 text-white" />;
  };

  const getStatusText = () => {
    if (sse.isConnected) return 'SSE Connected';
    if (sse.isConnecting) return 'SSE Connecting...';
    if (sse.error) return `SSE Error: ${sse.error}`;
    return 'SSE Disconnected';
  };

  const getPositionClasses = () => {
    const base = 'fixed z-50';
    switch (position) {
      case 'top-left':
        return `${base} top-4 left-4`;
      case 'top-right':
        return `${base} top-4 right-4`;
      case 'bottom-left':
        return `${base} bottom-4 left-4`;
      case 'bottom-right':
        return `${base} bottom-4 right-4`;
      default:
        return `${base} top-4 right-4`;
    }
  };

  const formatTimestamp = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleString();
  };

  return (
    <>
      {/* Status Indicator */}
      <div className={`${getPositionClasses()} ${className}`}>
        <div
          className="relative"
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          <button
            onClick={() => setShowDetailPanel(!showDetailPanel)}
            className={`
              w-8 h-8 rounded-full ${getStatusColor()} 
              flex items-center justify-center
              shadow-lg hover:shadow-xl transition-all duration-200
              ${sse.isConnecting ? 'animate-pulse' : ''}
            `}
            title={getStatusText()}
          >
            {getStatusIcon()}
          </button>

          {/* Tooltip */}
          {showTooltip && !showDetailPanel && (
            <div className="absolute bottom-full mb-2 right-0 bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap shadow-lg">
              {getStatusText()}
              <div className="absolute top-full right-2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
            </div>
          )}
        </div>
      </div>

      {/* Detail Panel */}
      {showDetailPanel && (
        <div className={`
          fixed z-50 bg-gray-800 border border-gray-700 rounded-lg shadow-xl
          ${position.includes('right') ? 'right-4' : 'left-4'}
          ${position.includes('top') ? 'top-16' : 'bottom-16'}
          w-80 max-w-sm
        `}>
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-700">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
              <span className="text-sm font-medium text-white">SSE Connection</span>
            </div>
            <button
              onClick={() => setShowDetailPanel(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* Content */}
          <div className="p-3 space-y-3">
            {/* Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Status</span>
              <div className="flex items-center space-x-2">
                {sse.isConnected && <CheckCircle className="w-4 h-4 text-green-500" />}
                {sse.isConnecting && <Loader2 className="w-4 h-4 text-yellow-500 animate-spin" />}
                {sse.error && <AlertTriangle className="w-4 h-4 text-red-500" />}
                <span className={`text-sm ${
                  sse.isConnected ? 'text-green-400' :
                  sse.isConnecting ? 'text-yellow-400' :
                  'text-red-400'
                }`}>
                  {sse.isConnected ? 'Connected' : sse.isConnecting ? 'Connecting' : 'Disconnected'}
                </span>
              </div>
            </div>

            {/* Health Score */}
            {sse.healthScore > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Health Score</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        sse.healthScore >= 80 ? 'bg-green-500' :
                        sse.healthScore >= 60 ? 'bg-yellow-500' :
                        sse.healthScore >= 40 ? 'bg-orange-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.max(sse.healthScore, 5)}%` }}
                    />
                  </div>
                  <span className={`text-sm ${
                    sse.healthScore >= 80 ? 'text-green-400' :
                    sse.healthScore >= 60 ? 'text-yellow-400' :
                    sse.healthScore >= 40 ? 'text-orange-400' : 'text-red-400'
                  }`}>
                    {sse.healthScore}%
                  </span>
                </div>
              </div>
            )}

            {/* Last Connected */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Last Connected</span>
              <span className="text-sm text-white">
                {formatTimestamp(sse.lastConnected)}
              </span>
            </div>

            {/* Reconnect Attempts */}
            {sse.reconnectAttempts > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Reconnect Attempts</span>
                <span className="text-sm text-white">{sse.reconnectAttempts}</span>
              </div>
            )}

            {/* Error */}
            {sse.error && (
              <div className="bg-red-900/20 border border-red-800 rounded p-2">
                <div className="text-xs text-red-300">{sse.error}</div>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2 pt-2">
              <button
                onClick={sse.reconnect}
                disabled={sse.isConnecting}
                className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white text-sm rounded transition-colors"
              >
                <RefreshCw className={`w-3 h-3 ${sse.isConnecting ? 'animate-spin' : ''}`} />
                <span>Reconnect</span>
              </button>
              
              {sse.messages.length > 0 && (
                <button
                  onClick={sse.clearMessages}
                  className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors"
                >
                  Clear ({sse.messages.length})
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SSEStatusIndicator;
