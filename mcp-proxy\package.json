{"name": "uru-smart-mcp-proxy", "version": "2.0.0", "description": "Smart MCP Proxy with intelligent tool orchestration and OAuth injection", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "proxy", "o<PERSON>h", "tools", "claude", "openai"], "author": "Uru Enterprises LLC", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.0", "axios": "^1.10.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "eventsource": "^4.0.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mcp-remote": "^0.1.16", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.1.7"}, "engines": {"node": ">=18.0.0"}}