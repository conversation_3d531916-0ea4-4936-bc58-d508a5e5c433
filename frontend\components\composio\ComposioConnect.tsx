import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ircle, ExternalLink, Loader2, AlertCircle, Zap } from 'lucide-react';
import { apiService } from '../../utils/api';
import { useAuth } from '../auth/AuthContext';

interface ComposioConnection {
  app_name: string;
  status: string;
  connected_at: string;
  last_updated: string;
}

interface ComposioConnectProps {
  onConnectionUpdate?: () => void;
}

const ComposioConnect: React.FC<ComposioConnectProps> = ({ onConnectionUpdate }) => {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [connections, setConnections] = useState<ComposioConnection[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectingApp, setConnectingApp] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Available apps for connection
  const availableApps = [
    {
      name: 'gmail',
      displayName: 'Gmail',
      description: 'Email management and automation',
      icon: '📧',
      color: 'bg-red-500'
    },
    {
      name: 'drive',
      displayName: 'Google Drive',
      description: 'File storage and document management',
      icon: '📁',
      color: 'bg-blue-500'
    },
    {
      name: 'calendar',
      displayName: 'Google Calendar',
      description: 'Schedule and event management',
      icon: '📅',
      color: 'bg-green-500'
    },
    {
      name: 'github',
      displayName: 'GitHub',
      description: 'Code repository management',
      icon: '🐙',
      color: 'bg-gray-800'
    },
    {
      name: 'slack',
      displayName: 'Slack',
      description: 'Team communication and collaboration',
      icon: '💬',
      color: 'bg-purple-500'
    }
  ];

  // Only load connections when authentication is ready and user is authenticated
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      loadConnections();
    } else if (!authLoading && !isAuthenticated) {
      setLoading(false);
      setError('Authentication required');
    }
  }, [authLoading, isAuthenticated]);

  const loadConnections = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 ComposioConnect: Loading connections...');

      const result = await apiService.getComposioConnections();

      console.log('📊 ComposioConnect: API result:', {
        success: result.success,
        connectionsCount: result.connections?.length || 0
      });

      if (result.success) {
        setConnections(result.connections || []);
        console.log('✅ ComposioConnect: Connections loaded successfully');
      } else {
        console.warn('⚠️ ComposioConnect: Failed to load connections:', result);
        setError('Failed to load app connections');
      }
    } catch (err) {
      console.error('❌ ComposioConnect: Failed to load connections:', err);
      setError(err instanceof Error ? err.message : 'Failed to load app connections');
    } finally {
      setLoading(false);
    }
  };

  const connectApp = async (appName: string) => {
    try {
      setConnectingApp(appName);
      setError(null);

      const result = await apiService.connectComposioApp(appName, {
        redirect_url: `${window.location.origin}/app/settings`
      });

      if (result.success && result.authorization_url) {
        // Redirect to authorization URL
        window.location.href = result.authorization_url;
      } else {
        setError(`Failed to connect ${appName}`);
      }
    } catch (err) {
      console.error(`Failed to connect ${appName}:`, err);
      setError(`Failed to connect ${appName}`);
    } finally {
      setConnectingApp(null);
    }
  };

  const disconnectApp = async (appName: string) => {
    try {
      setError(null);
      
      const result = await apiService.disconnectComposioApp(appName);
      
      if (result.success) {
        // Reload connections
        await loadConnections();
        onConnectionUpdate?.();
      } else {
        setError(`Failed to disconnect ${appName}`);
      }
    } catch (err) {
      console.error(`Failed to disconnect ${appName}:`, err);
      setError(`Failed to disconnect ${appName}`);
    }
  };

  const isConnected = (appName: string) => {
    return connections.some(conn => conn.app_name === appName && conn.status === 'active');
  };

  const getConnection = (appName: string) => {
    return connections.find(conn => conn.app_name === appName && conn.status === 'active');
  };

  // Show loading state while auth is loading or data is loading
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-300">
          {authLoading ? 'Checking authentication...' : 'Loading app connections...'}
        </span>
      </div>
    );
  }

  // Show error state if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center p-8">
        <AlertCircle className="w-6 h-6 text-red-500" />
        <span className="ml-2 text-red-300">Authentication required to manage app connections</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <Zap className="w-6 h-6 text-blue-400" />
        <h3 className="text-xl font-semibold text-white">Personal Productivity Tools</h3>
      </div>

      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
            <span className="text-red-300">{error}</span>
          </div>
          <button
            onClick={loadConnections}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <Loader2 className="w-4 h-4" />
            <span>Retry</span>
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {availableApps.map((app) => {
          const connected = isConnected(app.name);
          const connection = getConnection(app.name);
          const connecting = connectingApp === app.name;

          return (
            <div
              key={app.name}
              className="bg-gray-800/50 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 ${app.color} rounded-lg flex items-center justify-center text-white text-lg`}>
                    {app.icon}
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white">{app.displayName}</h4>
                    <p className="text-sm text-gray-400">{app.description}</p>
                  </div>
                </div>
                
                {connected && (
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                )}
              </div>

              {connected && connection && (
                <div className="mb-4 p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-green-300 text-sm">
                    <CheckCircle className="w-4 h-4" />
                    <span>Connected</span>
                  </div>
                  <p className="text-xs text-green-400 mt-1">
                    Since {new Date(connection.connected_at).toLocaleDateString()}
                  </p>
                </div>
              )}

              <div className="flex space-x-2">
                {connected ? (
                  <button
                    onClick={() => disconnectApp(app.name)}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm font-medium"
                  >
                    Disconnect
                  </button>
                ) : (
                  <button
                    onClick={() => connectApp(app.name)}
                    disabled={connecting}
                    className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium flex items-center justify-center space-x-2"
                  >
                    {connecting ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span>Connecting...</span>
                      </>
                    ) : (
                      <>
                        <ExternalLink className="w-4 h-4" />
                        <span>Connect</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>

      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Zap className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="text-blue-300 font-medium mb-1">Instant Access</h4>
            <p className="text-blue-200 text-sm">
              Connect your personal productivity tools to unlock AI-powered automation and insights. 
              All connections are secure and you maintain full control over your data.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComposioConnect;
