import React from 'react';
import { GetServerSideProps } from 'next';
import { Sidebar } from '../../../components/shared/Sidebar';
import { ProtectedRoute } from '../../../components/auth/ProtectedRoute';
import { TrendingUp, TrendingDown, ArrowUpRight, ArrowDownRight, Calendar } from 'lucide-react';

export default function TrendsPage() {
  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-900 text-white">
      <Sidebar />
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <div className="bg-gray-900 border-b border-gray-800 flex-shrink-0">
          <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-white">Ignition Consultants</h1>
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-green-400 text-sm">Live</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                JM
              </div>
              <span className="text-white text-sm">Jackson Moss</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">Client Engagement Trends</h2>
              <div className="flex items-center space-x-2 text-gray-400">
                <Calendar className="w-4 h-4" />
                <span className="text-sm">Last 30 Days</span>
              </div>
            </div>

            {/* Trend Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">AI/Technology Questions</h3>
                  <div className="flex items-center space-x-1 text-green-400">
                    <ArrowUpRight className="w-4 h-4" />
                    <span className="text-2xl font-bold">+73%</span>
                  </div>
                </div>
                <p className="text-gray-400 text-sm mb-2">Significant increase in clients asking about automation, AI tools, and digital transformation</p>
                <div className="text-xs text-gray-500">
                  Shift toward higher-level strategic guidance rather than basic tactical support
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Cash Flow Optimization</h3>
                  <div className="flex items-center space-x-1 text-green-400">
                    <ArrowUpRight className="w-4 h-4" />
                    <span className="text-2xl font-bold">+45%</span>
                  </div>
                </div>
                <p className="text-gray-400 text-sm mb-2">More clients seeking advanced cash flow forecasting and optimization strategies</p>
                <div className="text-xs text-gray-500">
                  Indicates higher engagement and trust in our capabilities
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Strategic vs Tactical Requests</h3>
                  <div className="flex items-center space-x-1 text-green-400">
                    <ArrowUpRight className="w-4 h-4" />
                    <span className="text-2xl font-bold">+32%</span>
                  </div>
                </div>
                <p className="text-gray-400 text-sm mb-2">Shift toward higher-level strategic guidance rather than basic tactical support</p>
                <div className="text-xs text-gray-500">
                  Clients value our expertise for complex business decisions
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Response Time</h3>
                  <div className="flex items-center space-x-1 text-red-400">
                    <ArrowDownRight className="w-4 h-4" />
                    <span className="text-2xl font-bold">-28%</span>
                  </div>
                </div>
                <p className="text-gray-400 text-sm mb-2">Improved from 6.1 to 4.2 hours average response time to client questions</p>
                <div className="text-xs text-gray-500">
                  Faster response correlates with higher satisfaction
                </div>
              </div>
            </div>

            {/* Service Performance Metrics */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">Service Performance Metrics</h3>
                <span className="text-gray-400 text-sm">Q4 2024</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">85%</div>
                  <div className="text-white font-medium mb-1">Satisfaction Score Impact</div>
                  <div className="text-gray-400 text-sm">Higher satisfaction scores for clients with monthly check-ins vs ad-hoc meetings</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">+18%</div>
                  <div className="text-white font-medium mb-1">Meeting Duration</div>
                  <div className="text-gray-400 text-sm">Average client meeting length increased, indicating higher engagement</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">60%</div>
                  <div className="text-white font-medium mb-1">Retention Improvement</div>
                  <div className="text-gray-400 text-sm">Better retention rates for clients with structured monthly touchpoints</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">+23%</div>
                  <div className="text-white font-medium mb-1">Client Engagement</div>
                  <div className="text-gray-400 text-sm">Overall engagement increase correlating with improved response times</div>
                </div>
              </div>
            </div>

            {/* Business Development Opportunities */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">Business Development Opportunities</h3>
                <span className="text-gray-400 text-sm">Emerging Patterns</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400 mb-2">12</div>
                  <div className="text-white font-medium mb-1">Technology Adoption Prospects</div>
                  <div className="text-gray-400 text-sm">Clients specifically mentioned needing help with technology and automation</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-400 mb-2">8</div>
                  <div className="text-white font-medium mb-1">Scaling Operations Inquiries</div>
                  <div className="text-gray-400 text-sm">Growth-stage companies asking about operational scaling guidance</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-cyan-400 mb-2">5</div>
                  <div className="text-white font-medium mb-1">Fractional CFO Expansion</div>
                  <div className="text-gray-400 text-sm">Clients expressed interest in expanding fractional CFO services</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">3</div>
                  <div className="text-white font-medium mb-1">New Industry Verticals</div>
                  <div className="text-gray-400 text-sm">Potential expansion opportunities in healthcare, fintech, and manufacturing</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
    </ProtectedRoute>
  );
}

// Prevent static generation for protected routes
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
