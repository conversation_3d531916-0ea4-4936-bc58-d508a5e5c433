#!/usr/bin/env node

/**
 * SSE Environment Configuration Script
 * Automatically detects and configures SSE settings for different environments
 */

const fs = require('fs');
const path = require('path');

class SSEEnvironmentConfigurator {
    constructor() {
        this.environment = this.detectEnvironment();
        this.config = this.getEnvironmentConfig();
        
        console.log(`🔧 SSE Environment Configurator`);
        console.log(`📍 Detected Environment: ${this.environment}`);
    }

    detectEnvironment() {
        // Check for Elestio environment
        if (process.env.ENVIRONMENT === 'production' || 
            process.env.ELESTIO_DEPLOYMENT === 'true' ||
            process.env.HOSTNAME?.includes('elestio')) {
            return 'elestio';
        }
        
        // Check for Docker environment
        if (fs.existsSync('/.dockerenv') || process.env.DOCKER_ENV === 'true') {
            return 'docker';
        }
        
        // Default to local
        return 'local';
    }

    getEnvironmentConfig() {
        const configs = {
            elestio: {
                name: 'Elestio Production',
                sse_timeout: 30000,
                retry_delay: 2000,
                max_retries: 15,
                heartbeat_interval: 30000,
                health_check_interval: 60000,
                proxy_read_timeout: '3600s',
                proxy_buffering: 'off',
                connection_pool_size: 5,
                monitoring_enabled: true,
                alerts_enabled: true
            },
            docker: {
                name: 'Docker Development',
                sse_timeout: 15000,
                retry_delay: 1000,
                max_retries: 10,
                heartbeat_interval: 20000,
                health_check_interval: 30000,
                proxy_read_timeout: '300s',
                proxy_buffering: 'off',
                connection_pool_size: 3,
                monitoring_enabled: true,
                alerts_enabled: false
            },
            local: {
                name: 'Local Development',
                sse_timeout: 10000,
                retry_delay: 500,
                max_retries: 5,
                heartbeat_interval: 15000,
                health_check_interval: 20000,
                proxy_read_timeout: '60s',
                proxy_buffering: 'on',
                connection_pool_size: 1,
                monitoring_enabled: false,
                alerts_enabled: false
            }
        };
        
        return configs[this.environment];
    }

    async configure() {
        console.log(`\n🚀 Configuring SSE for ${this.config.name}...`);
        
        try {
            // 1. Generate environment-specific variables
            await this.generateEnvironmentFile();
            
            // 2. Update Docker configuration if needed
            await this.updateDockerConfig();
            
            // 3. Configure monitoring
            await this.configureMonitoring();
            
            // 4. Generate startup script
            await this.generateStartupScript();
            
            console.log(`\n✅ SSE configuration completed for ${this.config.name}!`);
            this.printUsageInstructions();
            
        } catch (error) {
            console.error(`\n❌ Configuration failed:`, error.message);
            process.exit(1);
        }
    }

    async generateEnvironmentFile() {
        console.log(`\n📝 Generating environment configuration...`);
        
        const envContent = `# SSE Configuration for ${this.config.name}
# Generated automatically - do not edit manually

# SSE Connection Settings
SSE_CONNECTION_TIMEOUT=${this.config.sse_timeout}
SSE_RETRY_DELAY=${this.config.retry_delay}
SSE_MAX_RETRIES=${this.config.max_retries}
SSE_HEARTBEAT_INTERVAL=${this.config.heartbeat_interval}
SSE_HEALTH_CHECK_INTERVAL=${this.config.health_check_interval}

# Connection Pool Settings
SSE_CONNECTION_POOL_SIZE=${this.config.connection_pool_size}

# Monitoring Settings
SSE_MONITORING_ENABLED=${this.config.monitoring_enabled}
SSE_ALERTS_ENABLED=${this.config.alerts_enabled}

# Environment Detection
SSE_ENVIRONMENT=${this.environment}
SSE_CONFIG_VERSION=1.0.0
SSE_CONFIG_GENERATED=${new Date().toISOString()}

# Debug Settings (disable in production)
DEBUG_SSE=${this.environment !== 'elestio'}
LOG_SSE_EVENTS=${this.environment !== 'elestio'}
`;

        const envPath = path.join(process.cwd(), `.env.sse.${this.environment}`);
        fs.writeFileSync(envPath, envContent);
        
        console.log(`   ✅ Generated: ${envPath}`);
        
        // Also update main .env file if it exists
        const mainEnvPath = path.join(process.cwd(), '.env');
        if (fs.existsSync(mainEnvPath)) {
            let mainEnv = fs.readFileSync(mainEnvPath, 'utf8');
            
            // Remove existing SSE config
            mainEnv = mainEnv.replace(/# SSE Configuration[\s\S]*?(?=\n#|\n[A-Z]|$)/g, '');
            
            // Add new SSE config
            mainEnv += `\n\n# SSE Configuration (auto-generated)\n`;
            mainEnv += envContent.split('\n').filter(line => 
                line.startsWith('SSE_') || line.startsWith('DEBUG_SSE') || line.startsWith('LOG_SSE')
            ).join('\n');
            
            fs.writeFileSync(mainEnvPath, mainEnv);
            console.log(`   ✅ Updated: ${mainEnvPath}`);
        }
    }

    async updateDockerConfig() {
        if (this.environment === 'local') {
            console.log(`\n🐳 Skipping Docker config for local environment`);
            return;
        }
        
        console.log(`\n🐳 Updating Docker configuration...`);
        
        // Create environment-specific Docker override
        const dockerOverride = `# Docker Compose Override for ${this.config.name}
version: '3.8'

services:
  mcp-proxy:
    environment:
      - SSE_CONNECTION_TIMEOUT=${this.config.sse_timeout}
      - SSE_RETRY_DELAY=${this.config.retry_delay}
      - SSE_MAX_RETRIES=${this.config.max_retries}
      - SSE_HEARTBEAT_INTERVAL=${this.config.heartbeat_interval}
      - SSE_MONITORING_ENABLED=${this.config.monitoring_enabled}
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/sse/status"]
      interval: ${Math.floor(this.config.health_check_interval / 1000)}s
      timeout: 10s
      retries: 3
      
    restart: unless-stopped
`;

        const overridePath = path.join(process.cwd(), `docker-compose.${this.environment}.yml`);
        fs.writeFileSync(overridePath, dockerOverride);
        
        console.log(`   ✅ Generated: ${overridePath}`);
    }

    async configureMonitoring() {
        if (!this.config.monitoring_enabled) {
            console.log(`\n📊 Monitoring disabled for ${this.environment} environment`);
            return;
        }
        
        console.log(`\n📊 Configuring monitoring...`);
        
        // Create monitoring configuration
        const monitoringConfig = {
            environment: this.environment,
            health_check_interval: this.config.health_check_interval,
            alert_thresholds: {
                health_score_critical: 30,
                health_score_warning: 70,
                connection_attempts_warning: 5,
                heartbeat_timeout_critical: 120000
            },
            endpoints: {
                health: '/health',
                sse_status: '/api/sse/status',
                sse_diagnostics: '/api/sse/diagnostics',
                sse_reconnect: '/api/sse/reconnect'
            }
        };
        
        const configPath = path.join(process.cwd(), 'config', `sse-monitoring.${this.environment}.json`);
        
        // Ensure config directory exists
        const configDir = path.dirname(configPath);
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }
        
        fs.writeFileSync(configPath, JSON.stringify(monitoringConfig, null, 2));
        console.log(`   ✅ Generated: ${configPath}`);
    }

    async generateStartupScript() {
        console.log(`\n🚀 Generating startup script...`);
        
        const scriptContent = `#!/bin/bash
# SSE-Optimized Startup Script for ${this.config.name}

echo "🔧 Starting Uru Workspace Platform with SSE optimization..."
echo "📍 Environment: ${this.config.name}"

# Load SSE environment variables
if [ -f ".env.sse.${this.environment}" ]; then
    echo "📝 Loading SSE configuration..."
    export $(cat .env.sse.${this.environment} | grep -v '^#' | xargs)
fi

# Start services with appropriate configuration
if [ "${this.environment}" = "elestio" ]; then
    echo "🌐 Starting for Elestio production..."
    docker-compose -f docker-compose.yml -f docker-compose.elestio.yml up -d
elif [ "${this.environment}" = "docker" ]; then
    echo "🐳 Starting for Docker development..."
    docker-compose -f docker-compose.yml -f docker-compose.docker.yml up -d
else
    echo "🏠 Starting for local development..."
    docker-compose up -d
fi

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 10

# Check SSE connection
echo "🔍 Checking SSE connection..."
if command -v curl &> /dev/null; then
    SSE_STATUS=$(curl -s http://localhost:3001/api/sse/status 2>/dev/null || echo "failed")
    if [[ "$SSE_STATUS" == *"connected"* ]]; then
        echo "✅ SSE connection established"
    else
        echo "⚠️ SSE connection not ready - check logs"
    fi
else
    echo "ℹ️ curl not available - check SSE status manually"
fi

echo "🎉 Startup completed!"
echo "📊 Monitor SSE health: http://localhost:3001/api/sse/status"
echo "🔧 SSE diagnostics: http://localhost:3001/api/sse/diagnostics"
`;

        const scriptPath = path.join(process.cwd(), `start-sse-optimized.sh`);
        fs.writeFileSync(scriptPath, scriptContent);
        
        try {
            fs.chmodSync(scriptPath, '755');
        } catch (error) {
            // Ignore chmod errors on Windows
        }
        
        console.log(`   ✅ Generated: ${scriptPath}`);
    }

    printUsageInstructions() {
        console.log(`\n📋 Usage Instructions for ${this.config.name}:`);
        console.log(`\n🚀 Starting the platform:`);
        console.log(`   ./start-sse-optimized.sh`);
        
        console.log(`\n📊 Monitoring SSE health:`);
        console.log(`   curl http://localhost:3001/api/sse/status`);
        
        console.log(`\n🔧 Troubleshooting:`);
        console.log(`   curl http://localhost:3001/api/sse/diagnostics`);
        
        console.log(`\n🔄 Force reconnection:`);
        console.log(`   curl -X POST http://localhost:3001/api/sse/reconnect`);
        
        if (this.environment === 'elestio') {
            console.log(`\n🌐 Production URLs:`);
            console.log(`   Health: https://mcp.uruenterprises.com/api/sse/status`);
            console.log(`   Diagnostics: https://mcp.uruenterprises.com/api/sse/diagnostics`);
        }
    }
}

// Run configuration if called directly
if (require.main === module) {
    const configurator = new SSEEnvironmentConfigurator();
    configurator.configure().catch(console.error);
}

module.exports = SSEEnvironmentConfigurator;
