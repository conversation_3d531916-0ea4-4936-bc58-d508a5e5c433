import React, { useState } from 'react';
import { GetServerSideProps } from 'next';
import { UruLogo } from '../components/shared/UruLogo';
import { Mail, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';

export default function RequestAccessPage() {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [workspaceSlug, setWorkspaceSlug] = useState('ignition-consultants');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // For now, we'll simulate the request since this would typically
      // send an email to administrators or create a pending request
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsSubmitted(true);
    } catch (err: any) {
      setError(err.message || 'Failed to submit request. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full text-center">
          <div className="flex justify-center mb-6">
            <UruLogo size="lg" />
          </div>
          
          <div className="bg-green-600/10 border border-green-600/30 rounded-lg p-8">
            <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">
              Request Submitted!
            </h2>
            <p className="text-gray-400 mb-6">
              We've received your access request for <strong>{workspaceSlug}</strong>. 
              A workspace administrator will review your request and send you an invitation 
              if approved.
            </p>
            <p className="text-gray-500 text-sm mb-6">
              You'll receive an email at <strong>{email}</strong> with further instructions.
            </p>
            
            <div className="space-y-3">
              <a 
                href="/login" 
                className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Back to Login
              </a>
              <button 
                onClick={() => {
                  setIsSubmitted(false);
                  setEmail('');
                  setName('');
                  setMessage('');
                }}
                className="block w-full text-gray-400 hover:text-gray-300 text-sm transition-colors"
              >
                Submit Another Request
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <UruLogo size="lg" />
          </div>
          <h2 className="text-3xl font-bold text-white">
            Request Workspace Access
          </h2>
          <p className="mt-2 text-gray-400">
            Submit a request to join a workspace
          </p>
        </div>

        {/* Request Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-600/10 border border-red-600/30 rounded-lg p-4 flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
              <span className="text-red-400 text-sm">{error}</span>
            </div>
          )}

          <div className="space-y-4">
            {/* Workspace */}
            <div>
              <label htmlFor="workspace" className="block text-sm font-medium text-gray-300 mb-2">
                Workspace
              </label>
              <input
                id="workspace"
                name="workspace"
                type="text"
                value={workspaceSlug}
                onChange={(e) => setWorkspaceSlug(e.target.value)}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="workspace-name"
                required
              />
              <p className="text-gray-500 text-xs mt-1">
                The workspace you'd like to join
              </p>
            </div>

            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                Full Name
              </label>
              <input
                id="name"
                name="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Your full name"
                required
              />
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
                required
              />
            </div>

            {/* Message */}
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                Message (Optional)
              </label>
              <textarea
                id="message"
                name="message"
                rows={3}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Tell us why you'd like to join this workspace..."
              />
            </div>
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Submitting request...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>Submit Request</span>
                </div>
              )}
            </button>
          </div>

          {/* Back Link */}
          <div className="text-center">
            <a 
              href="/login" 
              className="inline-flex items-center space-x-2 text-gray-400 hover:text-gray-300 transition-colors text-sm"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Login</span>
            </a>
          </div>
        </form>

        {/* Info Box */}
        <div className="bg-blue-600/10 border border-blue-600/30 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-blue-400 font-medium text-sm">How it works</h4>
              <p className="text-gray-400 text-xs mt-1">
                Your request will be sent to workspace administrators. If approved, 
                you'll receive an invitation email with a link to set up your account.
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-gray-500 text-xs">
            Powered by Uru Workspace Platform
          </p>
        </div>
      </div>
    </div>
  );
}

// Prevent static generation for this page to avoid SSR issues
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
