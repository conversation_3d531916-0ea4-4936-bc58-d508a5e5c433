// frontend/hooks/useSSEConnection.ts
// React hook for managing SSE connections with automatic reconnection

import { useState, useEffect, useRef, useCallback } from 'react';

export interface SSEConnectionState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  lastConnected: Date | null;
  reconnectAttempts: number;
  healthScore: number;
}

export interface SSEConnectionConfig {
  url: string;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatTimeout?: number;
  enabled?: boolean;
}

export interface SSEMessage {
  type: string;
  data: any;
  timestamp: Date;
}

const DEFAULT_CONFIG: Required<Omit<SSEConnectionConfig, 'url'>> = {
  maxReconnectAttempts: 10,
  reconnectInterval: 5000,
  heartbeatTimeout: 60000,
  enabled: true
};

export function useSSEConnection(config: SSEConnectionConfig) {
  const [state, setState] = useState<SSEConnectionState>({
    connected: false,
    connecting: false,
    error: null,
    lastConnected: null,
    reconnectAttempts: 0,
    healthScore: 0
  });

  const [messages, setMessages] = useState<SSEMessage[]>([]);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const configRef = useRef({ ...DEFAULT_CONFIG, ...config });

  // Update config ref when config changes
  useEffect(() => {
    configRef.current = { ...DEFAULT_CONFIG, ...config };
  }, [config]);

  const clearTimeouts = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }
  }, []);

  const updateHealthScore = useCallback(async () => {
    try {
      const response = await fetch(`${configRef.current.url}/api/sse/status`);
      if (response.ok) {
        const data = await response.json();
        setState(prev => ({
          ...prev,
          healthScore: data.connection?.health_score || 0
        }));
      }
    } catch (error) {
      console.warn('Failed to fetch SSE health score:', error);
    }
  }, []);

  const startHeartbeatMonitoring = useCallback(() => {
    clearTimeouts();
    
    heartbeatTimeoutRef.current = setTimeout(() => {
      console.warn('[SSE] Heartbeat timeout - connection may be stale');
      setState(prev => ({
        ...prev,
        error: 'Connection timeout - no heartbeat received'
      }));
      
      // Trigger reconnection
      disconnect();
      scheduleReconnect();
    }, configRef.current.heartbeatTimeout);
  }, []);

  const resetHeartbeat = useCallback(() => {
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      startHeartbeatMonitoring();
    }
  }, [startHeartbeatMonitoring]);

  const scheduleReconnect = useCallback(() => {
    if (state.reconnectAttempts >= configRef.current.maxReconnectAttempts) {
      console.error('[SSE] Max reconnection attempts reached');
      setState(prev => ({
        ...prev,
        error: `Max reconnection attempts (${configRef.current.maxReconnectAttempts}) reached`
      }));
      return;
    }

    const delay = Math.min(
      configRef.current.reconnectInterval * Math.pow(2, state.reconnectAttempts),
      30000 // Max 30 seconds
    );

    console.log(`[SSE] Scheduling reconnection in ${delay}ms (attempt ${state.reconnectAttempts + 1})`);

    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [state.reconnectAttempts]);

  const connect = useCallback(() => {
    if (!configRef.current.enabled) {
      console.log('[SSE] Connection disabled');
      return;
    }

    if (eventSourceRef.current) {
      console.log('[SSE] Closing existing connection');
      eventSourceRef.current.close();
    }

    setState(prev => ({
      ...prev,
      connecting: true,
      error: null
    }));

    try {
      console.log(`[SSE] Connecting to ${configRef.current.url}`);
      
      const eventSource = new EventSource(configRef.current.url);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('[SSE] Connection opened');
        setState(prev => ({
          ...prev,
          connected: true,
          connecting: false,
          error: null,
          lastConnected: new Date(),
          reconnectAttempts: 0
        }));
        
        startHeartbeatMonitoring();
        updateHealthScore();
      };

      eventSource.onmessage = (event) => {
        console.log('[SSE] Message received:', event.data);
        
        resetHeartbeat();
        
        try {
          const data = JSON.parse(event.data);
          const message: SSEMessage = {
            type: 'message',
            data,
            timestamp: new Date()
          };
          
          setMessages(prev => [...prev.slice(-99), message]); // Keep last 100 messages
        } catch (error) {
          console.warn('[SSE] Failed to parse message:', event.data);
        }
      };

      eventSource.onerror = (error) => {
        console.error('[SSE] Connection error:', error);
        
        setState(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          error: 'Connection error',
          reconnectAttempts: prev.reconnectAttempts + 1
        }));

        clearTimeouts();
        eventSource.close();
        
        scheduleReconnect();
      };

      // Handle custom events
      eventSource.addEventListener('heartbeat', () => {
        resetHeartbeat();
      });

      eventSource.addEventListener('endpoint', (event) => {
        console.log('[SSE] Endpoint event:', event.data);
      });

    } catch (error) {
      console.error('[SSE] Failed to create connection:', error);
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        reconnectAttempts: prev.reconnectAttempts + 1
      }));
      
      scheduleReconnect();
    }
  }, [startHeartbeatMonitoring, resetHeartbeat, scheduleReconnect, updateHealthScore]);

  const disconnect = useCallback(() => {
    console.log('[SSE] Disconnecting');
    
    clearTimeouts();
    
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    setState(prev => ({
      ...prev,
      connected: false,
      connecting: false
    }));
  }, [clearTimeouts]);

  const reconnect = useCallback(async () => {
    console.log('[SSE] Manual reconnection requested');
    
    // Reset reconnection attempts
    setState(prev => ({
      ...prev,
      reconnectAttempts: 0,
      error: null
    }));
    
    disconnect();
    
    // Try to trigger server-side reconnection
    try {
      await fetch(`${configRef.current.url}/api/sse/reconnect`, {
        method: 'POST'
      });
    } catch (error) {
      console.warn('[SSE] Failed to trigger server reconnection:', error);
    }
    
    // Wait a moment then reconnect
    setTimeout(connect, 1000);
  }, [disconnect, connect]);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (configRef.current.enabled) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimeouts();
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [clearTimeouts]);

  return {
    state,
    messages,
    connect,
    disconnect,
    reconnect,
    clearMessages,
    isEnabled: configRef.current.enabled
  };
}
