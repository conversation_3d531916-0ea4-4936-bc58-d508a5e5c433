"""
Integration Configuration System for Uru Workspace Platform
Manages multiple Composio integrations with white-labeled OAuth flows
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

class AuthType(Enum):
    OAUTH2 = "OAUTH2"
    API_KEY = "API_KEY"
    BEARER_TOKEN = "BEARER_TOKEN"
    OAUTH1 = "OAUTH1"

@dataclass
class OAuthConfig:
    """OAuth configuration for a specific integration"""
    client_id_env: str
    client_secret_env: str
    authorization_url: str
    token_url: str
    scopes: List[str]
    redirect_uri_path: str = "/api/uru/composio/oauth/callback"

@dataclass
class ApiKeyConfig:
    """API Key configuration for a specific integration"""
    api_key_env: str
    header_name: str = "Authorization"
    header_prefix: str = "Bearer"

@dataclass
class IntegrationConfig:
    """Complete configuration for a Composio integration"""
    composio_app_name: str
    display_name: str
    description: str
    category: str
    auth_type: AuthType
    tier: int
    capabilities: List[str]
    logo_url: str
    oauth_config: Optional[OAuthConfig] = None
    api_key_config: Optional[ApiKeyConfig] = None
    documentation_url: Optional[str] = None
    is_enabled: bool = True

class IntegrationRegistry:
    """Registry of all supported integrations with their configurations"""
    
    def __init__(self):
        self._integrations: Dict[str, IntegrationConfig] = {}
        self._initialize_integrations()
    
    def _initialize_integrations(self):
        """Initialize all supported integrations"""
        
        # Tier 1: Essential Business Communication & Collaboration
        self._integrations.update({
            "slack": IntegrationConfig(
                composio_app_name="slack",
                display_name="Slack",
                description="Team communication, channel management, file sharing",
                category="Communication",
                auth_type=AuthType.OAUTH2,
                tier=1,
                capabilities=["send_message", "list_channels", "upload_file", "read_messages"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg",
                oauth_config=OAuthConfig(
                    client_id_env="SLACK_CLIENT_ID",
                    client_secret_env="SLACK_CLIENT_SECRET",
                    authorization_url="https://slack.com/oauth/v2/authorize",
                    token_url="https://slack.com/api/oauth.v2.access",
                    scopes=["channels:read", "chat:write", "files:read", "files:write"]
                )
            ),
            
            "microsoft_teams": IntegrationConfig(
                composio_app_name="microsoft_teams",
                display_name="Microsoft Teams",
                description="Enterprise communication, meetings, collaboration",
                category="Communication",
                auth_type=AuthType.OAUTH2,
                tier=1,
                capabilities=["send_message", "list_teams", "create_meeting", "manage_channels"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/microsoft-teams-logo.jpeg",
                oauth_config=OAuthConfig(
                    client_id_env="MICROSOFT_CLIENT_ID",
                    client_secret_env="MICROSOFT_CLIENT_SECRET",
                    authorization_url="https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
                    token_url="https://login.microsoftonline.com/common/oauth2/v2.0/token",
                    scopes=["https://graph.microsoft.com/Chat.ReadWrite", "https://graph.microsoft.com/Team.ReadBasic.All"]
                )
            ),
            
            "notion": IntegrationConfig(
                composio_app_name="notion",
                display_name="Notion",
                description="Knowledge management, documentation, project planning",
                category="Productivity",
                auth_type=AuthType.OAUTH2,
                tier=1,
                capabilities=["create_page", "update_page", "search_pages", "manage_databases"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg",
                oauth_config=OAuthConfig(
                    client_id_env="NOTION_CLIENT_ID",
                    client_secret_env="NOTION_CLIENT_SECRET",
                    authorization_url="https://api.notion.com/v1/oauth/authorize",
                    token_url="https://api.notion.com/v1/oauth/token",
                    scopes=["read", "write"]
                )
            ),
            
            "airtable": IntegrationConfig(
                composio_app_name="airtable",
                display_name="Airtable",
                description="Database management, project tracking, workflow automation",
                category="Productivity",
                auth_type=AuthType.OAUTH2,
                tier=1,
                capabilities=["create_record", "update_record", "list_records", "manage_tables"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/airtable.svg",
                oauth_config=OAuthConfig(
                    client_id_env="AIRTABLE_CLIENT_ID",
                    client_secret_env="AIRTABLE_CLIENT_SECRET",
                    authorization_url="https://airtable.com/oauth2/v1/authorize",
                    token_url="https://airtable.com/oauth2/v1/token",
                    scopes=["data.records:read", "data.records:write", "schema.bases:read"]
                )
            ),
            
            "asana": IntegrationConfig(
                composio_app_name="asana",
                display_name="Asana",
                description="Project management, task tracking, team coordination",
                category="Project Management",
                auth_type=AuthType.OAUTH2,
                tier=1,
                capabilities=["create_task", "update_task", "list_projects", "manage_teams"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/asana.png",
                oauth_config=OAuthConfig(
                    client_id_env="ASANA_CLIENT_ID",
                    client_secret_env="ASANA_CLIENT_SECRET",
                    authorization_url="https://app.asana.com/-/oauth_authorize",
                    token_url="https://app.asana.com/-/oauth_token",
                    scopes=["default"]
                )
            )
        })
        
        # Tier 2: CRM & Sales Productivity
        self._integrations.update({
            "hubspot": IntegrationConfig(
                composio_app_name="hubspot",
                display_name="HubSpot",
                description="CRM, marketing automation, sales pipeline management",
                category="CRM",
                auth_type=AuthType.OAUTH2,
                tier=2,
                capabilities=["manage_contacts", "track_deals", "create_companies", "send_emails"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/hubspot.webp",
                oauth_config=OAuthConfig(
                    client_id_env="HUBSPOT_CLIENT_ID",
                    client_secret_env="HUBSPOT_CLIENT_SECRET",
                    authorization_url="https://app.hubspot.com/oauth/authorize",
                    token_url="https://api.hubapi.com/oauth/v1/token",
                    scopes=["crm.objects.contacts.read", "crm.objects.deals.read", "crm.objects.companies.read"]
                )
            ),

            "salesforce": IntegrationConfig(
                composio_app_name="salesforce",
                display_name="Salesforce",
                description="Enterprise CRM, lead management, sales analytics",
                category="CRM",
                auth_type=AuthType.OAUTH2,
                tier=2,
                capabilities=["manage_leads", "track_opportunities", "create_accounts", "run_reports"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/salesforce.svg",
                oauth_config=OAuthConfig(
                    client_id_env="SALESFORCE_CLIENT_ID",
                    client_secret_env="SALESFORCE_CLIENT_SECRET",
                    authorization_url="https://login.salesforce.com/services/oauth2/authorize",
                    token_url="https://login.salesforce.com/services/oauth2/token",
                    scopes=["api", "refresh_token", "offline_access"]
                )
            ),

            "pipedrive": IntegrationConfig(
                composio_app_name="pipedrive",
                display_name="Pipedrive",
                description="Sales pipeline management, deal tracking",
                category="CRM",
                auth_type=AuthType.OAUTH2,
                tier=2,
                capabilities=["manage_deals", "track_pipeline", "create_activities", "manage_contacts"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pipedrive.svg",
                oauth_config=OAuthConfig(
                    client_id_env="PIPEDRIVE_CLIENT_ID",
                    client_secret_env="PIPEDRIVE_CLIENT_SECRET",
                    authorization_url="https://oauth.pipedrive.com/oauth/authorize",
                    token_url="https://oauth.pipedrive.com/oauth/token",
                    scopes=["base"]
                )
            ),

            "intercom": IntegrationConfig(
                composio_app_name="intercom",
                display_name="Intercom",
                description="Customer support, live chat, user engagement",
                category="Customer Support",
                auth_type=AuthType.OAUTH2,
                tier=2,
                capabilities=["send_messages", "manage_conversations", "track_users", "create_articles"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/intercom.svg",
                oauth_config=OAuthConfig(
                    client_id_env="INTERCOM_CLIENT_ID",
                    client_secret_env="INTERCOM_CLIENT_SECRET",
                    authorization_url="https://app.intercom.com/oauth",
                    token_url="https://api.intercom.io/auth/eagle/token",
                    scopes=["read_conversations", "write_conversations", "read_users", "write_users"]
                )
            ),

            "zendesk": IntegrationConfig(
                composio_app_name="zendesk",
                display_name="Zendesk",
                description="Customer support ticketing, knowledge base",
                category="Customer Support",
                auth_type=AuthType.OAUTH2,
                tier=2,
                capabilities=["manage_tickets", "create_articles", "track_users", "generate_reports"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zendesk.svg",
                oauth_config=OAuthConfig(
                    client_id_env="ZENDESK_CLIENT_ID",
                    client_secret_env="ZENDESK_CLIENT_SECRET",
                    authorization_url="https://{subdomain}.zendesk.com/oauth/authorizations/new",
                    token_url="https://{subdomain}.zendesk.com/oauth/tokens",
                    scopes=["read", "write"]
                )
            )
        })

        # Tier 3: Document & File Management
        self._integrations.update({
            "dropbox": IntegrationConfig(
                composio_app_name="dropbox",
                display_name="Dropbox",
                description="File storage, sharing, collaboration",
                category="File Management",
                auth_type=AuthType.OAUTH2,
                tier=3,
                capabilities=["upload_files", "download_files", "share_files", "manage_folders"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/dropbox.svg",
                oauth_config=OAuthConfig(
                    client_id_env="DROPBOX_CLIENT_ID",
                    client_secret_env="DROPBOX_CLIENT_SECRET",
                    authorization_url="https://www.dropbox.com/oauth2/authorize",
                    token_url="https://api.dropboxapi.com/oauth2/token",
                    scopes=["files.content.read", "files.content.write", "files.metadata.read"]
                )
            ),

            "one_drive": IntegrationConfig(
                composio_app_name="one_drive",
                display_name="OneDrive",
                description="Microsoft cloud storage, document collaboration",
                category="File Management",
                auth_type=AuthType.OAUTH2,
                tier=3,
                capabilities=["upload_files", "download_files", "share_files", "manage_folders"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/one-drive.svg",
                oauth_config=OAuthConfig(
                    client_id_env="MICROSOFT_CLIENT_ID",
                    client_secret_env="MICROSOFT_CLIENT_SECRET",
                    authorization_url="https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
                    token_url="https://login.microsoftonline.com/common/oauth2/v2.0/token",
                    scopes=["https://graph.microsoft.com/Files.ReadWrite", "https://graph.microsoft.com/Sites.ReadWrite.All"]
                )
            ),

            "googledocs": IntegrationConfig(
                composio_app_name="googledocs",
                display_name="Google Docs",
                description="Document creation, real-time collaboration",
                category="File Management",
                auth_type=AuthType.OAUTH2,
                tier=3,
                capabilities=["create_document", "edit_document", "share_document", "export_document"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-docs.svg",
                oauth_config=OAuthConfig(
                    client_id_env="GOOGLE_CLIENT_ID",
                    client_secret_env="GOOGLE_CLIENT_SECRET",
                    authorization_url="https://accounts.google.com/o/oauth2/v2/auth",
                    token_url="https://oauth2.googleapis.com/token",
                    scopes=["https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/drive.file"]
                )
            ),

            "googlesheets": IntegrationConfig(
                composio_app_name="googlesheets",
                display_name="Google Sheets",
                description="Spreadsheet management, data analysis",
                category="File Management",
                auth_type=AuthType.OAUTH2,
                tier=3,
                capabilities=["create_spreadsheet", "edit_spreadsheet", "read_data", "write_data"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg",
                oauth_config=OAuthConfig(
                    client_id_env="GOOGLE_CLIENT_ID",
                    client_secret_env="GOOGLE_CLIENT_SECRET",
                    authorization_url="https://accounts.google.com/o/oauth2/v2/auth",
                    token_url="https://oauth2.googleapis.com/token",
                    scopes=["https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/drive.file"]
                )
            )
        })

        # Tier 4: Specialized Business Tools
        self._integrations.update({
            "calendly": IntegrationConfig(
                composio_app_name="calendly",
                display_name="Calendly",
                description="Meeting scheduling, appointment booking",
                category="Scheduling",
                auth_type=AuthType.OAUTH2,
                tier=4,
                capabilities=["schedule_meeting", "list_events", "manage_availability", "send_invites"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/calendly.svg",
                oauth_config=OAuthConfig(
                    client_id_env="CALENDLY_CLIENT_ID",
                    client_secret_env="CALENDLY_CLIENT_SECRET",
                    authorization_url="https://auth.calendly.com/oauth/authorize",
                    token_url="https://auth.calendly.com/oauth/token",
                    scopes=["default"]
                )
            ),

            "zoom": IntegrationConfig(
                composio_app_name="zoom",
                display_name="Zoom",
                description="Video conferencing, webinars, recordings",
                category="Communication",
                auth_type=AuthType.OAUTH2,
                tier=4,
                capabilities=["create_meeting", "start_meeting", "manage_webinars", "access_recordings"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zoom.svg",
                oauth_config=OAuthConfig(
                    client_id_env="ZOOM_CLIENT_ID",
                    client_secret_env="ZOOM_CLIENT_SECRET",
                    authorization_url="https://zoom.us/oauth/authorize",
                    token_url="https://zoom.us/oauth/token",
                    scopes=["meeting:write", "meeting:read", "webinar:write"]
                )
            ),

            "trello": IntegrationConfig(
                composio_app_name="trello",
                display_name="Trello",
                description="Kanban-style project management",
                category="Project Management",
                auth_type=AuthType.OAUTH1,
                tier=4,
                capabilities=["create_card", "update_card", "manage_boards", "track_progress"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/trello.svg",
                oauth_config=OAuthConfig(
                    client_id_env="TRELLO_CLIENT_ID",
                    client_secret_env="TRELLO_CLIENT_SECRET",
                    authorization_url="https://trello.com/1/OAuthAuthorizeToken",
                    token_url="https://trello.com/1/OAuthGetAccessToken",
                    scopes=["read", "write"]
                )
            ),

            "clickup": IntegrationConfig(
                composio_app_name="clickup",
                display_name="ClickUp",
                description="All-in-one workspace, project management",
                category="Project Management",
                auth_type=AuthType.OAUTH2,
                tier=4,
                capabilities=["create_task", "update_task", "manage_projects", "track_time"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/clickup.png",
                oauth_config=OAuthConfig(
                    client_id_env="CLICKUP_CLIENT_ID",
                    client_secret_env="CLICKUP_CLIENT_SECRET",
                    authorization_url="https://app.clickup.com/api/v2/oauth/token",
                    token_url="https://api.clickup.com/api/v2/oauth/token",
                    scopes=["default"]
                )
            ),

            "linear": IntegrationConfig(
                composio_app_name="linear",
                display_name="Linear",
                description="Issue tracking, project management for tech teams",
                category="Project Management",
                auth_type=AuthType.OAUTH2,
                tier=4,
                capabilities=["create_issue", "update_issue", "manage_projects", "track_progress"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linear.png",
                oauth_config=OAuthConfig(
                    client_id_env="LINEAR_CLIENT_ID",
                    client_secret_env="LINEAR_CLIENT_SECRET",
                    authorization_url="https://linear.app/oauth/authorize",
                    token_url="https://api.linear.app/oauth/token",
                    scopes=["read", "write"]
                )
            )
        })

        # Tier 5: Marketing & Analytics
        self._integrations.update({
            "mailchimp": IntegrationConfig(
                composio_app_name="mailchimp",
                display_name="Mailchimp",
                description="Email marketing, automation campaigns",
                category="Marketing",
                auth_type=AuthType.OAUTH2,
                tier=5,
                capabilities=["create_campaign", "manage_lists", "send_emails", "track_analytics"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mailchimp.svg",
                oauth_config=OAuthConfig(
                    client_id_env="MAILCHIMP_CLIENT_ID",
                    client_secret_env="MAILCHIMP_CLIENT_SECRET",
                    authorization_url="https://login.mailchimp.com/oauth2/authorize",
                    token_url="https://login.mailchimp.com/oauth2/token",
                    scopes=["default"]
                )
            ),

            "canva": IntegrationConfig(
                composio_app_name="canva",
                display_name="Canva",
                description="Design creation, marketing materials",
                category="Design",
                auth_type=AuthType.OAUTH2,
                tier=5,
                capabilities=["create_design", "edit_design", "export_design", "manage_templates"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/canva.jpeg",
                oauth_config=OAuthConfig(
                    client_id_env="CANVA_CLIENT_ID",
                    client_secret_env="CANVA_CLIENT_SECRET",
                    authorization_url="https://www.canva.com/api/oauth/authorize",
                    token_url="https://api.canva.com/rest/v1/oauth/token",
                    scopes=["design:read", "design:write"]
                )
            ),

            "twitter": IntegrationConfig(
                composio_app_name="twitter",
                display_name="Twitter",
                description="Social media management, content posting",
                category="Social Media",
                auth_type=AuthType.OAUTH2,
                tier=5,
                capabilities=["post_tweet", "read_timeline", "manage_followers", "track_mentions"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/twitter.png",
                oauth_config=OAuthConfig(
                    client_id_env="TWITTER_CLIENT_ID",
                    client_secret_env="TWITTER_CLIENT_SECRET",
                    authorization_url="https://twitter.com/i/oauth2/authorize",
                    token_url="https://api.twitter.com/2/oauth2/token",
                    scopes=["tweet.read", "tweet.write", "users.read"]
                )
            ),

            "linkedin": IntegrationConfig(
                composio_app_name="linkedin",
                display_name="LinkedIn",
                description="Professional networking, content sharing",
                category="Social Media",
                auth_type=AuthType.OAUTH2,
                tier=5,
                capabilities=["post_content", "read_profile", "manage_connections", "track_analytics"],
                logo_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linkedin.svg",
                oauth_config=OAuthConfig(
                    client_id_env="LINKEDIN_CLIENT_ID",
                    client_secret_env="LINKEDIN_CLIENT_SECRET",
                    authorization_url="https://www.linkedin.com/oauth/v2/authorization",
                    token_url="https://www.linkedin.com/oauth/v2/accessToken",
                    scopes=["r_liteprofile", "r_emailaddress", "w_member_social"]
                )
            )
        })
    
    def get_integration(self, composio_app_name: str) -> Optional[IntegrationConfig]:
        """Get integration configuration by Composio app name"""
        return self._integrations.get(composio_app_name)
    
    def get_all_integrations(self) -> Dict[str, IntegrationConfig]:
        """Get all integration configurations"""
        return self._integrations.copy()
    
    def get_integrations_by_category(self, category: str) -> Dict[str, IntegrationConfig]:
        """Get integrations filtered by category"""
        return {
            name: config for name, config in self._integrations.items()
            if config.category == category
        }
    
    def get_integrations_by_tier(self, tier: int) -> Dict[str, IntegrationConfig]:
        """Get integrations filtered by tier"""
        return {
            name: config for name, config in self._integrations.items()
            if config.tier == tier
        }
    
    def get_enabled_integrations(self) -> Dict[str, IntegrationConfig]:
        """Get only enabled integrations"""
        return {
            name: config for name, config in self._integrations.items()
            if config.is_enabled
        }
    
    def get_oauth_integrations(self) -> Dict[str, IntegrationConfig]:
        """Get integrations that use OAuth2 authentication"""
        return {
            name: config for name, config in self._integrations.items()
            if config.auth_type == AuthType.OAUTH2 and config.oauth_config is not None
        }
    
    def get_categories(self) -> List[str]:
        """Get all unique categories"""
        return list(set(config.category for config in self._integrations.values()))

# Global registry instance
integration_registry = IntegrationRegistry()
