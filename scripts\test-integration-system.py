#!/usr/bin/env python3
"""
Test Integration System for Uru Workspace Platform
Comprehensive testing of the new multi-integration architecture
"""

import os
import sys
import json
import time
import logging
import requests
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegrationSystemTester:
    def __init__(self):
        # Service URLs
        self.auth_service_url = os.getenv("AUTH_SERVICE_URL", "http://localhost:8000")
        self.composio_service_url = os.getenv("COMPOSIO_SERVICE_URL", "http://localhost:8001")
        self.mcp_proxy_url = os.getenv("MCP_PROXY_URL", "http://localhost:3001")
        self.frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        
        # Test credentials
        self.test_email = os.getenv("TEST_EMAIL", "<EMAIL>")
        self.test_password = os.getenv("TEST_PASSWORD", "testpassword123")
        
        # Authentication token
        self.auth_token = None
        
        logger.info("🧪 Integration System Tester initialized")
        logger.info(f"📡 Auth Service: {self.auth_service_url}")
        logger.info(f"🧩 Composio Service: {self.composio_service_url}")
        logger.info(f"🔗 MCP Proxy: {self.mcp_proxy_url}")
        logger.info(f"🌐 Frontend: {self.frontend_url}")
    
    def test_service_health(self):
        """Test if all services are running and healthy"""
        logger.info("🏥 Testing service health...")
        
        services = [
            ("Auth Service", f"{self.auth_service_url}/health"),
            ("Composio Service", f"{self.composio_service_url}/health"),
            ("MCP Proxy", f"{self.mcp_proxy_url}/health"),
        ]
        
        all_healthy = True
        
        for service_name, health_url in services:
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    logger.info(f"✅ {service_name}: Healthy")
                else:
                    logger.error(f"❌ {service_name}: Unhealthy (status: {response.status_code})")
                    all_healthy = False
            except Exception as e:
                logger.error(f"❌ {service_name}: Connection failed - {e}")
                all_healthy = False
        
        return all_healthy
    
    def authenticate(self):
        """Authenticate with the auth service"""
        logger.info("🔐 Authenticating...")
        
        try:
            # Try to login with test credentials
            login_data = {
                "email": self.test_email,
                "password": self.test_password
            }
            
            response = requests.post(
                f"{self.auth_service_url}/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                self.auth_token = result.get("access_token")
                logger.info("✅ Authentication successful")
                return True
            else:
                logger.error(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False
    
    def test_available_integrations(self):
        """Test getting available integrations"""
        logger.info("📋 Testing available integrations endpoint...")
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            response = requests.get(
                f"{self.composio_service_url}/api/uru/integrations/available",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    categories = result.get("categories", {})
                    total = result.get("total_integrations", 0)
                    
                    logger.info(f"✅ Available integrations: {total} total")
                    for category, integrations in categories.items():
                        logger.info(f"   📂 {category}: {len(integrations)} integrations")
                    
                    return True
                else:
                    logger.error(f"❌ API returned success=false: {result}")
                    return False
            else:
                logger.error(f"❌ Failed to get integrations: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing integrations: {e}")
            return False
    
    def test_user_connections(self):
        """Test getting user's integration connections"""
        logger.info("🔗 Testing user connections endpoint...")
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            response = requests.get(
                f"{self.composio_service_url}/api/uru/integrations/connections",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    connections = result.get("connections", [])
                    total = result.get("total_connections", 0)
                    
                    logger.info(f"✅ User connections: {total} total")
                    for conn in connections:
                        status = "🟢 Connected" if conn.get("is_connected") else "🔴 Disconnected"
                        expired = " (⚠️ Expired)" if conn.get("is_expired") else ""
                        logger.info(f"   {status}{expired} {conn.get('name')} ({conn.get('category')})")
                    
                    return True
                else:
                    logger.error(f"❌ API returned success=false: {result}")
                    return False
            else:
                logger.error(f"❌ Failed to get connections: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing connections: {e}")
            return False
    
    def test_mcp_tools(self):
        """Test MCP proxy tools endpoint"""
        logger.info("🔧 Testing MCP tools endpoint...")
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            response = requests.get(
                f"{self.mcp_proxy_url}/api/tools",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                tools = response.json()
                logger.info(f"✅ MCP tools available: {len(tools)} total")
                
                # Group tools by type
                tool_types = {}
                for tool in tools:
                    tool_type = tool.get("type", "unknown")
                    if tool_type not in tool_types:
                        tool_types[tool_type] = []
                    tool_types[tool_type].append(tool.get("function", {}).get("name", "unnamed"))
                
                for tool_type, tool_names in tool_types.items():
                    logger.info(f"   🔧 {tool_type}: {len(tool_names)} tools")
                    for tool_name in tool_names[:3]:  # Show first 3 tools
                        logger.info(f"      - {tool_name}")
                    if len(tool_names) > 3:
                        logger.info(f"      ... and {len(tool_names) - 3} more")
                
                return True
            else:
                logger.error(f"❌ Failed to get MCP tools: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing MCP tools: {e}")
            return False
    
    def test_integration_execution(self):
        """Test executing a mock integration tool"""
        logger.info("⚡ Testing integration tool execution...")
        
        try:
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json"
            }
            
            # Test with a mock tool execution
            test_payload = {
                "tool_name": "slack_send_message",
                "parameters": {
                    "channel": "#general",
                    "message": "Test message from Uru integration system"
                }
            }
            
            response = requests.post(
                f"{self.composio_service_url}/api/uru/integrations/execute",
                headers=headers,
                json=test_payload,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    logger.info("✅ Integration tool execution successful")
                    logger.info(f"   🔧 Tool: {result.get('tool_name')}")
                    logger.info(f"   🧩 Integration: {result.get('integration_id')}")
                    logger.info(f"   📊 Result: {json.dumps(result.get('result', {}), indent=2)}")
                    return True
                else:
                    logger.error(f"❌ Tool execution failed: {result}")
                    return False
            elif response.status_code == 404:
                logger.info("ℹ️  Integration not connected (expected for test)")
                return True  # This is expected if no integrations are connected
            else:
                logger.error(f"❌ Tool execution failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing tool execution: {e}")
            return False
    
    def test_frontend_integration_hub(self):
        """Test if the frontend integration hub is accessible"""
        logger.info("🌐 Testing frontend integration hub...")
        
        try:
            response = requests.get(f"{self.frontend_url}/app/settings", timeout=10)
            
            if response.status_code == 200:
                logger.info("✅ Frontend settings page accessible")
                
                # Check if the page contains integration-related content
                content = response.text.lower()
                if "integration" in content or "composio" in content:
                    logger.info("✅ Integration content detected in frontend")
                    return True
                else:
                    logger.warning("⚠️  No integration content detected in frontend")
                    return True  # Still consider it a pass
            else:
                logger.error(f"❌ Frontend not accessible: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing frontend: {e}")
            return False
    
    def run_comprehensive_test(self):
        """Run all tests in sequence"""
        logger.info("🚀 Starting comprehensive integration system test...")
        
        test_results = []
        
        # Test 1: Service Health
        test_results.append(("Service Health", self.test_service_health()))
        
        # Test 2: Authentication
        if test_results[-1][1]:  # Only if services are healthy
            test_results.append(("Authentication", self.authenticate()))
        else:
            logger.error("❌ Skipping remaining tests due to service health issues")
            return False
        
        # Test 3: Available Integrations
        if test_results[-1][1]:  # Only if authenticated
            test_results.append(("Available Integrations", self.test_available_integrations()))
        
        # Test 4: User Connections
        if self.auth_token:
            test_results.append(("User Connections", self.test_user_connections()))
        
        # Test 5: MCP Tools
        if self.auth_token:
            test_results.append(("MCP Tools", self.test_mcp_tools()))
        
        # Test 6: Integration Execution
        if self.auth_token:
            test_results.append(("Integration Execution", self.test_integration_execution()))
        
        # Test 7: Frontend Integration Hub
        test_results.append(("Frontend Integration Hub", self.test_frontend_integration_hub()))
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info("📊 TEST SUMMARY")
        logger.info("="*60)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{status} {test_name}")
            if result:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📈 Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            logger.info("🎉 All tests passed! Integration system is working correctly.")
            return True
        else:
            logger.error(f"❌ {total - passed} tests failed. Please check the issues above.")
            return False

def main():
    """Main function"""
    logger.info("🧪 Uru Integration System Test Suite")
    logger.info("="*60)
    
    tester = IntegrationSystemTester()
    success = tester.run_comprehensive_test()
    
    if success:
        logger.info("✅ Integration system test completed successfully")
        sys.exit(0)
    else:
        logger.error("❌ Integration system test failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
