'use client';

// components/permissions/PermissionManagement.tsx

import React, { useState } from 'react';
import { Users, Shield, CheckCircle, X } from 'lucide-react';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'member' | 'viewer';
  avatar: string;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

interface AppPermissions {
  app: string;
  icon: string;
  color: string;
  permissions: Permission[];
}

export const PermissionManagement: React.FC = () => {
  const [selectedMember, setSelectedMember] = useState<string>('jackson-moss');

  const teamMembers: TeamMember[] = [
    { id: 'jackson-moss', name: '<PERSON>', email: '<EMAIL>', role: 'member', avatar: '<PERSON><PERSON>' },
    { id: 'andrew-moss', name: '<PERSON>', email: '<EMAIL>', role: 'admin', avatar: '<PERSON>' },
    { id: 'craig-<PERSON><PERSON>', name: '<PERSON>', email: '<EMAIL>', role: 'member', avatar: 'C<PERSON>' },
    { id: 'carol-fernando', name: '<PERSON>', email: '<EMAIL>', role: 'viewer', avatar: 'CF' }
  ];

  const [appPermissions, setAppPermissions] = useState<AppPermissions[]>([
    {
      app: 'Google Drive',
      icon: '📁',
      color: 'bg-blue-500',
      permissions: [
        { id: 'read-files', name: 'Read Files', description: 'View and search documents', enabled: true },
        { id: 'create-files', name: 'Create Files', description: 'Create new documents and folders', enabled: true },
        { id: 'delete-files', name: 'Delete Files', description: 'Delete documents and folders', enabled: false }
      ]
    },
    {
      app: 'Gmail',
      icon: '📧',
      color: 'bg-red-500',
      permissions: [
        { id: 'read-emails', name: 'Read Emails', description: 'Search and analyze email content', enabled: true },
        { id: 'send-emails', name: 'Send Emails', description: 'Send emails through AI assistant', enabled: true },
        { id: 'manage-labels', name: 'Manage Labels', description: 'Create and modify email labels', enabled: false }
      ]
    },
    {
      app: 'Google Calendar',
      icon: '📅',
      color: 'bg-blue-600',
      permissions: [
        { id: 'view-events', name: 'View Events', description: 'See calendar events and schedules', enabled: true },
        { id: 'create-events', name: 'Create Events', description: 'Schedule meetings and appointments', enabled: true },
        { id: 'modify-events', name: 'Modify Events', description: 'Edit existing calendar events', enabled: false }
      ]
    }
  ]);

  const selectedMemberData = teamMembers.find(member => member.id === selectedMember);

  const togglePermission = (appIndex: number, permissionId: string) => {
    setAppPermissions(prev => prev.map((app, index) => 
      index === appIndex 
        ? {
            ...app,
            permissions: app.permissions.map(permission =>
              permission.id === permissionId
                ? { ...permission, enabled: !permission.enabled }
                : permission
            )
          }
        : app
    ));
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'member': return 'bg-blue-100 text-blue-800';
      case 'viewer': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-gray-900 min-h-screen">
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-white mb-2">Permission Management</h1>
          <p className="text-gray-400">Control team member access to apps and data sources</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Team Member Selector */}
          <div className="bg-gray-800 rounded-xl p-6">
            <div className="flex items-center space-x-2 mb-6">
              <Users className="w-5 h-5 text-purple-400" />
              <h2 className="text-lg font-semibold text-white">Select Team Member</h2>
            </div>
            
            <div className="space-y-3">
              {teamMembers.map((member) => (
                <button
                  key={member.id}
                  onClick={() => setSelectedMember(member.id)}
                  className={`w-full p-4 rounded-lg text-left transition-all ${
                    selectedMember === member.id
                      ? 'bg-blue-600 border-blue-500'
                      : 'bg-gray-700 hover:bg-gray-600 border-transparent'
                  } border`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {member.avatar}
                    </div>
                    <div className="flex-1">
                      <p className="text-white font-medium">{member.name}</p>
                      <p className="text-gray-400 text-sm">{member.email}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(member.role)}`}>
                      {member.role === 'admin' ? 'Admin' : member.role === 'member' ? 'Member' : 'Viewer'}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Permissions Panel */}
          <div className="lg:col-span-2">
            {selectedMemberData && (
              <div className="bg-gray-800 rounded-xl p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <Shield className="w-5 h-5 text-orange-400" />
                    <h2 className="text-lg font-semibold text-white">
                      Permissions for {selectedMemberData.name}
                    </h2>
                  </div>
                  <div className="flex space-x-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(selectedMemberData.role)}`}>
                      {selectedMemberData.role === 'admin' ? 'Admin' : selectedMemberData.role === 'member' ? 'Member' : 'Viewer'}
                    </span>
                  </div>
                </div>

                <div className="space-y-6">
                  {appPermissions.map((app, appIndex) => (
                    <div key={app.app} className="border border-gray-700 rounded-lg p-6">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className={`w-10 h-10 ${app.color} rounded-lg flex items-center justify-center text-white text-lg`}>
                          {app.icon}
                        </div>
                        <h3 className="text-white font-semibold text-lg">{app.app} Access</h3>
                      </div>

                      <div className="space-y-4">
                        {app.permissions.map((permission) => (
                          <div key={permission.id} className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                            <div className="flex-1">
                              <h4 className="text-white font-medium mb-1">{permission.name}</h4>
                              <p className="text-gray-400 text-sm">{permission.description}</p>
                            </div>
                            
                            <button
                              onClick={() => togglePermission(appIndex, permission.id)}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 ${
                                permission.enabled ? 'bg-green-500' : 'bg-gray-600'
                              }`}
                            >
                              <span
                                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                  permission.enabled ? 'translate-x-6' : 'translate-x-1'
                                }`}
                              />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-8 pt-6 border-t border-gray-700">
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-400">
                      Changes are saved automatically
                    </div>
                    <div className="flex items-center space-x-2 text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">All changes saved</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};