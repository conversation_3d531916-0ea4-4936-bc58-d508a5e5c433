{"name": "uru-workspace-platform", "version": "1.0.0", "description": "Multi-tenant workspace platform with per-employee OAuth and MCP integration", "main": "index.js", "scripts": {"start:prod": "docker-compose up --build", "start:prod:detached": "docker-compose up --build -d", "start:dev": "docker-compose -f docker-compose.dev.yml up --build", "start:dev:detached": "docker-compose -f docker-compose.dev.yml up --build -d", "stop:prod": "docker-compose down", "stop:dev": "docker-compose -f docker-compose.dev.yml down", "logs:prod": "docker-compose logs -f", "logs:dev": "docker-compose -f docker-compose.dev.yml logs -f", "test:new-architecture": "node test-new-architecture.js", "migrate:setup": "cd scripts && python setup-migration-env.py", "migrate:fix-schema": "cd scripts && python fix-oauth-tokens-schema.py", "migrate:oauth": "cd scripts && python migrate-oauth-storage.py", "migrate:test": "cd scripts && python test-oauth-migration.py", "migrate:rollback": "cd scripts && python rollback-oauth-migration.py", "clean": "docker-compose down --volumes --remove-orphans && docker system prune -f", "build:all": "docker-compose build", "build:frontend": "cd frontend && npm run build", "build:auth": "docker build -t uru-auth-service ./auth-service", "build:integrations": "docker build -t uru-integration-service ./integration-service", "build:composio": "docker build -t uru-composio-service ./composio-service", "build:mcp": "docker build -t uru-mcp-proxy ./mcp-proxy"}, "keywords": ["workspace", "o<PERSON>h", "mcp", "multi-tenant", "nextjs", "<PERSON><PERSON><PERSON>", "docker"], "author": "Uru Enterprises", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/uruenterprises/uru-workspace-platform.git"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"axios": "^1.10.0"}}