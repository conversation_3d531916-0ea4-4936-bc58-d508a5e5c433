# ===========================================
# COMPREHENSIVE MONITORING & ALERTING SETUP
# ===========================================

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
import time
import psutil
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging
import json

# ===========================================
# METRICS COLLECTION
# ===========================================

class MetricsCollector:
    """Collect application metrics for monitoring"""
    
    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.response_times = []
        self.active_connections = 0
        self.oauth_operations = {
            'successful': 0,
            'failed': 0,
            'token_refreshes': 0
        }
        self.tool_executions = {
            'successful': 0,
            'failed': 0,
            'total_time': 0
        }
        self.rate_limit_hits = 0
        self.workspace_activity = {}
    
    def record_request(self, method: str, path: str, status_code: int, response_time: float):
        """Record request metrics"""
        self.request_count += 1
        self.response_times.append(response_time)
        
        # Keep only last 1000 response times
        if len(self.response_times) > 1000:
            self.response_times = self.response_times[-1000:]
        
        if status_code >= 400:
            self.error_count += 1
    
    def record_oauth_operation(self, operation: str, success: bool, workspace_id: str = None):
        """Record OAuth operation metrics"""
        if success:
            self.oauth_operations['successful'] += 1
        else:
            self.oauth_operations['failed'] += 1
        
        if operation == 'token_refresh':
            self.oauth_operations['token_refreshes'] += 1
        
        if workspace_id:
            if workspace_id not in self.workspace_activity:
                self.workspace_activity[workspace_id] = {'oauth_ops': 0, 'tool_execs': 0}
            self.workspace_activity[workspace_id]['oauth_ops'] += 1
    
    def record_tool_execution(self, success: bool, execution_time: float, workspace_id: str = None):
        """Record tool execution metrics"""
        if success:
            self.tool_executions['successful'] += 1
        else:
            self.tool_executions['failed'] += 1
        
        self.tool_executions['total_time'] += execution_time
        
        if workspace_id:
            if workspace_id not in self.workspace_activity:
                self.workspace_activity[workspace_id] = {'oauth_ops': 0, 'tool_execs': 0}
            self.workspace_activity[workspace_id]['tool_execs'] += 1
    
    def record_rate_limit_hit(self):
        """Record rate limit hit"""
        self.rate_limit_hits += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics snapshot"""
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        
        # System metrics
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'requests': {
                'total': self.request_count,
                'errors': self.error_count,
                'error_rate': self.error_count / max(self.request_count, 1),
                'avg_response_time': avg_response_time,
                'rate_limit_hits': self.rate_limit_hits
            },
            'oauth': self.oauth_operations,
            'tool_executions': self.tool_executions,
            'workspace_activity': self.workspace_activity,
            'system': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_percent': disk.percent,
                'disk_free_gb': disk.free / (1024**3)
            },
            'active_connections': self.active_connections
        }

# Global metrics collector
metrics = MetricsCollector()

# ===========================================
# METRICS MIDDLEWARE
# ===========================================

class MetricsMiddleware:
    """Middleware to collect request metrics"""
    
    def __init__(self, app: FastAPI):
        self.app = app
    
    async def __call__(self, request: Request, call_next):
        start_time = time.time()
        
        # Increment active connections
        metrics.active_connections += 1
        
        try:
            response = await call_next(request)
            
            # Record metrics
            response_time = time.time() - start_time
            metrics.record_request(
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                response_time=response_time
            )
            
            return response
            
        except Exception as e:
            # Record error
            response_time = time.time() - start_time
            metrics.record_request(
                method=request.method,
                path=request.url.path,
                status_code=500,
                response_time=response_time
            )
            raise
        finally:
            # Decrement active connections
            metrics.active_connections -= 1

# ===========================================
# HEALTH CHECK ENHANCEMENTS
# ===========================================

class HealthChecker:
    """Enhanced health checking with dependency validation"""
    
    def __init__(self):
        self.last_check = None
        self.cached_status = None
        self.cache_duration = 30  # seconds
    
    async def check_database_health(self, supabase_client) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            # Test basic connectivity
            result = supabase_client.table('employees').select('id').limit(1).execute()
            
            # Test write capability (audit log)
            test_log = {
                'employee_id': '00000000-0000-0000-0000-000000000000',
                'workspace_id': '00000000-0000-0000-0000-000000000000',
                'event_type': 'health_check',
                'event_details': {'test': True},
                'service': 'health-check',
                'timestamp': datetime.utcnow().isoformat()
            }
            supabase_client.table('audit_logs').insert(test_log).execute()
            
            response_time = time.time() - start_time
            
            return {
                'status': 'healthy',
                'response_time_ms': round(response_time * 1000, 2),
                'read_test': 'passed',
                'write_test': 'passed'
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'read_test': 'failed',
                'write_test': 'failed'
            }
    
    async def check_external_api_health(self, api_url: str, api_key: str, timeout: int = 5) -> Dict[str, Any]:
        """Check external API connectivity"""
        try:
            import httpx
            
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(
                    f"{api_url}/v1/apps",
                    headers={"x-api-key": api_key}
                )
            
            response_time = time.time() - start_time
            
            return {
                'status': 'healthy' if response.status_code == 200 else 'degraded',
                'status_code': response.status_code,
                'response_time_ms': round(response_time * 1000, 2)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def check_oauth_token_health(self, supabase_client) -> Dict[str, Any]:
        """Check OAuth token health across workspaces"""
        try:
            # Get token expiration stats
            result = supabase_client.table('oauth_tokens').select(
                'expires_at, provider, employee_id'
            ).execute()
            
            now = datetime.utcnow()
            total_tokens = len(result.data)
            expired_tokens = 0
            expiring_soon = 0  # Within 24 hours
            
            for token in result.data:
                if token.get('expires_at'):
                    try:
                        expires_at = datetime.fromisoformat(token['expires_at'].replace('Z', '+00:00'))
                        if expires_at < now:
                            expired_tokens += 1
                        elif expires_at < now + timedelta(hours=24):
                            expiring_soon += 1
                    except:
                        expired_tokens += 1  # Treat parse errors as expired
            
            health_status = 'healthy'
            if expired_tokens > total_tokens * 0.1:  # More than 10% expired
                health_status = 'degraded'
            if expired_tokens > total_tokens * 0.3:  # More than 30% expired
                health_status = 'unhealthy'
            
            return {
                'status': health_status,
                'total_tokens': total_tokens,
                'expired_tokens': expired_tokens,
                'expiring_soon': expiring_soon,
                'health_percentage': round((total_tokens - expired_tokens) / max(total_tokens, 1) * 100, 2)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }

# ===========================================
# ALERTING SYSTEM
# ===========================================

class AlertManager:
    """Simple alerting system for critical issues"""
    
    def __init__(self):
        self.alert_thresholds = {
            'error_rate': 0.05,  # 5% error rate
            'response_time': 2.0,  # 2 seconds
            'cpu_usage': 80.0,    # 80% CPU
            'memory_usage': 85.0,  # 85% memory
            'disk_usage': 90.0,   # 90% disk
            'oauth_failure_rate': 0.1  # 10% OAuth failure rate
        }
        self.active_alerts = set()
    
    def check_alerts(self, metrics_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for alert conditions"""
        alerts = []
        
        # Error rate alert
        error_rate = metrics_data['requests']['error_rate']
        if error_rate > self.alert_thresholds['error_rate']:
            alert = {
                'type': 'error_rate',
                'severity': 'critical' if error_rate > 0.1 else 'warning',
                'message': f"High error rate: {error_rate:.2%}",
                'value': error_rate,
                'threshold': self.alert_thresholds['error_rate']
            }
            alerts.append(alert)
        
        # Response time alert
        avg_response_time = metrics_data['requests']['avg_response_time']
        if avg_response_time > self.alert_thresholds['response_time']:
            alert = {
                'type': 'response_time',
                'severity': 'warning',
                'message': f"High response time: {avg_response_time:.2f}s",
                'value': avg_response_time,
                'threshold': self.alert_thresholds['response_time']
            }
            alerts.append(alert)
        
        # System resource alerts
        system = metrics_data['system']
        
        if system['cpu_percent'] > self.alert_thresholds['cpu_usage']:
            alerts.append({
                'type': 'cpu_usage',
                'severity': 'warning',
                'message': f"High CPU usage: {system['cpu_percent']:.1f}%",
                'value': system['cpu_percent'],
                'threshold': self.alert_thresholds['cpu_usage']
            })
        
        if system['memory_percent'] > self.alert_thresholds['memory_usage']:
            alerts.append({
                'type': 'memory_usage',
                'severity': 'critical' if system['memory_percent'] > 95 else 'warning',
                'message': f"High memory usage: {system['memory_percent']:.1f}%",
                'value': system['memory_percent'],
                'threshold': self.alert_thresholds['memory_usage']
            })
        
        # OAuth failure rate alert
        oauth = metrics_data['oauth']
        total_oauth = oauth['successful'] + oauth['failed']
        if total_oauth > 0:
            oauth_failure_rate = oauth['failed'] / total_oauth
            if oauth_failure_rate > self.alert_thresholds['oauth_failure_rate']:
                alerts.append({
                    'type': 'oauth_failure_rate',
                    'severity': 'critical',
                    'message': f"High OAuth failure rate: {oauth_failure_rate:.2%}",
                    'value': oauth_failure_rate,
                    'threshold': self.alert_thresholds['oauth_failure_rate']
                })
        
        return alerts

# Global alert manager
alert_manager = AlertManager()

# ===========================================
# MONITORING ENDPOINTS
# ===========================================

def setup_monitoring_endpoints(app: FastAPI):
    """Add monitoring endpoints to FastAPI app"""
    
    @app.get("/metrics")
    async def get_metrics():
        """Prometheus-style metrics endpoint"""
        metrics_data = metrics.get_metrics()
        alerts = alert_manager.check_alerts(metrics_data)
        
        return {
            "metrics": metrics_data,
            "alerts": alerts,
            "alert_count": len(alerts)
        }
    
    @app.get("/health/detailed")
    async def detailed_health_check():
        """Detailed health check with all dependencies"""
        # This would be implemented per service with specific dependencies
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "checks": {
                "database": {"status": "healthy"},
                "external_apis": {"status": "healthy"},
                "oauth_tokens": {"status": "healthy"}
            }
        }
