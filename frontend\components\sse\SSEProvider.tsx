// frontend/components/sse/SSEProvider.tsx
// Global SSE connection provider for the application

import React, { createContext, useContext, ReactNode } from 'react';
import { useSSEConnection, SSEConnectionConfig } from '../../hooks/useSSEConnection';

interface SSEContextType {
  isConnected: boolean;
  isConnecting: boolean;
  healthScore: number;
  error: string | null;
  reconnectAttempts: number;
  lastConnected: Date | null;
  reconnect: () => void;
  disconnect: () => void;
  messages: any[];
  clearMessages: () => void;
}

const SSEContext = createContext<SSEContextType | null>(null);

interface SSEProviderProps {
  children: ReactNode;
  config?: Partial<SSEConnectionConfig>;
  enabled?: boolean;
}

export const SSEProvider: React.FC<SSEProviderProps> = ({ 
  children, 
  config = {},
  enabled = true 
}) => {
  // Get MCP proxy URL from environment
  const mcpUrl = process.env.NEXT_PUBLIC_MCP_URL || 'http://localhost:3001';
  
  // Default SSE configuration
  const defaultConfig: SSEConnectionConfig = {
    url: `${mcpUrl}/api/sse/stream`, // This would be the actual SSE endpoint
    maxReconnectAttempts: parseInt(process.env.NEXT_PUBLIC_SSE_MAX_RECONNECT_ATTEMPTS || '10'),
    reconnectInterval: parseInt(process.env.NEXT_PUBLIC_SSE_RECONNECT_INTERVAL || '5000'),
    heartbeatTimeout: parseInt(process.env.NEXT_PUBLIC_SSE_HEARTBEAT_TIMEOUT || '60000'),
    enabled: enabled && (process.env.NEXT_PUBLIC_SSE_ENABLED !== 'false')
  };

  const finalConfig = { ...defaultConfig, ...config };
  
  const { state, messages, reconnect, disconnect, clearMessages } = useSSEConnection(finalConfig);

  const contextValue: SSEContextType = {
    isConnected: state.connected,
    isConnecting: state.connecting,
    healthScore: state.healthScore,
    error: state.error,
    reconnectAttempts: state.reconnectAttempts,
    lastConnected: state.lastConnected,
    reconnect,
    disconnect,
    messages,
    clearMessages
  };

  return (
    <SSEContext.Provider value={contextValue}>
      {children}
    </SSEContext.Provider>
  );
};

export const useSSE = (): SSEContextType => {
  const context = useContext(SSEContext);
  if (!context) {
    throw new Error('useSSE must be used within an SSEProvider');
  }
  return context;
};

// Optional hook for conditional SSE usage
export const useSSEOptional = (): SSEContextType | null => {
  return useContext(SSEContext);
};

export default SSEProvider;
