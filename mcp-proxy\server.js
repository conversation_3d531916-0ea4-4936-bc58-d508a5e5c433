// mcp-proxy/server.js
// Simplified Express Server with Direct n8n Integration
// No mcp-remote dependencies - everything handled directly

const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const { spawn } = require('child_process');
const path = require('path');
require('dotenv').config();

// Import environment utilities
let envConfig;
try {
    const { envConfig: config } = require(path.join(__dirname, '..', 'shared', 'environment.js'));
    envConfig = config;
    console.log('🌍 MCP Proxy Environment Configuration:');
    envConfig.printConfigSummary();
} catch (error) {
    console.warn('⚠️  Environment utilities not available, using fallback configuration');
    envConfig = null;
}

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware with environment-aware CORS
let allowedOrigins;
try {
    if (envConfig) {
        allowedOrigins = envConfig.getAllCorsOrigins();
        console.log(`🔒 CORS Origins (${allowedOrigins.length}):`, allowedOrigins);
    } else {
        throw new Error('Environment config not available');
    }
} catch (error) {
    console.warn('⚠️  Using fallback CORS configuration');
    // Fallback CORS configuration - only localhost for development
    const corsOrigins = process.env.CORS_ORIGINS;
    if (corsOrigins) {
        try {
            allowedOrigins = JSON.parse(corsOrigins);
        } catch (parseError) {
            console.error('Failed to parse CORS_ORIGINS environment variable');
            allowedOrigins = [];
        }
    } else {
        // Default to localhost only if no environment variable is set
        allowedOrigins = ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8001', 'http://localhost:8002', 'http://localhost:8003'];
    }

    // Add localhost for development
    if (process.env.NODE_ENV !== 'production') {
        const localhostOrigins = ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8001', 'http://localhost:8002', 'http://localhost:8003'];
        allowedOrigins = [...new Set([...allowedOrigins, ...localhostOrigins])];
    }
}

app.use(cors({
    origin: allowedOrigins,
    credentials: true
}));
app.use(express.json());

// Import our Consolidated Smart MCP Proxy
const SmartMCPProxy = require('./smart_proxy_js');

// Initialize proxy
const mcpProxy = new SmartMCPProxy();

// Global process tracking
let claudeMCPProcess = null;
let processRestartCount = 0;
const MAX_RESTART_ATTEMPTS = 3;

// JWT verification middleware
const verifyToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'No token provided' });
    }
    
    const token = authHeader.substring(7);
    req.employeeToken = token;
    next(); // For now, we'll trust the token - Auth service handles verification
};

// Enhanced health check with SSE monitoring
app.get('/health', async (req, res) => {
    try {
        // Test n8n connection directly (with fallback)
        let n8nHealth;
        try {
            n8nHealth = await mcpProxy._testN8NConnection();
        } catch (error) {
            n8nHealth = {
                success: false,
                status: 'n8n_test_failed',
                error: error.message
            };
        }

        // Test Auth service connectivity
        let authHealth;
        try {
            const axios = require('axios');
            const authUrl = process.env.AUTH_SERVICE_URL || 'http://auth-service:8003';
            const response = await axios.get(`${authUrl}/health`, { timeout: 5000 });
            authHealth = {
                success: true,
                status: 'healthy',
                response_status: response.status
            };
        } catch (error) {
            authHealth = {
                success: false,
                status: 'unreachable',
                error: error.message,
                code: error.code
            };
        }

        // Get detailed SSE connection status
        const sseStatus = mcpProxy.getSSEConnectionStatus();

        // Calculate connection health score
        const connectionHealthScore = calculateConnectionHealth(sseStatus);

        res.json({
            status: 'Smart MCP Proxy running',
            timestamp: new Date(),
            version: '2.0.0',
            features: ['direct_n8n', 'oauth_injection', 'claude_compatible', 'enhanced_sse'],
            n8n_connection: n8nHealth,
            auth_service: authHealth,
            sse_connection: {
                ...sseStatus,
                health_score: connectionHealthScore,
                uptime_ms: sseStatus.lastConnectionTime ? Date.now() - sseStatus.lastConnectionTime : null
            },
            claude_mcp_server: claudeMCPProcess ? 'running' : 'stopped',
            restart_count: processRestartCount,
            environment: {
                auth_service_url: process.env.AUTH_SERVICE_URL || 'not_set',
                integrations_service_url: process.env.INTEGRATIONS_SERVICE_URL || 'not_set',
                n8n_sse_url: process.env.N8N_SSE_URL ? 'set' : 'not_set',
                google_client_id: process.env.GOOGLE_CLIENT_ID ? 'SET' : 'MISSING',
                google_client_secret: process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'MISSING',
                port: process.env.PORT || 3001
            }
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            error: error.message,
            timestamp: new Date()
        });
    }
});

// Helper function to calculate connection health score
function calculateConnectionHealth(sseStatus) {
    let score = 0;

    // Connection status (40 points)
    if (sseStatus.connected) score += 40;
    else if (sseStatus.connecting) score += 20;

    // Retry attempts (20 points)
    const retryRatio = sseStatus.connectionAttempts / sseStatus.maxRetries;
    score += Math.max(0, 20 * (1 - retryRatio));

    // Recent activity (20 points)
    if (sseStatus.lastHeartbeat) {
        const timeSinceHeartbeat = Date.now() - sseStatus.lastHeartbeat;
        if (timeSinceHeartbeat < 30000) score += 20; // Within 30 seconds
        else if (timeSinceHeartbeat < 60000) score += 10; // Within 1 minute
    }

    // Session establishment (20 points)
    if (sseStatus.messagesEndpoint && sseStatus.sessionId) score += 20;

    return Math.round(score);
}

// Generate SSE recommendations based on status
function generateSSERecommendations(status) {
    const recommendations = [];

    if (!status.connected && status.connectionAttempts >= status.maxRetries) {
        recommendations.push({
            level: 'critical',
            message: 'Max retry attempts reached. Check N8N service availability.',
            action: 'Use POST /api/sse/reconnect to reset and retry'
        });
    }

    if (status.lastError) {
        recommendations.push({
            level: 'warning',
            message: `Last connection error: ${status.lastError}`,
            action: 'Check network connectivity and N8N service status'
        });
    }

    if (status.lastHeartbeat && (Date.now() - status.lastHeartbeat) > 60000) {
        recommendations.push({
            level: 'warning',
            message: 'No recent heartbeat detected. Connection may be stale.',
            action: 'Monitor for automatic reconnection or manually reconnect'
        });
    }

    if (!status.messagesEndpoint) {
        recommendations.push({
            level: 'info',
            message: 'Messages endpoint not yet established.',
            action: 'Wait for SSE handshake to complete'
        });
    }

    if (status.pendingRequests > 5) {
        recommendations.push({
            level: 'warning',
            message: `High number of pending requests: ${status.pendingRequests}`,
            action: 'Check for request processing delays'
        });
    }

    return recommendations;
}

// Test network connectivity
async function testNetworkConnectivity() {
    const tests = [];

    // Test N8N base connectivity
    if (process.env.N8N_SSE_URL) {
        try {
            const url = new URL(process.env.N8N_SSE_URL);
            const baseUrl = `${url.protocol}//${url.host}`;

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(baseUrl, {
                method: 'HEAD',
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            tests.push({
                test: 'n8n_base_connectivity',
                success: response.ok,
                status_code: response.status,
                url: baseUrl
            });
        } catch (error) {
            tests.push({
                test: 'n8n_base_connectivity',
                success: false,
                error: error.message,
                url: process.env.N8N_SSE_URL
            });
        }
    }

    // Test DNS resolution
    try {
        const dns = require('dns').promises;
        if (process.env.N8N_SSE_URL) {
            const url = new URL(process.env.N8N_SSE_URL);
            const addresses = await dns.resolve4(url.hostname);
            tests.push({
                test: 'dns_resolution',
                success: true,
                hostname: url.hostname,
                addresses: addresses
            });
        }
    } catch (error) {
        tests.push({
            test: 'dns_resolution',
            success: false,
            error: error.message
        });
    }

    return tests;
}

// Generate troubleshooting steps
function generateTroubleshootingSteps(sseStatus, n8nTest) {
    const steps = [];

    if (!sseStatus.connected) {
        steps.push({
            step: 1,
            title: 'Check N8N Service Status',
            description: 'Verify that the N8N service is running and accessible',
            command: 'curl -I ' + (process.env.N8N_SSE_URL || 'N8N_URL_NOT_SET')
        });

        steps.push({
            step: 2,
            title: 'Verify Network Connectivity',
            description: 'Test basic network connectivity to N8N host',
            command: 'ping ' + (process.env.N8N_SSE_URL ? new URL(process.env.N8N_SSE_URL).hostname : 'N8N_HOST_NOT_SET')
        });

        steps.push({
            step: 3,
            title: 'Check Environment Variables',
            description: 'Ensure N8N_SSE_URL is properly configured',
            status: process.env.N8N_SSE_URL ? 'SET' : 'MISSING'
        });
    }

    if (sseStatus.connectionAttempts >= sseStatus.maxRetries) {
        steps.push({
            step: 4,
            title: 'Reset Connection Attempts',
            description: 'Use the reconnect endpoint to reset retry counter',
            command: 'POST /api/sse/reconnect'
        });
    }

    if (!n8nTest.success) {
        steps.push({
            step: 5,
            title: 'Investigate N8N Connection Error',
            description: 'Check N8N service logs and configuration',
            error: n8nTest.error
        });
    }

    return steps;
}

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Uru Smart MCP Proxy - Direct Integration',
        version: '2.0.0',
        description: 'Direct n8n MCP integration with OAuth injection - enhanced SSE reliability',
        endpoints: {
            health: 'GET /health',
            sse_status: 'GET /api/sse/status',
            sse_diagnostics: 'GET /api/sse/diagnostics',
            sse_reconnect: 'POST /api/sse/reconnect',
            tools: 'GET /api/tools',
            execute: 'POST /api/tools/execute',
            chat: 'POST /api/chat/completions',
            mcp_list: 'POST /mcp/tools/list',
            mcp_call: 'POST /mcp/tools/call'
        }
    });
});

// ===========================================
// SSE DIAGNOSTIC ENDPOINTS
// ===========================================

// SSE Connection Status
app.get('/api/sse/status', (req, res) => {
    try {
        const status = mcpProxy.getSSEConnectionStatus();
        const healthScore = calculateConnectionHealth(status);

        res.json({
            success: true,
            connection: {
                ...status,
                health_score: healthScore,
                uptime_ms: status.lastConnectionTime ? Date.now() - status.lastConnectionTime : null,
                time_since_last_heartbeat: status.lastHeartbeat ? Date.now() - status.lastHeartbeat : null
            },
            recommendations: generateSSERecommendations(status)
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date()
        });
    }
});

// Detailed SSE Diagnostics
app.get('/api/sse/diagnostics', async (req, res) => {
    try {
        const status = mcpProxy.getSSEConnectionStatus();
        const n8nTest = await mcpProxy._testN8NConnection();

        // Test network connectivity
        const networkTest = await testNetworkConnectivity();

        res.json({
            success: true,
            timestamp: new Date(),
            connection_status: status,
            n8n_connectivity: n8nTest,
            network_diagnostics: networkTest,
            environment_check: {
                n8n_url_configured: !!process.env.N8N_SSE_URL,
                n8n_url_valid: process.env.N8N_SSE_URL && process.env.N8N_SSE_URL.startsWith('http'),
                docker_environment: !!process.env.DOCKER_ENV,
                elestio_deployment: process.env.ENVIRONMENT === 'production'
            },
            troubleshooting: generateTroubleshootingSteps(status, n8nTest)
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date()
        });
    }
});

// Force SSE Reconnection
app.post('/api/sse/reconnect', (req, res) => {
    try {
        console.log('[SSE-API] 🔄 Manual reconnection requested');

        // Clean up current connection
        mcpProxy.cleanup();

        // Reset connection attempts to allow immediate retry
        mcpProxy.persistentSSE.connectionAttempts = 0;

        // Trigger new connection
        setTimeout(() => {
            mcpProxy._establishPersistentSSE();
        }, 1000);

        res.json({
            success: true,
            message: 'SSE reconnection initiated',
            timestamp: new Date()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date()
        });
    }
});

// ===========================================
// MCP PROTOCOL ENDPOINTS (for Claude Desktop)
// ===========================================

// MCP: List all available tools for this employee
app.post('/mcp/tools/list', verifyToken, async (req, res) => {
    try {
        console.log(`[MCP] 📋 Listing tools for employee token: ${req.employeeToken.substring(0, 10)}...`);
        
        const tools = await mcpProxy.getAvailableTools(req.employeeToken);
        
        console.log(`[MCP] ✅ Found ${tools.length} tools for employee`);
        
        res.json({
            jsonrpc: "2.0",
            id: req.body.id || 1,
            result: {
                tools: tools.map(tool => ({
                    name: tool.function.name,
                    description: tool.function.description,
                    inputSchema: tool.function.parameters
                }))
            }
        });
    } catch (error) {
        console.error(`[MCP] ❌ Error listing tools:`, error);
        res.status(500).json({
            jsonrpc: "2.0",
            id: req.body.id || 1,
            error: {
                code: -32603,
                message: "Internal error",
                data: error.message
            }
        });
    }
});

// MCP: Execute a specific tool
app.post('/mcp/tools/call', verifyToken, async (req, res) => {
    try {
        const { name, arguments: toolArgs } = req.body.params;
        
        console.log(`[MCP] 🔧 Executing tool: ${name} for employee`);
        console.log(`[MCP] 📝 Arguments:`, JSON.stringify(toolArgs, null, 2));
        
        const result = await mcpProxy.executeTool(name, toolArgs, req.employeeToken);
        
        console.log(`[MCP] ✅ Tool execution complete: ${name}`);
        
        res.json({
            jsonrpc: "2.0",
            id: req.body.id || 1,
            result: {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify(result, null, 2)
                    }
                ]
            }
        });
    } catch (error) {
        console.error(`[MCP] ❌ Tool execution error:`, error);
        res.status(500).json({
            jsonrpc: "2.0",
            id: req.body.id || 1,
            error: {
                code: -32603,
                message: "Tool execution failed",
                data: error.message
            }
        });
    }
});

// ===========================================
// REST API ENDPOINTS (for web apps and testing)
// ===========================================

// Get available tools for employee (REST format)
app.get('/api/tools', verifyToken, async (req, res) => {
    try {
        const tools = await mcpProxy.getAvailableTools(req.employeeToken);
        res.json({
            success: true,
            tools: tools,
            count: tools.length,
            timestamp: new Date()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Execute tool (REST format)
app.post('/api/tools/execute', verifyToken, async (req, res) => {
    try {
        const { tool_name, parameters } = req.body;
        
        if (!tool_name) {
            return res.status(400).json({
                success: false,
                error: 'tool_name is required'
            });
        }
        
        console.log(`[API] 🔧 Executing tool: ${tool_name}`);
        
        const result = await mcpProxy.executeTool(tool_name, parameters || {}, req.employeeToken);
        
        res.json({
            success: !result.error,
            tool_name: tool_name,
            result: result,
            timestamp: new Date()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ===========================================
// TESTING ENDPOINTS
// ===========================================

// Test company tools with direct n8n integration
app.post('/api/test/company', verifyToken, async (req, res) => {
    try {
        console.log(`[TEST] 🧪 Testing direct n8n integration...`);
        
        // Test transcript query
        const transcriptResult = await mcpProxy.executeTool(
            'Transcript_Log_Query',
            { query: 'recent client calls' },
            req.employeeToken
        );
        
        console.log(`[TEST] ✅ Direct n8n result:`, JSON.stringify(transcriptResult, null, 2));
        
        res.json({
            success: true,
            test_results: {
                transcript_query: transcriptResult
            },
            timestamp: new Date(),
            method: 'direct_n8n_integration'
        });
    } catch (error) {
        console.error(`[TEST] ❌ Test failed:`, error);
        res.status(500).json({
            success: false,
            error: error.message,
            stack: error.stack
        });
    }
});

// Test specific n8n tool
app.post('/api/test/tool/:toolName', verifyToken, async (req, res) => {
    try {
        const { toolName } = req.params;
        const { parameters } = req.body;
        
        console.log(`[TEST] 🔧 Testing tool: ${toolName}`);
        console.log(`[TEST] 📝 Parameters:`, JSON.stringify(parameters, null, 2));
        
        const result = await mcpProxy.executeTool(toolName, parameters || {}, req.employeeToken);
        
        res.json({
            success: !result.error,
            tool_name: toolName,
            result: result,
            timestamp: new Date(),
            method: 'direct_n8n_integration'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Debug OAuth status
app.get('/api/debug/oauth', verifyToken, async (req, res) => {
    try {
        const oauthStatus = await mcpProxy.getOAuthConnections(req.employeeToken);
        res.json({
            success: true,
            oauth_connections: oauthStatus
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ===========================================
// CLAUDE DESKTOP MCP SERVER
// ===========================================

function startMCPServer() {
    if (processRestartCount >= MAX_RESTART_ATTEMPTS) {
        console.error(`❌ Max restart attempts (${MAX_RESTART_ATTEMPTS}) reached for Claude MCP Server`);
        return null;
    }

    const mcpServerScript = path.join(__dirname, 'claude_mcp_server.js');
    
    console.log(`🚀 Starting Claude MCP Server (attempt ${processRestartCount + 1}/${MAX_RESTART_ATTEMPTS})`);
    
    claudeMCPProcess = spawn('node', [mcpServerScript], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { 
            ...process.env,
            EMPLOYEE_TOKEN: process.env.EMPLOYEE_TOKEN || 'anonymous'
        }
    });
    
    claudeMCPProcess.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.log(`[Claude MCP] ${output}`);
        }
    });
    
    claudeMCPProcess.stderr.on('data', (data) => {
        const error = data.toString().trim();
        if (error && !error.includes('WARNING')) {
            console.error(`[Claude MCP Error] ${error}`);
        } else if (error.includes('WARNING')) {
            console.warn(`[Claude MCP] ${error}`);
        }
    });
    
    claudeMCPProcess.on('close', (code) => {
        console.log(`[Claude MCP] Process exited with code ${code}`);
        claudeMCPProcess = null;
        
        if (code !== 0) {
            processRestartCount++;
            
            if (processRestartCount < MAX_RESTART_ATTEMPTS) {
                console.log(`[Claude MCP] Scheduling restart in 5 seconds... (${processRestartCount}/${MAX_RESTART_ATTEMPTS})`);
                setTimeout(() => {
                    startMCPServer();
                }, 5000);
            } else {
                console.error(`[Claude MCP] Max restart attempts reached. Not restarting.`);
            }
        } else {
            processRestartCount = 0;
        }
    });
    
    claudeMCPProcess.on('error', (error) => {
        console.error(`[Claude MCP] Process error:`, error);
        claudeMCPProcess = null;
    });
    
    return claudeMCPProcess;
}

// Graceful shutdown handler
function gracefulShutdown() {
    console.log('\n🛑 Shutting down Smart MCP Proxy...');

    // Clean up SSE connections
    if (mcpProxy) {
        console.log('🔄 Cleaning up SSE connections...');
        mcpProxy.cleanup();
    }

    if (claudeMCPProcess) {
        console.log('🔄 Stopping Claude MCP Server...');
        claudeMCPProcess.kill('SIGTERM');

        setTimeout(() => {
            if (claudeMCPProcess && !claudeMCPProcess.killed) {
                console.log('💀 Force killing Claude MCP Server...');
                claudeMCPProcess.kill('SIGKILL');
            }
        }, 5000);
    }

    setTimeout(() => {
        console.log('✅ Shutdown complete');
        process.exit(0);
    }, 6000);
}

// ===========================================
// DOWNLOAD ENDPOINTS
// ===========================================

// Download Claude Desktop MCP Server file
app.get('/download/uru-claude-desktop-server.js', (req, res) => {
    try {
        const fs = require('fs');
        const serverFilePath = path.join(__dirname, 'uru_claude_desktop_server.js');

        // Check if file exists
        if (!fs.existsSync(serverFilePath)) {
            console.error('MCP server file not found at:', serverFilePath);
            return res.status(404).json({ message: 'MCP server file not found' });
        }

        console.log('Serving MCP server file from:', serverFilePath);

        // Read the file content
        const fileContent = fs.readFileSync(serverFilePath, 'utf8');

        // Set headers for file download
        res.setHeader('Content-Type', 'application/javascript');
        res.setHeader('Content-Disposition', 'attachment; filename="uru-claude-desktop-server.js"');
        res.setHeader('Cache-Control', 'no-cache');

        // Send the file content
        res.status(200).send(fileContent);
    } catch (error) {
        console.error('Error serving MCP server file:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// ===========================================
// SERVER STARTUP
// ===========================================

app.listen(PORT, () => {
    console.log(`🚀 Smart MCP Proxy running on port ${PORT}`);
    console.log(`📋 Direct n8n integration (no mcp-remote dependency)`);
    console.log(`📡 n8n endpoint: ${process.env.N8N_SSE_URL || 'https://n8n-uru-u46170.vm.elestio.app/mcp/.../sse'}`);
    console.log(`\n📋 Available endpoints:`);
    console.log(`   • GET  /health - Health check with SSE monitoring`);
    console.log(`   • GET  /api/sse/status - SSE connection status`);
    console.log(`   • GET  /api/sse/diagnostics - Detailed SSE diagnostics`);
    console.log(`   • POST /api/sse/reconnect - Force SSE reconnection`);
    console.log(`   • GET  /api/tools - List available tools`);
    console.log(`   • POST /api/tools/execute - Execute tool`);
    console.log(`   • POST /mcp/tools/list - MCP protocol (Claude)`);
    console.log(`   • POST /mcp/tools/call - MCP protocol (Claude)`);
    console.log(`   • POST /api/test/company - Test company tools`);
    console.log(`   • POST /api/test/tool/:toolName - Test specific tool`);
    console.log(`   • GET  /api/debug/oauth - Debug OAuth status`);
    console.log(`   • GET  /download/uru-claude-desktop-server.js - Download MCP server file`);
    
    // Start Claude MCP Server
    console.log(`\n🤖 Starting Claude MCP Server...`);
    startMCPServer();
    
    // Set up signal handlers for graceful shutdown
    process.on('SIGINT', gracefulShutdown);
    process.on('SIGTERM', gracefulShutdown);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    gracefulShutdown();
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown();
});

module.exports = app;