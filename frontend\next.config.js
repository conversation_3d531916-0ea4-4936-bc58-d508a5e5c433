/** @type {import('next').NextConfig} */
const nextConfig = {
  // Pages Router configuration - stable and reliable
  reactStrictMode: false,  // Disable to prevent hydration issues
  swcMinify: true,         // Use SWC for better performance
  poweredByHeader: false,  // Security

  // Disable static optimization to prevent SSR issues during build
  output: 'standalone',

  // Disable static generation for all pages to prevent build errors
  trailingSlash: false,
  generateBuildId: () => 'build',

  // Disable all static optimization and prerendering
  experimental: {
    esmExternals: false,
  },

  // Router configuration to handle navigation issues
  onDemandEntries: {
    // period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 25 * 1000,
    // number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 2,
  },

  // Disable static optimization completely
  distDir: '.next',
  compress: false,

  // Redirect and URL normalization settings (moved out of experimental)
  skipTrailingSlashRedirect: true,
  skipMiddlewareUrlNormalize: true,

  // Environment variables - no hardcoded production URLs
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_OAUTH_URL: process.env.NEXT_PUBLIC_OAUTH_URL,
    NEXT_PUBLIC_MCP_URL: process.env.NEXT_PUBLIC_MCP_URL,
  },

  // Image optimization
  images: {
    domains: [
      'localhost',
      'upload.wikimedia.org',
      'cdn.jsdelivr.net',
      'img.icons8.com',
      'cdn.simpleicons.org',
      'logos-world.net',
      'cdn.worldvectorlogo.com'
    ],
    unoptimized: process.env.NODE_ENV === 'development',
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
}

module.exports = nextConfig
