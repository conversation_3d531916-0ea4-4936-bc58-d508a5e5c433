import fs from 'fs';
import path from 'path';

export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // For now, serve a redirect to the MCP proxy container
    // In production, this file should be copied during build
    const mcpProxyUrl = process.env.MCP_PROXY_URL || 'http://localhost:3001';
    const downloadUrl = `${mcpProxyUrl}/download/uru-claude-desktop-server.js`;

    console.log('Redirecting MCP server download to:', downloadUrl);

    // Redirect to the MCP proxy download endpoint
    res.redirect(302, downloadUrl);
  } catch (error) {
    console.error('Error serving MCP server file:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
