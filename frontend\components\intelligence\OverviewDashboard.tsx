'use client';

import { Bar<PERSON>hart3, TrendingUp, Users, CheckCircle, Lightbulb, AlertTriangle, ArrowRight } from 'lucide-react';

const stats = [
  { value: 47, label: 'Client Interactions Analyzed' },
  { value: '1,247', label: 'Intelligence Queries' },
  { value: 23, label: 'Patterns Discovered' },
  { value: '94%', label: 'Data Coverage' },
];

const insights = [
  { 
    icon: TrendingUp,
    color: 'text-green-400',
    title: 'Growth Opportunity Identified',
    description: '73% increase in AI/automation questions from clients suggests strong demand for technology consulting services.',
    recommendation: 'Recommended Action: Develop "Digital CFO Services" package and schedule technology discussions with top 12 clients who expressed interest.'
  },
  { 
    icon: Users,
    color: 'text-blue-400',
    title: 'Client Satisfaction Pattern',
    description: 'Clients with monthly check-ins show 85% higher satisfaction scores and 60% better retention rates.',
    recommendation: 'Insight: Consider standardizing monthly touchpoints for all clients and creating structured check-in templates.'
  },
  { 
    icon: AlertTriangle,
    color: 'text-orange-400',
    title: 'A Response Time Impact',
    description: 'Average response time to client questions improved from 6.1 to 4.2 hours, correlating with 23% increase in client engagement.',
    recommendation: 'Goal: Target sub-3 hour response time could further improve engagement and differentiate from competitors.'
  }
];

const opportunities = [
  'Technology adoption consulting for 12 interested clients',
  'Operational scaling guidance for growth-stage companies',
  'Fractional CFO expansion into 3 new industry verticals',
  'Automated reporting services for recurring revenue'
];

const trends = [
  'Cash flow optimization requests +45% vs last week',
  'Strategic guidance requests outpacing tactical work 3:1',
  'Client meeting duration increased 18% (positive engagement)',
  'Follow-up questions after deliverables up 32%'
];

const actions = [
  'Schedule tech consultations with TechStart, Devlin, Manufacturing Co',
  'Draft proposal for "Digital CFO Services" package',
  'Finalize standardized monthly check-in template for all clients',
];

export const OverviewDashboard = () => {
  return (
    <div>
      <h1 className="text-2xl font-bold text-white mb-8">Overview</h1>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => (
          <div key={stat.label} className="bg-gray-800 rounded-xl p-6">
            <p className="text-4xl font-bold text-blue-400">{stat.value}</p>
            <p className="text-gray-400 mt-2">{stat.label}</p>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Key Intelligence Insights */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Key Intelligence Insights</h2>
          <div className="space-y-6">
            {insights.map((insight) => (
              <div key={insight.title} className="flex items-start space-x-4">
                <div className={`p-2 rounded-full bg-gray-700`}>
                  <insight.icon className={`w-6 h-6 ${insight.color}`} />
                </div>
                <div>
                  <h3 className={`font-semibold ${insight.color}`}>{insight.title}</h3>
                  <p className="text-gray-400 text-sm mt-1">{insight.description}</p>
                  <p className="text-gray-300 text-sm mt-2 font-medium">{insight.recommendation}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Opportunities, Trends, Actions */}
        <div className="space-y-8">
          {/* Top Opportunities */}
          <div className="bg-gray-800 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Top Opportunities</h2>
            <ul className="space-y-3">
              {opportunities.map((opp, i) => (
                <li key={i} className="flex items-center space-x-3 text-gray-300">
                  <ArrowRight className="w-4 h-4 text-blue-400" />
                  <span>{opp}</span>
                </li>
              ))}
            </ul>
          </div>
          {/* This Week's Trends */}
          <div className="bg-gray-800 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-4">This Week's Trends</h2>
            <ul className="space-y-3">
              {trends.map((trend, i) => (
                <li key={i} className="flex items-center space-x-3 text-gray-300">
                  <TrendingUp className="w-4 h-4 text-purple-400" />
                  <span>{trend}</span>
                </li>
              ))}
            </ul>
          </div>
          {/* Action Items */}
          <div className="bg-gray-800 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Action Items</h2>
            <ul className="space-y-3">
              {actions.map((action, i) => (
                <li key={i} className="flex items-center space-x-3 text-gray-300">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>{action}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}; 