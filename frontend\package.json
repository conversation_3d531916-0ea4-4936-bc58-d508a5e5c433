{"name": "uru-platform-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start -p 3000 -H 0.0.0.0", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "autoprefixer": "^10.4.16", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "next": "14.2.30", "postcss": "^8.4.31", "react": "^18", "react-dom": "^18", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^8", "eslint-config-next": "14.2.30", "typescript": "^5"}}