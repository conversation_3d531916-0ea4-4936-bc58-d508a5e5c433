import { useState } from 'react';
import Head from 'next/head';

interface DiagnosticResult {
  service: string;
  status: 'healthy' | 'degraded' | 'error' | 'unreachable';
  details: any;
  timestamp: string;
}

interface DiagnosticResponse {
  overall_status: 'healthy' | 'degraded' | 'error';
  services: DiagnosticResult[];
  recommendations: string[];
  timestamp: string;
}

export default function DiagnosePage() {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const runDiagnostics = async () => {
    setIsLoading(true);
    setError('');
    setDiagnostics(null);

    try {
      const response = await fetch('/api/diagnose-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Diagnostic failed: ${response.status}`);
      }

      const result = await response.json();
      setDiagnostics(result);
    } catch (err: any) {
      setError(err.message || 'Diagnostic failed');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'unreachable': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return '✅';
      case 'degraded': return '⚠️';
      case 'error': return '❌';
      case 'unreachable': return '🔌';
      default: return '❓';
    }
  };

  return (
    <>
      <Head>
        <title>System Diagnostics - Uru Workspace Platform</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">System Diagnostics</h1>
            <p className="text-gray-600">Troubleshoot authentication and connectivity issues</p>
          </div>

          <div className="mb-6">
            <button
              onClick={runDiagnostics}
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Running Diagnostics...' : 'Run System Diagnostics'}
            </button>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
              <strong>Error:</strong> {error}
            </div>
          )}

          {diagnostics && (
            <div className="space-y-6">
              {/* Overall Status */}
              <div className={`p-4 rounded-lg border-2 ${
                diagnostics.overall_status === 'healthy' ? 'border-green-500 bg-green-50' :
                diagnostics.overall_status === 'degraded' ? 'border-yellow-500 bg-yellow-50' :
                'border-red-500 bg-red-50'
              }`}>
                <h2 className="text-xl font-semibold mb-2">
                  {getStatusIcon(diagnostics.overall_status)} Overall Status: {diagnostics.overall_status.toUpperCase()}
                </h2>
                <p className="text-sm text-gray-600">
                  Diagnostic completed at {new Date(diagnostics.timestamp).toLocaleString()}
                </p>
              </div>

              {/* Service Status */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Service Status</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {diagnostics.services.map((service, index) => (
                    <div key={index} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{service.service}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                          {getStatusIcon(service.status)} {service.status.toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="text-sm text-gray-600 space-y-1">
                        {service.details.url && (
                          <div><strong>URL:</strong> {service.details.url}</div>
                        )}
                        {service.details.response_status && (
                          <div><strong>HTTP Status:</strong> {service.details.response_status}</div>
                        )}
                        {service.details.database && (
                          <div><strong>Database:</strong> {service.details.database}</div>
                        )}
                        {service.details.error && (
                          <div className="text-red-600"><strong>Error:</strong> {service.details.error}</div>
                        )}
                      </div>

                      {/* Detailed information */}
                      <details className="mt-2">
                        <summary className="cursor-pointer text-blue-600 text-sm">Show Details</summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(service.details, null, 2)}
                        </pre>
                      </details>
                    </div>
                  ))}
                </div>
              </div>

              {/* Recommendations */}
              {diagnostics.recommendations.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Recommendations</h3>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <ul className="space-y-2">
                      {diagnostics.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-2">•</span>
                          <span className="text-blue-800">{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                <div className="grid gap-3 md:grid-cols-2">
                  <a
                    href="/login"
                    className="block text-center bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Try Login Again
                  </a>
                  <button
                    onClick={runDiagnostics}
                    className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Re-run Diagnostics
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="mt-8 text-center">
            <a
              href="/login"
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              ← Back to Login
            </a>
          </div>
        </div>
      </div>
    </>
  );
}
