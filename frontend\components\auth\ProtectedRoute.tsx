// frontend/components/auth/ProtectedRoute.tsx
// Component to protect routes that require authentication

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from './AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted && !isLoading && !isAuthenticated && !redirecting) {
      console.log('🔒 ProtectedRoute: User not authenticated, redirecting to login');
      setRedirecting(true);
      router.push('/login').catch((error) => {
        console.error('❌ ProtectedRoute: Navigation to login failed:', error);
        setRedirecting(false);
      });
    }
  }, [isMounted, isAuthenticated, isLoading, router, redirecting]);

  // Show loading spinner while checking authentication, during SSR, or while redirecting
  if (!isMounted || isLoading || redirecting) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">
            {redirecting ? 'Redirecting...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  // Don't render children if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}

export default ProtectedRoute;
