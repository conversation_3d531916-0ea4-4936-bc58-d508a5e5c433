import React from 'react';
import Link from 'next/link';
import { GetServerSideProps } from 'next';
import { UruLogo } from '../components/shared/UruLogo';
import { ArrowLeft, CheckCircle, Clock, Shield, Zap } from 'lucide-react';

export default function IntegrationsPage() {
  const personalIntegrations = [
    {
      name: "Gmail",
      description: "Email analysis and intelligent search",
      category: "Communication",
      availability: "immediate",
      features: ["Email search", "Sentiment analysis", "Auto-categorization", "Smart replies"]
    },
    {
      name: "Google Drive",
      description: "Document search and content analysis",
      category: "Storage",
      availability: "immediate",
      features: ["Document search", "Content extraction", "Version tracking", "Collaboration insights"]
    },
    {
      name: "Google Calendar",
      description: "Meeting insights and scheduling optimization",
      category: "Productivity",
      availability: "immediate",
      features: ["Meeting analysis", "Schedule optimization", "Attendee insights", "Time tracking"]
    },
    {
      name: "Slack",
      description: "Team communication analysis",
      category: "Communication",
      availability: "immediate",
      features: ["Message search", "Channel insights", "Team collaboration metrics", "Knowledge extraction"]
    }
  ];

  const companyIntegrations = [
    {
      name: "Salesforce",
      description: "CRM data analysis and customer insights",
      category: "CRM",
      availability: "consultation",
      features: ["Customer journey analysis", "Sales pipeline insights", "Lead scoring", "Performance metrics"]
    },
    {
      name: "HubSpot",
      description: "Marketing and sales automation insights",
      category: "CRM",
      availability: "consultation",
      features: ["Campaign analysis", "Lead nurturing insights", "ROI tracking", "Customer lifecycle"]
    },
    {
      name: "QuickBooks",
      description: "Financial data analysis and reporting",
      category: "Financial",
      availability: "consultation",
      features: ["Financial reporting", "Cash flow analysis", "Expense tracking", "Budget insights"]
    },
    {
      name: "Jira",
      description: "Project management and development insights",
      category: "Development",
      availability: "consultation",
      features: ["Sprint analysis", "Bug tracking", "Team performance", "Project timelines"]
    },
    {
      name: "Microsoft 365",
      description: "Office suite integration and analysis",
      category: "Productivity",
      availability: "consultation",
      features: ["Document analysis", "Team collaboration", "Usage insights", "Productivity metrics"]
    },
    {
      name: "Zoom",
      description: "Meeting transcription and analysis",
      category: "Communication",
      availability: "consultation",
      features: ["Meeting transcripts", "Speaker insights", "Action item extraction", "Engagement metrics"]
    },
    {
      name: "Notion",
      description: "Knowledge base and documentation analysis",
      category: "Productivity",
      availability: "consultation",
      features: ["Content search", "Knowledge mapping", "Usage analytics", "Collaboration insights"]
    },
    {
      name: "Airtable",
      description: "Database and workflow analysis",
      category: "Productivity",
      availability: "consultation",
      features: ["Data analysis", "Workflow optimization", "Automation insights", "Performance tracking"]
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'communication':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      case 'storage':
        return 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30';
      case 'productivity':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'crm':
        return 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30';
      case 'financial':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'development':
        return 'bg-orange-500/20 text-orange-300 border-orange-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-gray-900/98 backdrop-blur-md border-b border-gray-700/50 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <UruLogo size="lg" className="flex-shrink-0" />
              <span className="text-xl font-black text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 tracking-tight hover:from-uru-cyan-400 hover:to-uru-blue-400 transition-all duration-300">Uru</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-all duration-200 font-medium px-4 py-2 rounded-lg hover:bg-gray-800/50"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Home</span>
              </Link>
              <Link
                href="/request-access"
                className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-6 py-2.5 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform"
              >
                Request Access
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative pt-32 pb-16">
        <div className="absolute inset-0 bg-gradient-to-br from-uru-blue-900/20 via-gray-900 to-uru-purple-900/20"></div>
        
        <div className="relative max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight leading-tight">
            Connect Your Entire
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-cyan-400 to-uru-blue-400 block mt-2">
              Tech Stack
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light mb-12">
            <span className="text-uru-cyan-400 font-medium">Personal integrations</span> start immediately out of the box.
            <span className="text-uru-blue-400 font-medium"> Company-wide tools</span> added through consultation.
          </p>
          
          {/* Integration Types */}
          <div className="flex flex-wrap justify-center gap-4 text-sm md:text-base">
            <span className="bg-green-500/20 text-green-300 px-4 py-2 rounded-full font-medium border border-green-500/30 flex items-center space-x-2">
              <Zap className="w-4 h-4" />
              <span>Immediate Access</span>
            </span>
            <span className="bg-uru-blue-500/20 text-uru-blue-300 px-4 py-2 rounded-full font-medium border border-uru-blue-500/30 flex items-center space-x-2">
              <Clock className="w-4 h-4" />
              <span>Consultation Required</span>
            </span>
            <span className="bg-uru-purple-500/20 text-uru-purple-300 px-4 py-2 rounded-full font-medium border border-uru-purple-500/30 flex items-center space-x-2">
              <Shield className="w-4 h-4" />
              <span>Enterprise Security</span>
            </span>
          </div>
        </div>
      </section>

      {/* Personal Integrations */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black text-white mb-6 tracking-tight leading-tight">
              <span className="text-green-400">Immediate Access</span> Integrations
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              These personal productivity tools work out of the box on day one. No setup required.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {personalIntegrations.map((integration: any, index: number) => (
              <div
                key={index}
                className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-6 border border-green-500/30 hover:border-green-400/50 hover:shadow-xl hover:shadow-green-500/20 transition-all duration-300 group hover:scale-105 transform"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-white group-hover:text-green-100 transition-colors">
                    {integration.name}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <Zap className="w-5 h-5 text-green-400" />
                    <span className="text-xs text-green-300 font-medium">LIVE</span>
                  </div>
                </div>
                
                <p className="text-gray-300 text-sm mb-4 leading-relaxed group-hover:text-gray-200 transition-colors">
                  {integration.description}
                </p>
                
                <div className="mb-4">
                  <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium border ${getCategoryColor(integration.category)}`}>
                    {integration.category}
                  </span>
                </div>
                
                <div className="space-y-2">
                  {integration.features.map((feature: string, featureIndex: number) => (
                    <div key={featureIndex} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Integrations */}
      <section className="py-16 bg-gradient-to-b from-gray-800/30 to-gray-900/30">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black text-white mb-6 tracking-tight leading-tight">
              <span className="text-uru-blue-400">Company-Wide</span> Integrations
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Custom integrations tailored to your business needs. Added through our consultation process.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {companyIntegrations.map((integration: any, index: number) => (
              <div
                key={index}
                className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-2xl p-6 border border-uru-blue-500/30 hover:border-uru-blue-400/50 hover:shadow-xl-colored transition-all duration-300 group hover:scale-105 transform"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-white group-hover:text-uru-blue-100 transition-colors">
                    {integration.name}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-uru-blue-400" />
                    <span className="text-xs text-uru-blue-300 font-medium">CUSTOM</span>
                  </div>
                </div>
                
                <p className="text-gray-300 text-sm mb-4 leading-relaxed group-hover:text-gray-200 transition-colors">
                  {integration.description}
                </p>
                
                <div className="mb-4">
                  <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium border ${getCategoryColor(integration.category)}`}>
                    {integration.category}
                  </span>
                </div>
                
                <div className="space-y-2">
                  {integration.features.slice(0, 3).map((feature: string, featureIndex: number) => (
                    <div key={featureIndex} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-uru-blue-400 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                  {integration.features.length > 3 && (
                    <div className="text-uru-blue-300 text-xs font-medium">
                      +{integration.features.length - 3} more features
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/30 to-gray-800/30"></div>
        
        <div className="relative max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-5xl font-black text-white mb-8 tracking-tight leading-tight">
            Ready to Connect
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 block mt-2">
              Your Tools?
            </span>
          </h2>
          <p className="text-xl text-gray-300 mb-12 leading-relaxed">
            Start with personal integrations today, then add company-wide tools through our consultation process.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link
              href="/request-access"
              className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-10 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform text-lg"
            >
              Get Started Today
            </Link>
            <button
              onClick={() => window.open('https://calendly.com/uru-discovery', '_blank')}
              className="border-2 border-uru-purple-500/50 hover:border-uru-purple-400 bg-uru-purple-500/10 hover:bg-uru-purple-500/20 text-uru-purple-300 hover:text-white px-10 py-4 rounded-xl font-semibold transition-all duration-300 backdrop-blur-sm hover:shadow-glow-purple hover:scale-105 transform text-lg"
            >
              Schedule Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}

// Prevent static generation for this page to avoid SSR issues
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
