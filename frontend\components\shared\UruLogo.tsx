import React from 'react';

interface UruLogoProps {
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
}

export const UruLogo: React.FC<UruLogoProps> = ({
  className = '',
  size = 'md'
}) => {
  const sizeMap = {
    xs: 40,
    sm: 56,
    md: 80,
    lg: 120,
    xl: 160,
    '2xl': 200,
    '3xl': 240
  };

  const imageSize = sizeMap[size];

  // Use regular img element to avoid Next.js Image fetchPriority warnings
  // This is simpler and more reliable for logos
  return (
    <div className={`relative ${className}`} style={{ width: imageSize, height: imageSize }}>
      <img
        src="/uru-logo.png"
        alt="Uru Logo"
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        }}
        loading={size === 'lg' || size === 'xl' || size === '2xl' ? 'eager' : 'lazy'}
      />
    </div>
  );
};
