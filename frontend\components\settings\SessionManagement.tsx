// frontend/components/settings/SessionManagement.tsx
// Session Management Component for Remember Me Tokens

import React, { useState, useEffect } from 'react';
import { Monitor, Smartphone, Tablet, Globe, Calendar, MapPin, Shield, X } from 'lucide-react';
import apiService from '../../utils/api';

interface RememberMeToken {
  selector: string;
  device_fingerprint: {
    user_agent_hash: string;
    ip_address: string;
    created_at: string;
  };
  user_agent: string;
  ip_address: string;
  last_used_at: string;
  expires_at: string;
  created_at: string;
}

interface SessionManagementProps {
  className?: string;
}

export function SessionManagement({ className = '' }: SessionManagementProps) {
  const [tokens, setTokens] = useState<RememberMeToken[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [revoking, setRevoking] = useState<string | null>(null);

  useEffect(() => {
    loadActiveTokens();
  }, []);

  const loadActiveTokens = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const response = await fetch(`${(apiService as any).authBaseUrl}/auth/remember-me/active`, {
        headers: (apiService as any).getAuthHeaders(),
      });

      if (response.ok) {
        const result = await response.json();
        setTokens(result.tokens || []);
      } else {
        throw new Error('Failed to load active sessions');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load sessions');
    } finally {
      setIsLoading(false);
    }
  };

  const revokeToken = async (selector: string) => {
    try {
      setRevoking(selector);
      
      const formData = new URLSearchParams();
      formData.append('selector', selector);

      const response = await fetch(`${(apiService as any).authBaseUrl}/auth/remember-me/revoke`, {
        method: 'POST',
        body: formData,
        headers: {
          ...(apiService as any).getAuthHeaders(),
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (response.ok) {
        // Remove the token from the list
        setTokens(tokens.filter(token => token.selector !== selector));
      } else {
        throw new Error('Failed to revoke session');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to revoke session');
    } finally {
      setRevoking(null);
    }
  };

  const revokeAllTokens = async () => {
    try {
      setIsLoading(true);
      
      const success = await apiService.revokeAllRememberMeTokens();
      if (success) {
        setTokens([]);
      } else {
        throw new Error('Failed to revoke all sessions');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to revoke all sessions');
    } finally {
      setIsLoading(false);
    }
  };

  const getDeviceIcon = (userAgent: string) => {
    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return <Smartphone className="w-5 h-5" />;
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return <Tablet className="w-5 h-5" />;
    } else {
      return <Monitor className="w-5 h-5" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getDaysUntilExpiry = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (isLoading) {
    return (
      <div className={`bg-gray-800 rounded-xl p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-3 text-gray-300">Loading sessions...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-800 rounded-xl p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Shield className="w-6 h-6 text-blue-400" />
          <h3 className="text-xl font-semibold text-white">Active Sessions</h3>
        </div>
        {tokens.length > 0 && (
          <button
            onClick={revokeAllTokens}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors"
          >
            Revoke All Sessions
          </button>
        )}
      </div>

      {error && (
        <div className="mb-4 bg-red-600/10 border border-red-600/30 rounded-lg p-4">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {tokens.length === 0 ? (
        <div className="text-center py-8">
          <Shield className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No active "Stay signed in" sessions found.</p>
          <p className="text-gray-500 text-sm mt-2">
            Enable "Stay signed in" during login to see sessions here.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {tokens.map((token) => (
            <div
              key={token.selector}
              className="bg-gray-700 rounded-lg p-4 flex items-center justify-between"
            >
              <div className="flex items-start space-x-4">
                <div className="text-blue-400 mt-1">
                  {getDeviceIcon(token.user_agent)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="text-white font-medium">
                      {token.user_agent.includes('Chrome') ? 'Chrome' :
                       token.user_agent.includes('Firefox') ? 'Firefox' :
                       token.user_agent.includes('Safari') ? 'Safari' :
                       token.user_agent.includes('Edge') ? 'Edge' : 'Unknown Browser'}
                    </h4>
                    <span className="text-gray-400 text-sm">
                      {token.user_agent.includes('Windows') ? 'Windows' :
                       token.user_agent.includes('Mac') ? 'macOS' :
                       token.user_agent.includes('Linux') ? 'Linux' :
                       token.user_agent.includes('Android') ? 'Android' :
                       token.user_agent.includes('iOS') ? 'iOS' : 'Unknown OS'}
                    </span>
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-400">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>IP: {token.ip_address}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>Last used: {formatDate(token.last_used_at)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Globe className="w-4 h-4" />
                      <span>
                        Expires in {getDaysUntilExpiry(token.expires_at)} days
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <button
                onClick={() => revokeToken(token.selector)}
                disabled={revoking === token.selector}
                className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-600/10 rounded-lg transition-colors disabled:opacity-50"
                title="Revoke this session"
              >
                {revoking === token.selector ? (
                  <div className="w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <X className="w-4 h-4" />
                )}
              </button>
            </div>
          ))}
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-600/10 border border-blue-600/30 rounded-lg">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-blue-400 mt-0.5" />
          <div className="text-sm">
            <p className="text-blue-300 font-medium mb-1">Security Information</p>
            <p className="text-blue-200">
              "Stay signed in" sessions allow you to remain logged in for up to 30 days. 
              You can revoke any session if you suspect unauthorized access.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SessionManagement;
