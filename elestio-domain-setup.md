# Elestio Domain Configuration for New Architecture

## Required Domain Mappings

Configure these domains in your Elestio dashboard:

1. **app.uruenterprises.com**
   - Port: 3000
   - Path: /
   - Service: Frontend

2. **auth.uruenterprises.com**
   - Port: 8003
   - Path: /
   - Service: Auth Service

3. **integrations.uruenterprises.com**
   - Port: 8002
   - Path: /
   - Service: Integration Service

4. **mcp.uruenterprises.com**
   - Port: 3001
   - Path: /
   - Service: MCP Proxy

## Google OAuth Setup

Add these redirect URIs to your Google Cloud Console OAuth configuration:

- https://auth.uruenterprises.com/oauth/google/callback
- https://integrations.uruenterprises.com/oauth/google/callback

## Health Check URLs

After deployment, verify these endpoints:

- https://app.uruenterprises.com/api/health
- https://auth.uruenterprises.com/health
- https://integrations.uruenterprises.com/health
- https://mcp.uruenterprises.com/health
