#!/usr/bin/env node

/**
 * SSE Docker Environment Optimization Script
 * Optimizes Docker and environment settings for reliable SSE connections
 * in both local development and Elestio production environments
 */

const fs = require('fs');
const path = require('path');

class SSEDockerOptimizer {
    constructor() {
        this.isElestio = process.env.ENVIRONMENT === 'production';
        this.isLocal = !this.isElestio;
        
        console.log(`🔧 SSE Docker Optimizer`);
        console.log(`📍 Environment: ${this.isElestio ? 'Elestio Production' : 'Local Development'}`);
    }

    async optimize() {
        console.log(`\n🚀 Starting SSE optimization...`);
        
        try {
            // 1. Validate environment variables
            await this.validateEnvironment();
            
            // 2. Check Docker configuration
            await this.checkDockerConfig();
            
            // 3. Optimize network settings
            await this.optimizeNetworkSettings();
            
            // 4. Generate environment-specific recommendations
            await this.generateRecommendations();
            
            console.log(`\n✅ SSE optimization completed successfully!`);
            
        } catch (error) {
            console.error(`\n❌ SSE optimization failed:`, error.message);
            process.exit(1);
        }
    }

    async validateEnvironment() {
        console.log(`\n📋 Validating environment variables...`);

        const requiredVars = [
            'N8N_SSE_URL',
            'AUTH_SERVICE_URL',
            'INTEGRATIONS_SERVICE_URL'
        ];

        const missing = [];
        const invalid = [];

        for (const varName of requiredVars) {
            const value = process.env[varName];

            if (!value) {
                missing.push(varName);
            } else if (varName.includes('URL') && !value.startsWith('http')) {
                invalid.push(`${varName}: ${value}`);
            } else {
                console.log(`   ✅ ${varName}: ${this.maskUrl(value)}`);
            }
        }

        if (missing.length > 0) {
            console.warn(`   ⚠️ Missing environment variables: ${missing.join(', ')}`);
            console.log(`   💡 For local development, these may be set in Docker Compose or .env files`);

            // Don't fail for local development - just warn
            if (this.isElestio) {
                throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
            }
        }

        if (invalid.length > 0) {
            throw new Error(`Invalid URL format: ${invalid.join(', ')}`);
        }

        // Validate N8N SSE URL format if present
        const n8nUrl = process.env.N8N_SSE_URL;
        if (n8nUrl && !n8nUrl.includes('/sse')) {
            console.warn(`   ⚠️ N8N_SSE_URL should end with '/sse': ${this.maskUrl(n8nUrl)}`);
        }
    }

    async checkDockerConfig() {
        console.log(`\n🐳 Checking Docker configuration...`);
        
        const dockerComposePath = path.join(process.cwd(), 'docker-compose.yml');
        
        if (!fs.existsSync(dockerComposePath)) {
            throw new Error('docker-compose.yml not found');
        }
        
        const dockerConfig = fs.readFileSync(dockerComposePath, 'utf8');
        
        // Check for health checks
        if (dockerConfig.includes('healthcheck:')) {
            console.log(`   ✅ Health checks configured`);
        } else {
            console.warn(`   ⚠️ No health checks found in Docker configuration`);
        }
        
        // Check for network configuration
        if (dockerConfig.includes('networks:')) {
            console.log(`   ✅ Custom networks configured`);
        } else {
            console.warn(`   ⚠️ Using default Docker network`);
        }
        
        // Check for restart policies
        if (dockerConfig.includes('restart:') || dockerConfig.includes('depends_on:')) {
            console.log(`   ✅ Service dependencies configured`);
        } else {
            console.warn(`   ⚠️ No restart policies or dependencies found`);
        }
    }

    async optimizeNetworkSettings() {
        console.log(`\n🌐 Optimizing network settings...`);
        
        if (this.isElestio) {
            console.log(`   📡 Elestio environment detected`);
            console.log(`   ✅ Using reverse proxy configuration`);
            console.log(`   ✅ HTTPS termination at load balancer`);
            
            // Check if Nginx config exists
            const nginxConfigPath = path.join(process.cwd(), 'nginx-new-architecture.conf');
            if (fs.existsSync(nginxConfigPath)) {
                console.log(`   ✅ Nginx configuration found`);
                
                const nginxConfig = fs.readFileSync(nginxConfigPath, 'utf8');
                
                // Check for SSE optimizations
                if (nginxConfig.includes('proxy_buffering off')) {
                    console.log(`   ✅ SSE buffering optimizations enabled`);
                } else {
                    console.warn(`   ⚠️ SSE buffering optimizations not found`);
                }
                
                if (nginxConfig.includes('proxy_read_timeout 3600s')) {
                    console.log(`   ✅ Extended timeout for SSE connections`);
                } else {
                    console.warn(`   ⚠️ Standard timeouts may cause SSE disconnections`);
                }
            } else {
                console.warn(`   ⚠️ Nginx configuration not found`);
            }
        } else {
            console.log(`   🏠 Local development environment`);
            console.log(`   ✅ Direct container communication`);
            console.log(`   ✅ No reverse proxy overhead`);
        }
    }

    async generateRecommendations() {
        console.log(`\n💡 Environment-specific recommendations:`);
        
        if (this.isElestio) {
            console.log(`\n   📡 Elestio Production Recommendations:`);
            console.log(`   • Monitor SSE connection health via /api/sse/status`);
            console.log(`   • Use /api/sse/diagnostics for troubleshooting`);
            console.log(`   • Set up alerts for connection health score < 70`);
            console.log(`   • Consider N8N service placement in same region`);
            console.log(`   • Monitor Nginx access logs for SSE endpoint usage`);
            
            // Generate monitoring script
            this.generateMonitoringScript();
            
        } else {
            console.log(`\n   🏠 Local Development Recommendations:`);
            console.log(`   • Use Docker Desktop for consistent networking`);
            console.log(`   • Test SSE connections with: curl -N ${process.env.N8N_SSE_URL || 'N8N_URL'}`);
            console.log(`   • Monitor container logs: docker-compose logs -f mcp-proxy`);
            console.log(`   • Use /api/sse/reconnect for quick connection reset`);
            console.log(`   • Ensure N8N service is running before starting MCP proxy`);
        }
        
        console.log(`\n   🔧 General SSE Optimization Tips:`);
        console.log(`   • Keep N8N service stable and accessible`);
        console.log(`   • Monitor connection health score regularly`);
        console.log(`   • Use exponential backoff for reconnections (implemented)`);
        console.log(`   • Set up proper logging for SSE events`);
        console.log(`   • Consider connection pooling for high-traffic scenarios`);
    }

    generateMonitoringScript() {
        const monitoringScript = `#!/bin/bash
# SSE Connection Monitoring Script for Elestio
# Run this script periodically to monitor SSE health

MCP_URL="${process.env.NEXT_PUBLIC_MCP_URL || 'https://mcp.uruenterprises.com'}"

echo "🔍 Checking SSE Connection Health..."

# Get SSE status
HEALTH_RESPONSE=$(curl -s "$MCP_URL/api/sse/status")
HEALTH_SCORE=$(echo "$HEALTH_RESPONSE" | jq -r '.connection.health_score // 0')

echo "📊 Health Score: $HEALTH_SCORE/100"

if [ "$HEALTH_SCORE" -lt 70 ]; then
    echo "⚠️ Low health score detected!"
    echo "🔄 Triggering reconnection..."
    curl -X POST "$MCP_URL/api/sse/reconnect"
    
    # Wait and check again
    sleep 10
    NEW_HEALTH=$(curl -s "$MCP_URL/api/sse/status" | jq -r '.connection.health_score // 0')
    echo "📊 New Health Score: $NEW_HEALTH/100"
fi

echo "✅ SSE monitoring completed"
`;

        const scriptPath = path.join(process.cwd(), 'scripts', 'monitor-sse-health.sh');
        fs.writeFileSync(scriptPath, monitoringScript);
        
        // Make executable
        try {
            fs.chmodSync(scriptPath, '755');
            console.log(`   ✅ Generated monitoring script: ${scriptPath}`);
        } catch (error) {
            console.log(`   ✅ Generated monitoring script: ${scriptPath} (chmod manually)`);
        }
    }

    maskUrl(url) {
        if (!url) return 'NOT_SET';
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;
        } catch {
            return url.substring(0, 20) + '...';
        }
    }
}

// Run optimization if called directly
if (require.main === module) {
    const optimizer = new SSEDockerOptimizer();
    optimizer.optimize().catch(console.error);
}

module.exports = SSEDockerOptimizer;
