# Uru Workspace Platform - Production Deployment Guide

This guide covers deploying the enhanced integration system to Elestio production environment.

## Overview

The enhanced integration system includes:
- **Multi-Integration Support**: 25+ business productivity integrations
- **White-Labeled OAuth**: Complete Uru branding with no external provider visibility
- **Scalable Architecture**: Microservices-based with auth-service, integration-service, composio-service
- **Dynamic MCP Tools**: Automatic tool generation based on connected integrations
- **Integration Hub UI**: Comprehensive dashboard for managing connections

## Pre-Deployment Checklist

### 1. Local Testing Complete
- [ ] All local tests pass (`./scripts/run-local-tests.sh`)
- [ ] Integration Hub UI working correctly
- [ ] OAuth flows tested with at least one provider
- [ ] MCP tool generation verified
- [ ] Database schema updated

### 2. Production Environment Variables
Ensure all required environment variables are configured in Elestio:

#### Core Configuration
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_service_key
JWT_SECRET=your_production_jwt_secret
ENCRYPTION_KEY=your_32_character_encryption_key
```

#### Service URLs (Elestio Production)
```bash
AUTH_SERVICE_URL=https://auth.uruenterprises.com
COMPOSIO_SERVICE_URL=https://oauth.uruenterprises.com
MCP_PROXY_URL=https://mcp.uruenterprises.com
FRONTEND_URL=https://app.uruenterprises.com
```

#### OAuth Credentials (Production)
```bash
# Google OAuth
GOOGLE_CLIENT_ID=your_production_google_client_id
GOOGLE_CLIENT_SECRET=your_production_google_client_secret

# Slack OAuth
SLACK_CLIENT_ID=your_production_slack_client_id
SLACK_CLIENT_SECRET=your_production_slack_client_secret

# Microsoft OAuth
MICROSOFT_CLIENT_ID=your_production_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_production_microsoft_client_secret

# Notion OAuth
NOTION_CLIENT_ID=your_production_notion_client_id
NOTION_CLIENT_SECRET=your_production_notion_client_secret

# Additional integrations...
```

### 3. Database Migration
Run the database setup script to create integration tables:

```bash
# Connect to production database and run:
python3 scripts/setup-integration-system.py
```

## Deployment Steps

### Step 1: Update Elestio Environment Variables

1. **Access Elestio Dashboard**
   - Navigate to your Uru Workspace Platform service
   - Go to "Environment Variables" section

2. **Add New Variables**
   - Add all OAuth client IDs and secrets for integrations you want to support
   - Update service URLs to use production domains
   - Ensure ENCRYPTION_KEY and JWT_SECRET are production-ready

3. **Verify Configuration**
   - Double-check all URLs use HTTPS and correct domains
   - Ensure no localhost or development URLs remain

### Step 2: Deploy Code Changes

1. **Push to Repository**
   ```bash
   git add .
   git commit -m "feat: Add multi-integration support with white-labeled OAuth"
   git push origin main
   ```

2. **Trigger Elestio Build**
   - Elestio will automatically detect the changes and rebuild containers
   - Monitor the build process in Elestio dashboard
   - Verify all services start successfully

### Step 3: Database Schema Update

1. **Run Migration Script**
   ```bash
   # Execute via Elestio terminal or container
   python3 scripts/setup-integration-system.py
   ```

2. **Verify Tables Created**
   - Check `integration_definitions` table exists and is populated
   - Verify `employee_integrations` table is created
   - Confirm indexes are in place

### Step 4: OAuth Provider Configuration

For each integration you want to support, configure OAuth applications:

#### Slack
1. Go to https://api.slack.com/apps
2. Create new app or update existing
3. Set redirect URI: `https://oauth.uruenterprises.com/api/uru/integrations/oauth/callback/slack`
4. Copy Client ID and Secret to Elestio environment variables

#### Microsoft Teams
1. Go to https://portal.azure.com
2. Navigate to Azure Active Directory > App registrations
3. Set redirect URI: `https://oauth.uruenterprises.com/api/uru/integrations/oauth/callback/microsoft_teams`
4. Configure required permissions
5. Copy Application ID and Secret

#### Notion
1. Go to https://www.notion.so/my-integrations
2. Create new integration
3. Set redirect URI: `https://oauth.uruenterprises.com/api/uru/integrations/oauth/callback/notion`
4. Copy OAuth client ID and secret

#### Continue for other integrations...

### Step 5: Validation Testing

1. **Service Health Checks**
   ```bash
   curl https://auth.uruenterprises.com/health
   curl https://oauth.uruenterprises.com/health
   curl https://mcp.uruenterprises.com/health
   ```

2. **Integration Endpoints**
   ```bash
   # Test available integrations (requires auth token)
   curl -H "Authorization: Bearer <token>" \
        https://oauth.uruenterprises.com/api/uru/integrations/available
   ```

3. **Frontend Integration Hub**
   - Navigate to https://app.uruenterprises.com/app/settings
   - Verify Integration Hub loads correctly
   - Test OAuth flow with at least one integration

## Post-Deployment Verification

### 1. Functional Testing

- [ ] Integration Hub displays all available integrations
- [ ] OAuth flows work for configured integrations
- [ ] Connection status updates correctly
- [ ] MCP tools are generated dynamically
- [ ] Tool execution returns appropriate responses

### 2. Performance Testing

- [ ] Page load times are acceptable
- [ ] API response times under 2 seconds
- [ ] No memory leaks or excessive resource usage
- [ ] Database queries are optimized

### 3. Security Testing

- [ ] All OAuth flows use HTTPS
- [ ] No sensitive data exposed in client-side code
- [ ] JWT tokens are properly validated
- [ ] Encryption keys are secure

## Monitoring and Maintenance

### 1. Set Up Monitoring

- **Service Health**: Monitor all service endpoints
- **Database Performance**: Track query performance and connection counts
- **OAuth Token Expiry**: Monitor and alert on expired tokens
- **Error Rates**: Track API error rates and response times

### 2. Regular Maintenance

- **Token Refresh**: Implement automatic token refresh for long-lived integrations
- **Database Cleanup**: Remove expired OAuth states and old tokens
- **Security Updates**: Keep OAuth credentials rotated and secure
- **Integration Updates**: Monitor for changes in third-party APIs

## Rollback Plan

If issues arise after deployment:

### 1. Immediate Rollback
```bash
# Revert to previous commit
git revert <commit-hash>
git push origin main

# Or rollback in Elestio dashboard
# Navigate to "Deployments" and select previous version
```

### 2. Database Rollback
```sql
-- If needed, drop new tables (CAUTION: This will lose data)
DROP TABLE IF EXISTS employee_integrations;
DROP TABLE IF EXISTS integration_definitions;
```

### 3. Environment Variables
- Remove new OAuth credentials
- Revert service URLs to previous configuration
- Restore previous environment variable state

## Troubleshooting

### Common Issues

1. **OAuth Redirect Mismatch**
   - Verify redirect URIs in OAuth provider settings
   - Ensure URLs use correct domain and HTTPS

2. **Service Communication Errors**
   - Check internal service URLs in environment variables
   - Verify network connectivity between services

3. **Database Connection Issues**
   - Confirm Supabase credentials are correct
   - Check database permissions and network access

4. **Frontend Integration Hub Not Loading**
   - Verify API endpoints are accessible
   - Check browser console for JavaScript errors
   - Confirm authentication is working

### Debug Commands

```bash
# Check service logs in Elestio
docker logs <container-name>

# Test API endpoints
curl -v https://oauth.uruenterprises.com/api/uru/integrations/available

# Verify database connection
python3 -c "from supabase import create_client; print('DB OK')"
```

## Success Criteria

Deployment is considered successful when:

- [ ] All services are healthy and responding
- [ ] Integration Hub loads and displays integrations correctly
- [ ] At least one OAuth integration can be connected successfully
- [ ] MCP tools are generated and accessible
- [ ] No critical errors in service logs
- [ ] Performance metrics are within acceptable ranges

## Next Steps

After successful deployment:

1. **User Communication**: Notify users about new integration capabilities
2. **Documentation**: Update user guides and help documentation
3. **Training**: Provide training on new integration features
4. **Feedback Collection**: Gather user feedback and iterate
5. **Expansion**: Plan for additional integrations based on user demand

## Support

For deployment issues:

1. Check Elestio service logs and status
2. Verify environment variable configuration
3. Test individual service endpoints
4. Review OAuth provider configurations
5. Consult this deployment guide and main documentation
