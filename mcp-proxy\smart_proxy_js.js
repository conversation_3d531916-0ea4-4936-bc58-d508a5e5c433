// mcp-proxy/smart_proxy_js.js
// Fixed version based on original working implementation
// Maintains the working parameter format but adds persistent SSE session

const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const EventEmitter = require('events');


const ToolType = {
    PERSONAL_MCP: 'personal_mcp',
    COMPANY_N8N: 'company_n8n'
};

class MCPServerConfig {
    constructor(name, command, args, env, tools) {
        this.name = name;
        this.command = command;
        this.args = args;
        this.env = env;
        this.tools = tools;
    }
}

class CompanyTool {
    constructor(name, description, parameters) {
        this.name = name;
        this.description = description;
        this.parameters = parameters;
    }
}

class SmartMCPProxy extends EventEmitter {
    constructor() {
        super();

        // Import environment utilities
        let envConfig;
        try {
            const path = require('path');
            const { envConfig: config } = require(path.join(__dirname, '..', 'shared', 'environment.js'));
            envConfig = config;
        } catch (error) {
            console.warn('⚠️  Environment utilities not available in SmartMCPProxy, using fallback configuration');
            envConfig = null;
        }

        // Configure service URLs for microservices architecture
        this.authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://auth-service:8003';
        this.integrationsServiceUrl = process.env.INTEGRATIONS_SERVICE_URL || 'http://integration-service:8002';
        console.log(`[INIT] 🔗 Auth Service URL: ${this.authServiceUrl}`);
        console.log(`[INIT] 🔗 Integrations Service URL: ${this.integrationsServiceUrl}`);

        // For backward compatibility, set oauthServiceUrl to auth service
        this.oauthServiceUrl = this.authServiceUrl;

        this.n8nSseUrl = process.env.N8N_SSE_URL || 'https://n8n-uru-u46170.vm.elestio.app/mcp/4caa0a7f-1251-45a8-97a5-7663841a2c9b/sse';
        this.n8nBaseUrl = this.n8nSseUrl.replace('/sse', '');

        // Check if n8n URL is a placeholder
        this.n8nEnabled = !this.n8nSseUrl.includes('your-n8n-sse-endpoint-url') &&
                         !this.n8nSseUrl.includes('placeholder') &&
                         this.n8nSseUrl.startsWith('http');

        console.log(`[INIT] 📡 n8n SSE URL: ${this.n8nSseUrl}`);
        console.log(`[INIT] 🔌 n8n Integration: ${this.n8nEnabled ? 'ENABLED' : 'DISABLED (placeholder URL)'}`);

        if (!this.n8nEnabled) {
            console.log(`[INIT] ⚠️ n8n integration disabled - using placeholder URL`);
        }
        
        // MCP Server configurations for personal tools
        this.mcpServers = {};
        this.companyTools = {};
        this.activeMCPProcesses = {}; // Store active MCP processes per employee
        
        // Enhanced persistent SSE session management
        this.persistentSSE = {
            connection: null,
            messagesEndpoint: null,
            sessionId: null,
            connected: false,
            connecting: false,
            pendingRequests: new Map(),
            // Connection state tracking
            connectionAttempts: 0,
            maxRetries: 10,
            baseRetryDelay: 1000, // 1 second
            maxRetryDelay: 30000, // 30 seconds
            lastConnectionTime: null,
            lastError: null,
            // Health monitoring
            heartbeatInterval: null,
            lastHeartbeat: null,
            connectionId: null
        };
        
        this._initializeMCPServers();
        this._initializeCompanyTools();
        
        // Start persistent SSE connection only if n8n is enabled
        if (this.n8nEnabled) {
            this._establishPersistentSSE();
        }
        
        console.log(`🔗 Initialized Smart MCP Proxy`);
        console.log(`📡 n8n SSE URL: ${this.n8nSseUrl}`);

        // Cache for integration tools
        this.integrationToolsCache = {
            data: null,
            lastFetch: null,
            ttl: 60000 // Cache for 1 minute
        };

        // Composio service URL for integration management
        this.composioServiceUrl = process.env.COMPOSIO_SERVICE_URL || 'http://localhost:8001';
        console.log(`🧩 Composio service URL: ${this.composioServiceUrl}`);
    }

    _initializeMCPServers() {
        // Google Drive MCP Server (isaacphi)
        this.mcpServers.gdrive = new MCPServerConfig(
            'gdrive',
            'npx',
            ['-y', '@isaacphi/mcp-gdrive'],
            {
                CLIENT_ID: '', // Will be injected per employee
                CLIENT_SECRET: '', // Will be injected per employee
                GDRIVE_CREDS_DIR: '' // Will be set to temp directory
            },
            ['gdrive_search', 'gdrive_read_file', 'gsheets_read', 'gsheets_update_cell']
        );

        // Gmail MCP Server (GongRzhe)
        this.mcpServers.gmail = new MCPServerConfig(
            'gmail',
            'npx',
            ['@gongrzhe/server-gmail-autoauth-mcp'],
            {}, // Uses global config in ~/.gmail-mcp/
            ['send_email', 'search_emails', 'create_draft', 'list_emails'] // Remove get_email, modify_labels, delete_email
        );

        // Google Calendar MCP Server (nspady)
        this.mcpServers.calendar = new MCPServerConfig(
            'calendar',
            'npx',
            ['@cocal/google-calendar-mcp'],
            {
                GOOGLE_OAUTH_CREDENTIALS: '' // Will be set to temp credentials file
            },
            ['list_events', 'create_event', 'update_event', 'delete_event', 'get_free_busy']
        );
    }


    _getGmailToolSchema(toolName) {
        const schemas = {
            'search_emails': {
                type: 'object',
                properties: {
                    query: { type: 'string', description: 'Gmail search query (e.g., "from:<EMAIL>", "subject:invoice")' },
                    max_results: { type: 'number', description: 'Maximum results to return (default: 10)', default: 10 }
                },
                required: ['query']
            },
            'send_email': {
                type: 'object',
                properties: {
                    to: { type: 'string', description: 'Recipient email address' },
                    subject: { type: 'string', description: 'Email subject' },
                    body: { type: 'string', description: 'Email body content' },
                    cc: { type: 'string', description: 'CC recipients (optional)' },
                    bcc: { type: 'string', description: 'BCC recipients (optional)' }
                },
                required: ['to', 'subject', 'body']
            },
            'get_email': {
                type: 'object',
                properties: {
                    message_id: { type: 'string', description: 'Gmail message ID' }
                },
                required: ['message_id']
            },
            'create_draft': {
                type: 'object',
                properties: {
                    to: { type: 'string', description: 'Recipient email address' },
                    subject: { type: 'string', description: 'Email subject' },
                    body: { type: 'string', description: 'Email body content' }
                },
                required: ['to', 'subject', 'body']
            },
            'list_emails': {
                type: 'object',
                properties: {
                    max_results: { type: 'number', description: 'Maximum emails to return', default: 20 },
                    label_ids: { type: 'array', items: { type: 'string' }, description: 'Filter by label IDs' }
                }
            }
        };
    
        return schemas[toolName] || {
            type: 'object',
            properties: {},
            required: []
        };
    }

    _initializeCompanyTools() {
        // Use the EXACT original tool definitions that were working
        this.companyTools = {
            // Gmail MCP Tools
            'Ignition_Maguire_Gmail_MCP_List': new CompanyTool(
                'Ignition_Maguire_Gmail_MCP_List',
                'List available Gmail MCP tools for Ignition',
                {
                    type: 'object',
                    properties: {},
                    additionalProperties: true
                }
            ),

            'Ignition_Maguire_Gmail_MCP_Execute': new CompanyTool(
                'Ignition_Maguire_Gmail_MCP_Execute',
                'Execute Gmail MCP operations for Ignition',
                {
                    type: 'object',
                    properties: {
                        tool: { type: 'string', description: 'the tool selected' },
                        Tool_Parameters: { type: 'string', description: 'Tool parameters as JSON string' }
                    },
                    required: ['tool', 'Tool_Parameters'],
                    additionalProperties: true
                }
            ),

            // Drive MCP Tools
            'Ignition_Drive_MCP_List': new CompanyTool(
                'Ignition_Drive_MCP_List',
                'List available Google Drive MCP tools for Ignition',
                {
                    type: 'object',
                    properties: {},
                    additionalProperties: true
                }
            ),

            'Ignition_Drive_MCP_Execute': new CompanyTool(
                'Ignition_Drive_MCP_Execute',
                'Execute Google Drive MCP operations for Ignition',
                {
                    type: 'object',
                    properties: {
                        tool: { type: 'string', description: 'the tool selected' },
                        Tool_Parameters: { type: 'string', description: 'Tool parameters as JSON string' }
                    },
                    required: ['tool', 'Tool_Parameters'],
                    additionalProperties: true
                }
            ),

            // Direct n8n Tools
            'Slack_Post': new CompanyTool(
                'Slack_Post',
                'Post a message to an Ignition Slack channel',
                {
                    type: 'object',
                    properties: {
                        input: { type: 'string', description: 'Message to post to Slack' }
                    },
                    additionalProperties: true
                }
            ),

            'Client_Agents_Tool': new CompanyTool(
                'Client_Agents_Tool',
                'Talk to an agent for an Ignition vendor/client that has knowledge of interactions',
                {
                    type: 'object',
                    properties: {
                        input: { type: 'string', description: 'Query for the client agent' }
                    },
                    additionalProperties: true
                }
            ),

            'Transcript_Log_Query': new CompanyTool(
                'Transcript_Log_Query',
                'Access Ignition transcript logs for client interaction analysis',
                {
                    type: 'object',
                    properties: {
                        input: { type: 'string', description: 'Search query for transcript logs' }
                    },
                    additionalProperties: true
                }
            ),

            'Client_Table_Query': new CompanyTool(
                'Client_Table_Query',
                'Access the Ignition Client table for client information',
                {
                    type: 'object',
                    properties: {
                        input: { type: 'string', description: 'Query for client table data' }
                    },
                    additionalProperties: true
                }
            ),

            // Webflow Tools
            'Webflow_Tools': new CompanyTool(
                'Webflow_Tools',
                'List available Webflow management operations',
                {
                    type: 'object',
                    properties: {},
                    additionalProperties: true
                }
            ),

            'Webflow_Execute': new CompanyTool(
                'Webflow_Execute',
                'Execute Webflow operations like updating content, publishing, managing CMS',
                {
                    type: 'object',
                    properties: {
                        tool: { type: 'string', description: 'the tool selected' },
                        Tool_Parameters: { type: 'string', description: 'Tool parameters as JSON string' }
                    },
                    required: ['tool', 'Tool_Parameters'],
                    additionalProperties: true
                }
            ),

            // Uru Tools
            'Uru_Tools': new CompanyTool(
                'Uru_Tools',
                'List available Uru analysis tools (transcript, SOW, agreement analyzers)',
                {
                    type: 'object',
                    properties: {},
                    additionalProperties: true
                }
            ),

            'Uru_Execute': new CompanyTool(
                'Uru_Execute',
                'Execute Uru analysis tools for advanced document analysis',
                {
                    type: 'object',
                    properties: {
                        tool: { type: 'string', description: 'the tool selected' },
                        Tool_Parameters: { type: 'string', description: 'Tool parameters as JSON string' }
                    },
                    required: ['tool', 'Tool_Parameters'],
                    additionalProperties: true
                }
            )
        };
    }

    // === PERSISTENT SSE SESSION MANAGEMENT ===

    async _establishPersistentSSE() {
        // Prevent multiple concurrent connection attempts
        if (this.persistentSSE.connecting) {
            console.log(`[SSE-PERSISTENT] ⏳ Connection attempt already in progress`);
            return;
        }

        // Check if we've exceeded max retries
        if (this.persistentSSE.connectionAttempts >= this.persistentSSE.maxRetries) {
            console.error(`[SSE-PERSISTENT] 🚫 Max retry attempts (${this.persistentSSE.maxRetries}) exceeded`);
            this._scheduleReconnection(this.persistentSSE.maxRetryDelay);
            return;
        }

        this.persistentSSE.connecting = true;
        this.persistentSSE.connectionAttempts++;
        this.persistentSSE.connectionId = `sse_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        try {
            console.log(`[SSE-PERSISTENT] 🚀 Establishing connection (attempt ${this.persistentSSE.connectionAttempts}/${this.persistentSSE.maxRetries})`);
            console.log(`[SSE-PERSISTENT] 🔗 Connection ID: ${this.persistentSSE.connectionId}`);

            // Create AbortController for timeout handling
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, 30000); // 30 second timeout

            const response = await fetch(this.n8nSseUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'User-Agent': 'Uru-MCP-Proxy/2.0',
                    'X-Connection-ID': this.persistentSSE.connectionId
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`SSE connection failed: ${response.status} ${response.statusText}`);
            }

            console.log(`[SSE-PERSISTENT] ✅ Connection established successfully`);

            // Reset connection state on successful connection
            this.persistentSSE.connection = response;
            this.persistentSSE.connected = true;
            this.persistentSSE.connecting = false;
            this.persistentSSE.connectionAttempts = 0; // Reset on success
            this.persistentSSE.lastConnectionTime = Date.now();
            this.persistentSSE.lastError = null;

            // Start heartbeat monitoring
            this._startHeartbeat();

            let responseBuffer = '';

            response.body.on('data', (chunk) => {
                const data = chunk.toString();
                responseBuffer += data;
                this.persistentSSE.lastHeartbeat = Date.now();

                console.log(`[SSE-PERSISTENT] 📨 Raw chunk: ${data.substring(0, 200)}...`);

                // Parse SSE events
                const lines = data.split('\n');
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();

                    if (line.startsWith('event: endpoint')) {
                        // Next line should contain the endpoint
                        const nextLine = lines[i + 1];
                        if (nextLine && nextLine.startsWith('data: ')) {
                            this.persistentSSE.messagesEndpoint = nextLine.substring(6).trim();
                            this.persistentSSE.sessionId = this._extractSessionId(this.persistentSSE.messagesEndpoint);

                            console.log(`[SSE-PERSISTENT] 🔗 Messages endpoint: ${this.persistentSSE.messagesEndpoint}`);
                            console.log(`[SSE-PERSISTENT] 🔑 Session ID: ${this.persistentSSE.sessionId}`);
                            i++; // Skip the data line
                        }
                    } else if (line.startsWith('data: ')) {
                        const eventData = line.substring(6).trim();

                        // Skip if it's the endpoint URL or empty
                        if (eventData === this.persistentSSE.messagesEndpoint || !eventData || eventData.length < 5) {
                            continue;
                        }

                        console.log(`[SSE-PERSISTENT] 🔍 Processing response data: ${eventData}`);

                        this._handleResponseData(eventData);
                    }
                }
            });

            response.body.on('end', () => {
                console.log(`[SSE-PERSISTENT] 🏁 Connection ended gracefully`);
                this._handleConnectionEnd('end');
            });

            response.body.on('error', (error) => {
                console.error(`[SSE-PERSISTENT] ❌ Connection error:`, error);
                this._handleConnectionEnd('error', error);
            });

        } catch (error) {
            console.error(`[SSE-PERSISTENT] ❌ Failed to establish connection:`, error);
            this.persistentSSE.lastError = error.message;
            this._handleConnectionEnd('error', error);
        }
    }

    _handleResponseData(eventData) {
        try {
            // Try to parse as JSON first
            const parsed = JSON.parse(eventData);
            console.log(`[SSE-PERSISTENT] ✅ Parsed JSON response:`, JSON.stringify(parsed, null, 2));
            
            // Check if this matches a pending request
            if (parsed.id && this.persistentSSE.pendingRequests.has(parsed.id)) {
                const { resolve, reject } = this.persistentSSE.pendingRequests.get(parsed.id);
                this.persistentSSE.pendingRequests.delete(parsed.id);
                
                console.log(`[SSE-PERSISTENT] 🎯 Matched request ${parsed.id}`);
                
                if (parsed.error) {
                    reject(new Error(`n8n MCP error: ${JSON.stringify(parsed.error)}`));
                } else {
                    resolve(parsed.result || parsed);
                }
                return;
            }
            
            // Check for general MCP response
            if (parsed.jsonrpc === "2.0" && this.persistentSSE.pendingRequests.size > 0) {
                console.log(`[SSE-PERSISTENT] 🎯 Generic MCP response`);
                const [oldestId, { resolve, reject }] = this.persistentSSE.pendingRequests.entries().next().value;
                this.persistentSSE.pendingRequests.delete(oldestId);
                
                if (parsed.error) {
                    reject(new Error(`n8n MCP error: ${JSON.stringify(parsed.error)}`));
                } else {
                    resolve(parsed.result || parsed);
                }
                return;
            }
            
        } catch (e) {
            // Not JSON - treat as direct tool output
            console.log(`[SSE-PERSISTENT] 📄 Non-JSON response: ${eventData}`);
            
            if (this.persistentSSE.pendingRequests.size > 0 && eventData.length > 10) {
                console.log(`[SSE-PERSISTENT] 🎯 Treating as tool output`);
                const [oldestId, { resolve }] = this.persistentSSE.pendingRequests.entries().next().value;
                this.persistentSSE.pendingRequests.delete(oldestId);
                resolve({
                    success: true,
                    result: eventData,
                    type: 'text'
                });
            }
        }
    }

    _extractSessionId(endpoint) {
        const match = endpoint.match(/sessionId=([^&]+)/);
        return match ? match[1] : null;
    }

    _handleConnectionEnd(reason, error = null) {
        console.log(`[SSE-PERSISTENT] 🔄 Handling connection end: ${reason}`);

        // Clean up connection state
        this.persistentSSE.connected = false;
        this.persistentSSE.connecting = false;
        this.persistentSSE.connection = null;

        // Stop heartbeat monitoring
        this._stopHeartbeat();

        // Store error if provided
        if (error) {
            this.persistentSSE.lastError = error.message;
        }

        // Schedule reconnection with exponential backoff
        this._scheduleReconnection();
    }

    _scheduleReconnection(customDelay = null) {
        if (customDelay) {
            console.log(`[SSE-PERSISTENT] ⏰ Scheduling reconnection in ${customDelay}ms (custom delay)`);
            setTimeout(() => this._establishPersistentSSE(), customDelay);
            return;
        }

        // Calculate exponential backoff delay
        const delay = Math.min(
            this.persistentSSE.baseRetryDelay * Math.pow(2, this.persistentSSE.connectionAttempts - 1),
            this.persistentSSE.maxRetryDelay
        );

        console.log(`[SSE-PERSISTENT] ⏰ Scheduling reconnection in ${delay}ms (attempt ${this.persistentSSE.connectionAttempts})`);
        setTimeout(() => this._establishPersistentSSE(), delay);
    }

    _startHeartbeat() {
        // Clear any existing heartbeat
        this._stopHeartbeat();

        this.persistentSSE.lastHeartbeat = Date.now();

        // Check connection health every 30 seconds
        this.persistentSSE.heartbeatInterval = setInterval(() => {
            const now = Date.now();
            const timeSinceLastHeartbeat = now - (this.persistentSSE.lastHeartbeat || now);

            // If no data received for 60 seconds, consider connection stale
            if (timeSinceLastHeartbeat > 60000) {
                console.warn(`[SSE-PERSISTENT] 💔 No heartbeat for ${timeSinceLastHeartbeat}ms, reconnecting...`);
                this._handleConnectionEnd('heartbeat_timeout');
            }
        }, 30000);
    }

    _stopHeartbeat() {
        if (this.persistentSSE.heartbeatInterval) {
            clearInterval(this.persistentSSE.heartbeatInterval);
            this.persistentSSE.heartbeatInterval = null;
        }
    }

    // Enhanced connection status method
    getSSEConnectionStatus() {
        return {
            connected: this.persistentSSE.connected,
            connecting: this.persistentSSE.connecting,
            connectionAttempts: this.persistentSSE.connectionAttempts,
            maxRetries: this.persistentSSE.maxRetries,
            lastConnectionTime: this.persistentSSE.lastConnectionTime,
            lastError: this.persistentSSE.lastError,
            lastHeartbeat: this.persistentSSE.lastHeartbeat,
            connectionId: this.persistentSSE.connectionId,
            messagesEndpoint: this.persistentSSE.messagesEndpoint,
            sessionId: this.persistentSSE.sessionId,
            pendingRequests: this.persistentSSE.pendingRequests.size
        };
    }

    // === MAIN API METHODS ===

    async getAvailableTools(employeeToken) {
        const availableTools = [];

        // Get employee's Composio connections
        console.log(`[TOOLS] 🔍 Getting Composio connections for employee...`);
        const composioConnections = await this._getComposioConnections(employeeToken);
        console.log(`[TOOLS] 📊 Composio connections:`, JSON.stringify(composioConnections, null, 2));

        // Add personal tools based on Composio connections
        if (composioConnections && composioConnections.success && composioConnections.connections) {
            for (const connection of composioConnections.connections) {
                if (connection.status === 'active') {
                    console.log(`[TOOLS] ✅ ${connection.app_name} connected - adding tools`);

                    // Add tools based on connected app
                    const appTools = this._getToolsForApp(connection.app_name);
                    for (const tool of appTools) {
                        console.log(`[TOOLS] ➕ Adding ${connection.app_name} tool: ${tool.name}`);
                        availableTools.push({
                            type: 'function',
                            function: {
                                name: tool.name,
                                description: `${connection.app_name}: ${tool.description}`,
                                parameters: tool.parameters
                            }
                        });
                    }
                }
            }
        } else {
            console.log(`[TOOLS] ❌ No Composio connections found`);
        }
        
        // Add company tools (always available)
        console.log(`[TOOLS] 🏢 Adding ${Object.keys(this.companyTools).length} company tools`);
        for (const [toolName, tool] of Object.entries(this.companyTools)) {
            availableTools.push({
                type: 'function',
                function: {
                    name: toolName,
                    description: tool.description,
                    parameters: tool.parameters
                }
            });
        }
        
        console.log(`[TOOLS] ✅ Total tools available: ${availableTools.length}`);
        return availableTools;
    }

    async executeTool(toolName, parameters, employeeToken) {
        // Determine if this is a personal tool (route to Composio) or company tool (route to n8n)
        if (this._isPersonalTool(toolName)) {
            return await this._executePersonalComposioTool(toolName, parameters, employeeToken);
        } else if (this.companyTools[toolName]) {
            return await this._executeCompanyN8NTool(toolName, parameters, employeeToken);
        } else {
            return { error: `Tool '${toolName}' not found` };
        }
    }

    _isPersonalTool(toolName) {
        // Define personal productivity tools that should be routed to Composio
        const personalTools = [
            // Gmail tools
            'send_email', 'search_emails', 'get_email', 'create_draft', 'list_emails',
            // Google Drive tools
            'gdrive_search', 'gdrive_read_file', 'gsheets_read', 'gsheets_update_cell',
            // Google Calendar tools
            'list_events', 'create_event', 'get_event', 'update_event', 'delete_event',
            // GitHub tools
            'github_list_repos', 'github_get_repo', 'github_create_issue', 'github_list_issues',
            // Slack tools
            'slack_send_message', 'slack_list_channels', 'slack_get_channel_history'
        ];

        return personalTools.includes(toolName);
    }

    async _getComposioConnections(employeeToken) {
        try {
            if (employeeToken === 'anonymous') {
                console.log(`[COMPOSIO] ⚠️ Anonymous token - no connections available`);
                return { success: false, connections: [] };
            }

            const composioServiceUrl = process.env.COMPOSIO_SERVICE_URL || 'http://composio-service:8001';
            const url = `${composioServiceUrl}/api/uru/integrations/connections`;
            console.log(`[COMPOSIO] 🔗 Requesting connections from: ${url}`);

            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${employeeToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                console.error(`[COMPOSIO] ❌ Failed to get connections - status: ${response.status}`);
                return { success: false, connections: [] };
            }

            const result = await response.json();
            console.log(`[COMPOSIO] ✅ Got connections:`, result);

            return result;

        } catch (error) {
            console.error(`[COMPOSIO] ❌ Failed to get connections:`, error.message);
            return { success: false, connections: [] };
        }
    }

    _getToolsForApp(appName) {
        // Define available tools for each app
        const appToolsMap = {
            gmail: [
                { name: 'send_email', description: 'Send an email', parameters: { type: 'object', properties: { to: { type: 'string' }, subject: { type: 'string' }, body: { type: 'string' } }, required: ['to', 'subject', 'body'] } },
                { name: 'search_emails', description: 'Search emails', parameters: { type: 'object', properties: { query: { type: 'string' } }, required: ['query'] } },
                { name: 'list_emails', description: 'List recent emails', parameters: { type: 'object', properties: { limit: { type: 'number', default: 10 } } } },
                { name: 'create_draft', description: 'Create email draft', parameters: { type: 'object', properties: { to: { type: 'string' }, subject: { type: 'string' }, body: { type: 'string' } }, required: ['to', 'subject', 'body'] } }
            ],
            drive: [
                { name: 'gdrive_search', description: 'Search Google Drive files', parameters: { type: 'object', properties: { query: { type: 'string' } }, required: ['query'] } },
                { name: 'gdrive_read_file', description: 'Read Google Drive file', parameters: { type: 'object', properties: { fileId: { type: 'string' } }, required: ['fileId'] } },
                { name: 'gsheets_read', description: 'Read Google Sheets', parameters: { type: 'object', properties: { spreadsheetId: { type: 'string' }, range: { type: 'string' } }, required: ['spreadsheetId'] } },
                { name: 'gsheets_update_cell', description: 'Update Google Sheets cell', parameters: { type: 'object', properties: { spreadsheetId: { type: 'string' }, range: { type: 'string' }, value: { type: 'string' } }, required: ['spreadsheetId', 'range', 'value'] } }
            ],
            calendar: [
                { name: 'list_events', description: 'List calendar events', parameters: { type: 'object', properties: { timeMin: { type: 'string' }, timeMax: { type: 'string' } } } },
                { name: 'create_event', description: 'Create calendar event', parameters: { type: 'object', properties: { summary: { type: 'string' }, start: { type: 'string' }, end: { type: 'string' } }, required: ['summary', 'start', 'end'] } },
                { name: 'get_event', description: 'Get calendar event', parameters: { type: 'object', properties: { eventId: { type: 'string' } }, required: ['eventId'] } }
            ],
            github: [
                { name: 'github_list_repos', description: 'List GitHub repositories', parameters: { type: 'object', properties: { type: { type: 'string', enum: ['all', 'owner', 'public', 'private'] } } } },
                { name: 'github_get_repo', description: 'Get GitHub repository', parameters: { type: 'object', properties: { owner: { type: 'string' }, repo: { type: 'string' } }, required: ['owner', 'repo'] } },
                { name: 'github_create_issue', description: 'Create GitHub issue', parameters: { type: 'object', properties: { owner: { type: 'string' }, repo: { type: 'string' }, title: { type: 'string' }, body: { type: 'string' } }, required: ['owner', 'repo', 'title'] } }
            ],
            slack: [
                { name: 'slack_send_message', description: 'Send Slack message', parameters: { type: 'object', properties: { channel: { type: 'string' }, text: { type: 'string' } }, required: ['channel', 'text'] } },
                { name: 'slack_list_channels', description: 'List Slack channels', parameters: { type: 'object', properties: {} } },
                { name: 'slack_get_channel_history', description: 'Get Slack channel history', parameters: { type: 'object', properties: { channel: { type: 'string' }, limit: { type: 'number', default: 10 } }, required: ['channel'] } }
            ],
            microsoft_teams: [
                { name: 'teams_send_message', description: 'Send Teams message', parameters: { type: 'object', properties: { team_id: { type: 'string' }, channel_id: { type: 'string' }, message: { type: 'string' } }, required: ['team_id', 'channel_id', 'message'] } },
                { name: 'teams_list_teams', description: 'List Microsoft Teams', parameters: { type: 'object', properties: { limit: { type: 'number', default: 20 } } } },
                { name: 'teams_create_meeting', description: 'Create Teams meeting', parameters: { type: 'object', properties: { subject: { type: 'string' }, start_time: { type: 'string' }, end_time: { type: 'string' } }, required: ['subject', 'start_time', 'end_time'] } }
            ],
            notion: [
                { name: 'notion_create_page', description: 'Create Notion page', parameters: { type: 'object', properties: { title: { type: 'string' }, content: { type: 'string' }, parent_id: { type: 'string' } }, required: ['title', 'content'] } },
                { name: 'notion_search_pages', description: 'Search Notion pages', parameters: { type: 'object', properties: { query: { type: 'string' }, limit: { type: 'number', default: 10 } }, required: ['query'] } },
                { name: 'notion_update_page', description: 'Update Notion page', parameters: { type: 'object', properties: { page_id: { type: 'string' }, content: { type: 'string' } }, required: ['page_id', 'content'] } }
            ],
            airtable: [
                { name: 'airtable_create_record', description: 'Create Airtable record', parameters: { type: 'object', properties: { base_id: { type: 'string' }, table_name: { type: 'string' }, fields: { type: 'object' } }, required: ['base_id', 'table_name', 'fields'] } },
                { name: 'airtable_list_records', description: 'List Airtable records', parameters: { type: 'object', properties: { base_id: { type: 'string' }, table_name: { type: 'string' }, max_records: { type: 'number', default: 20 } }, required: ['base_id', 'table_name'] } },
                { name: 'airtable_update_record', description: 'Update Airtable record', parameters: { type: 'object', properties: { base_id: { type: 'string' }, table_name: { type: 'string' }, record_id: { type: 'string' }, fields: { type: 'object' } }, required: ['base_id', 'table_name', 'record_id', 'fields'] } }
            ],
            asana: [
                { name: 'asana_create_task', description: 'Create Asana task', parameters: { type: 'object', properties: { name: { type: 'string' }, notes: { type: 'string' }, project_id: { type: 'string' }, assignee: { type: 'string' } }, required: ['name'] } },
                { name: 'asana_list_tasks', description: 'List Asana tasks', parameters: { type: 'object', properties: { project_id: { type: 'string' }, assignee: { type: 'string' }, limit: { type: 'number', default: 20 } } } },
                { name: 'asana_update_task', description: 'Update Asana task', parameters: { type: 'object', properties: { task_id: { type: 'string' }, name: { type: 'string' }, notes: { type: 'string' }, completed: { type: 'boolean' } }, required: ['task_id'] } }
            ],
            hubspot: [
                { name: 'hubspot_create_contact', description: 'Create HubSpot contact', parameters: { type: 'object', properties: { email: { type: 'string' }, firstname: { type: 'string' }, lastname: { type: 'string' }, company: { type: 'string' } }, required: ['email'] } },
                { name: 'hubspot_list_contacts', description: 'List HubSpot contacts', parameters: { type: 'object', properties: { limit: { type: 'number', default: 20 }, search: { type: 'string' } } } },
                { name: 'hubspot_create_deal', description: 'Create HubSpot deal', parameters: { type: 'object', properties: { dealname: { type: 'string' }, amount: { type: 'number' }, pipeline: { type: 'string' }, dealstage: { type: 'string' } }, required: ['dealname'] } }
            ],
            salesforce: [
                { name: 'salesforce_create_lead', description: 'Create Salesforce lead', parameters: { type: 'object', properties: { LastName: { type: 'string' }, Company: { type: 'string' }, Email: { type: 'string' }, Phone: { type: 'string' } }, required: ['LastName', 'Company'] } },
                { name: 'salesforce_list_leads', description: 'List Salesforce leads', parameters: { type: 'object', properties: { limit: { type: 'number', default: 20 }, status: { type: 'string' } } } },
                { name: 'salesforce_create_opportunity', description: 'Create Salesforce opportunity', parameters: { type: 'object', properties: { Name: { type: 'string' }, Amount: { type: 'number' }, CloseDate: { type: 'string' }, StageName: { type: 'string' } }, required: ['Name', 'CloseDate', 'StageName'] } }
            ]
        };

        return appToolsMap[appName] || [];
    }

    async _executePersonalComposioTool(toolName, parameters, employeeToken) {
        try {
            console.log(`[COMPOSIO] 🚀 Executing personal tool: ${toolName}`);
            console.log(`[COMPOSIO] 📤 Parameters:`, JSON.stringify(parameters, null, 2));

            // Get Composio service URL from environment
            const composioServiceUrl = process.env.COMPOSIO_SERVICE_URL || 'http://composio-service:8001';

            // Prepare request payload for Composio service
            const requestPayload = {
                tool_name: toolName,
                parameters: parameters || {}
            };

            console.log(`[COMPOSIO] 📡 Calling Composio service: ${composioServiceUrl}/api/uru/integrations/execute`);

            // Call Composio service
            const response = await fetch(`${composioServiceUrl}/api/uru/integrations/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${employeeToken}`,
                    'User-Agent': 'Uru-MCP-Proxy/2.0'
                },
                body: JSON.stringify(requestPayload)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`[COMPOSIO] ❌ HTTP ${response.status}: ${errorText}`);
                return {
                    success: false,
                    error: `Composio service error: ${response.status} ${response.statusText}`,
                    tool_name: toolName,
                    source: 'composio_service'
                };
            }

            const result = await response.json();
            console.log(`[COMPOSIO] ✅ Tool execution successful: ${toolName}`);

            return {
                success: result.success || true,
                result: result.result || result,
                tool_name: toolName,
                source: 'composio_service',
                execution_time: result.execution_time
            };

        } catch (error) {
            console.error(`[COMPOSIO] ❌ Error executing ${toolName}:`, error);
            return {
                success: false,
                error: `Composio tool execution failed: ${error.message}`,
                tool_name: toolName,
                source: 'composio_service'
            };
        }
    }

    async _executeCompanyN8NTool(toolName, parameters, employeeToken) {
        try {
            console.log(`[N8N] 🚀 Executing ${toolName}`);
            console.log(`[N8N] 📤 Original parameters:`, JSON.stringify(parameters, null, 2));

            // Check if n8n is enabled
            if (!this.n8nEnabled) {
                console.log(`[N8N] ❌ n8n integration disabled - cannot execute ${toolName}`);
                return {
                    success: false,
                    error: 'n8n integration is disabled (placeholder URL configured)',
                    tool_name: toolName,
                    source: 'n8n_disabled'
                };
            }

            // Wait for persistent SSE session to be ready
            await this._waitForSSEReady();

            // Use the ORIGINAL parameter formatting that was working
            const formattedParams = this._formatParametersForN8N(toolName, parameters);
            
            // Create MCP request using the EXACT format that was working
            const mcpRequest = {
                jsonrpc: '2.0',
                id: Date.now(),
                method: 'tools/call',
                params: {
                    name: toolName,
                    arguments: formattedParams
                }
            };

            console.log(`[N8N] 📤 Sending MCP request:`, JSON.stringify(mcpRequest, null, 2));

            // Send via persistent session
            const result = await this._sendToPersistentSession(mcpRequest);

            return {
                success: true,
                result: result,
                tool_name: toolName,
                source: 'n8n_persistent_sse'
            };

        } catch (error) {
            console.error(`[N8N] ❌ Error:`, error.message);
            return { error: `n8n tool execution failed: ${error.message}` };
        }
    }

    // Use the ORIGINAL parameter formatting that was working
    _formatParametersForN8N(toolName, parameters) {
        console.log(`[N8N] 🔧 Formatting parameters for ${toolName}`);
        
        // Use original logic from working version
        if (['Client_Table_Query', 'Transcript_Log_Query', 'Slack_Post', 'Client_Agents_Tool'].includes(toolName)) {
            // Simple tools that expect direct input
            if (typeof parameters === 'string') {
                return { input: parameters };
            } else if (parameters && typeof parameters === 'object') {
                if (parameters.input) {
                    return { input: parameters.input };
                } else if (parameters.query) {
                    return { input: parameters.query };  // Map query to input
                } else {
                    return { input: JSON.stringify(parameters) };
                }
            } else {
                return { input: "default input" };
            }
        }
        
        // Execute tools that use tool + Tool_Parameters pattern
        if (['Ignition_Maguire_Gmail_MCP_Execute', 'Ignition_Drive_MCP_Execute', 'Webflow_Execute', 'Uru_Execute'].includes(toolName)) {
            return {
                tool: parameters.tool || 'list',
                Tool_Parameters: typeof parameters.Tool_Parameters === 'string' 
                    ? parameters.Tool_Parameters 
                    : JSON.stringify(parameters.Tool_Parameters || parameters || {})
            };
        }
        
        // List tools
        if (['Ignition_Maguire_Gmail_MCP_List', 'Ignition_Drive_MCP_List', 'Webflow_Tools', 'Uru_Tools'].includes(toolName)) {
            return {};
        }
        
        // Default: return parameters as-is
        return parameters || {};
    }

    async _waitForSSEReady() {
        const maxWait = 15000; // 15 seconds
        const startTime = Date.now();
        
        while (!this.persistentSSE.messagesEndpoint && (Date.now() - startTime) < maxWait) {
            console.log(`[SSE] ⏳ Waiting for messages endpoint...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        if (!this.persistentSSE.messagesEndpoint) {
            throw new Error('SSE session not ready - no messages endpoint');
        }
        
        console.log(`[SSE] ✅ Using endpoint: ${this.persistentSSE.messagesEndpoint}`);
    }

    async _sendToPersistentSession(mcpRequest) {
        return new Promise(async (resolve, reject) => {
            const requestId = mcpRequest.id;
            
            // Store request for response matching
            this.persistentSSE.pendingRequests.set(requestId, { resolve, reject });
            
            try {
                // Send to n8n messages endpoint
                const messagesUrl = `https://n8n-uru-u46170.vm.elestio.app${this.persistentSSE.messagesEndpoint}`;
                
                console.log(`[N8N] 📤 Sending to: ${messagesUrl}`);
                console.log(`[N8N] 📦 Payload:`, JSON.stringify(mcpRequest, null, 2));
                
                const response = await fetch(messagesUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/plain, */*',
                        'User-Agent': 'Uru-MCP-Proxy/2.0'
                    },
                    body: JSON.stringify(mcpRequest)
                });
                
                console.log(`[N8N] 📬 Response: ${response.status} ${response.statusText}`);
                
                if (response.status === 202) {
                    console.log(`[N8N] ✅ Request accepted - waiting for async response`);
                } else {
                    const responseText = await response.text();
                    console.log(`[N8N] 📄 Response text:`, responseText);
                }
                
                // Set timeout for this request
                setTimeout(() => {
                    if (this.persistentSSE.pendingRequests.has(requestId)) {
                        this.persistentSSE.pendingRequests.delete(requestId);
                        reject(new Error(`Request timeout for ${mcpRequest.params.name}`));
                    }
                }, 45000);
                
            } catch (error) {
                this.persistentSSE.pendingRequests.delete(requestId);
                reject(error);
            }
        });
    }

        // Add this to your smart_proxy_js.js - Replace the _executePersonalMCPTool method

    async _executePersonalMCPTool(toolName, parameters, employeeToken) {
        try {
            console.log(`[PERSONAL-MCP] 🔧 Executing ${toolName} for employee`);
            
            // Get OAuth tokens from your OAuth service
            const oauthTokens = await this._getEmployeeOAuthTokens(employeeToken, 'google');
            
            if (!oauthTokens || !oauthTokens.access_token) {
                console.error(`[PERSONAL-MCP] ❌ OAuth token validation failed for ${toolName}`);
                console.error(`[PERSONAL-MCP] ❌ Token details:`, {
                    has_tokens: !!oauthTokens,
                    has_access_token: !!(oauthTokens && oauthTokens.access_token),
                    has_refresh_token: !!(oauthTokens && oauthTokens.refresh_token),
                    expires_at: oauthTokens && oauthTokens.expires_at,
                    oauth_service_url: this.oauthServiceUrl
                });
                return {
                    error: 'No Google OAuth connection found. Please connect your Google account first.',
                    requires_oauth: true,
                    oauth_url: `${this.oauthServiceUrl}/oauth/google/initiate`,
                    debug_info: {
                        employee_token_provided: employeeToken !== 'anonymous',
                        oauth_service_url: this.oauthServiceUrl,
                        token_fetch_attempted: true
                    }
                };
            }
            
            // Determine which MCP server to use
            const serverType = this._getServerTypeForTool(toolName);
            if (!serverType) {
                return { error: `Unknown personal tool: ${toolName}` };
            }
            
            // Execute the tool with OAuth injection
            return await this._executeMCPToolWithOAuth(serverType, toolName, parameters, oauthTokens);
            
        } catch (error) {
            console.error(`[PERSONAL-MCP] ❌ Error executing ${toolName}:`, error);
            return { error: `Personal MCP tool execution failed: ${error.message}` };
        }
    }

        // Replace your _getEmployeeOAuthTokens method with this auto-refreshing version

    async _getEmployeeOAuthTokens(employeeToken, provider) {
        try {
            if (employeeToken === 'anonymous') {
                console.log(`[OAUTH] ⚠️ Anonymous token - no OAuth available`);
                return null;
            }

            // Use appropriate service based on architecture
            let url;
            if (this.useNewArchitecture) {
                // New architecture: get tokens from integration-service
                url = `${this.integrationsServiceUrl}/employee/oauth-tokens/${provider}`;
                console.log(`[OAUTH] 🔗 Requesting tokens from integration-service: ${url}`);
            } else {
                // Old architecture: get tokens from oauth-service
                url = `${this.oauthServiceUrl}/employee/oauth-tokens/${provider}`;
                console.log(`[OAUTH] 🔗 Requesting tokens from oauth-service: ${url}`);
            }

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${employeeToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000, // Increased timeout for production
                validateStatus: function (status) {
                    return status < 500; // Don't throw for 4xx errors, handle them gracefully
                }
            });

            if (response.status === 404) {
                console.log(`[OAUTH] ⚠️ No ${provider} tokens found for employee`);
                return null;
            }

            if (response.status === 401) {
                console.error(`[OAUTH] ❌ Authentication failed - invalid employee token`);
                return null;
            }

            if (response.status !== 200) {
                console.error(`[OAUTH] ❌ Failed to retrieve tokens - status: ${response.status}`);
                console.error(`[OAUTH] ❌ Response:`, response.data);
                return null;
            }

            const tokens = response.data;

            // Validate token structure
            if (!tokens.access_token) {
                console.error(`[OAUTH] ❌ Invalid token response - missing access_token`);
                return null;
            }
            
            // Check if access token is expired or expires soon (within 5 minutes)
            if (tokens.expires_at) {
                const expiryTime = new Date(tokens.expires_at);
                const now = new Date();
                const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
                
                if (expiryTime <= fiveMinutesFromNow) {
                    console.log(`[OAUTH] ⏰ Token expires soon or expired (${tokens.expires_at}), refreshing...`);
                    
                    // Auto-refresh the token
                    const refreshedTokens = await this._refreshEmployeeOAuthTokens(employeeToken, provider);
                    if (refreshedTokens) {
                        console.log(`[OAUTH] ✅ Successfully refreshed ${provider} tokens`);
                        return refreshedTokens;
                    } else {
                        console.warn(`[OAUTH] ⚠️ Failed to refresh tokens, using existing ones`);
                        return tokens; // Fall back to existing tokens
                    }
                }
            }
            
            console.log(`[OAUTH] ✅ Got valid tokens:`, {
                has_access_token: !!tokens.access_token,
                has_refresh_token: !!tokens.refresh_token,
                expires_at: tokens.expires_at
            });
            
            return tokens;
            
        } catch (error) {
            console.error(`[OAUTH] ❌ Failed to get ${provider} tokens:`, error.message);
            if (error.response) {
                console.error(`[OAUTH] ❌ Response status: ${error.response.status}`);
                console.error(`[OAUTH] ❌ Response data:`, error.response.data);
            }
            if (error.code === 'ECONNREFUSED') {
                console.error(`[OAUTH] ❌ Connection refused - OAuth service may be down or unreachable`);
                console.error(`[OAUTH] ❌ OAuth service URL: ${this.oauthServiceUrl}`);
            }
            if (error.code === 'ENOTFOUND') {
                console.error(`[OAUTH] ❌ DNS resolution failed - check OAuth service URL: ${this.oauthServiceUrl}`);
            }
            return null;
        }
    }

    // Add this new method for auto-refreshing tokens
    async _refreshEmployeeOAuthTokens(employeeToken, provider) {
        try {
            const refreshUrl = `${this.oauthServiceUrl}/oauth/${provider}/refresh`;
            console.log(`[OAUTH] 🔄 Refreshing tokens at: ${refreshUrl}`);
            
            const response = await axios.post(refreshUrl, {}, {
                headers: { Authorization: `Bearer ${employeeToken}` },
                timeout: 10000
            });
            
            if (response.data.message && response.data.message.includes('success')) {
                console.log(`[OAUTH] ✅ Token refresh successful`);
                
                // Get the fresh tokens
                const tokensResponse = await axios.get(`${this.oauthServiceUrl}/oauth/${provider}/tokens`, {
                    headers: { Authorization: `Bearer ${employeeToken}` },
                    timeout: 5000
                });
                
                return tokensResponse.data;
            } else {
                console.warn(`[OAUTH] ⚠️ Unexpected refresh response:`, response.data);
                return null;
            }
            
        } catch (error) {
            console.error(`[OAUTH] ❌ Token refresh failed:`, {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText
            });
            return null;
        }
    }

    _getServerTypeForTool(toolName) {
        // Map tools to their MCP servers
        const toolMapping = {
            // Google Drive tools (isaacphi/mcp-gdrive)
            'gdrive_search': 'gdrive',
            'gdrive_read_file': 'gdrive', 
            'gsheets_read': 'gdrive',
            'gsheets_update_cell': 'gdrive',
            
            // Gmail tools (GongRzhe/server-gmail-autoauth-mcp)
            'send_email': 'gmail',
            'search_emails': 'gmail',
            'get_email': 'gmail',
            'create_draft': 'gmail',
            'list_emails': 'gmail',
            
            // Calendar tools (future)
            'list_events': 'calendar',
            'create_event': 'calendar'
        };
        
        return toolMapping[toolName];
    }

    async _executeMCPToolWithOAuth(serverType, toolName, parameters, oauthTokens) {
        try {
            console.log(`[MCP-OAUTH] 🚀 Spawning ${serverType} server for ${toolName}`);
            
            if (serverType === 'gdrive') {
                return await this._executeGoogleDriveTool(toolName, parameters, oauthTokens);
            } else if (serverType === 'gmail') {
                return await this._executeGmailTool(toolName, parameters, oauthTokens);
            }
            
            // Future: Add calendar implementations
            return { error: `MCP server type ${serverType} not implemented yet` };
            
        } catch (error) {
            console.error(`[MCP-OAUTH] ❌ Error with ${serverType}:`, error);
            return { error: `MCP server execution failed: ${error.message}` };
        }
    }

    async _executeGmailTool(toolName, parameters, oauthTokens) {
        return new Promise((resolve, reject) => {
            try {
                // Create temporary credentials directory
                const tempDir = require('os').tmpdir();
                const credentialsDir = path.join(tempDir, `mcp-gmail-${Date.now()}`);
                require('fs').mkdirSync(credentialsDir, { recursive: true });
                
                // Validate Google OAuth credentials
                if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
                    console.error(`[GMAIL-MCP] ❌ Missing Google OAuth credentials`);
                    console.error(`[GMAIL-MCP] ❌ GOOGLE_CLIENT_ID: ${process.env.GOOGLE_CLIENT_ID ? 'SET' : 'MISSING'}`);
                    console.error(`[GMAIL-MCP] ❌ GOOGLE_CLIENT_SECRET: ${process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'MISSING'}`);
                    resolve({
                        error: 'Missing Google OAuth credentials in environment variables'
                    });
                    return;
                }

                // Create Gmail MCP credentials file
                const credentialsFile = path.join(credentialsDir, 'credentials.json');
                const credentials = {
                    installed: {
                        client_id: process.env.GOOGLE_CLIENT_ID,
                        client_secret: process.env.GOOGLE_CLIENT_SECRET,
                        auth_uri: "https://accounts.google.com/o/oauth2/auth",
                        token_uri: "https://oauth2.googleapis.com/token",
                        redirect_uris: ["http://localhost"]
                    }
                };
                
                // Create token file for Gmail MCP
                const tokenFile = path.join(credentialsDir, 'token.json');
                const tokenData = {
                    type: "authorized_user",
                    client_id: process.env.GOOGLE_CLIENT_ID,
                    client_secret: process.env.GOOGLE_CLIENT_SECRET,
                    refresh_token: oauthTokens.refresh_token,
                    access_token: oauthTokens.access_token
                };
                
                require('fs').writeFileSync(credentialsFile, JSON.stringify(credentials, null, 2));
                require('fs').writeFileSync(tokenFile, JSON.stringify(tokenData, null, 2));
                
                console.log(`[GMAIL-MCP] 📁 Created credentials in: ${credentialsDir}`);
                
                // Windows-compatible spawn for Gmail MCP server
                const isWindows = process.platform === 'win32';
                
                const mcpProcess = spawn(
                    isWindows ? 'cmd' : 'npx',
                    isWindows ? ['/c', 'npx', '@gongrzhe/server-gmail-autoauth-mcp'] : ['@gongrzhe/server-gmail-autoauth-mcp'],
                    {
                        stdio: ['pipe', 'pipe', 'pipe'],
                        env: {
                            ...process.env,
                            GMAIL_CREDENTIALS_FILE: credentialsFile,
                            GMAIL_TOKEN_FILE: tokenFile,
                            GMAIL_SCOPES: 'https://www.googleapis.com/auth/gmail.readonly,https://www.googleapis.com/auth/gmail.send'
                        }
                    }
                );
                
                let responseData = '';
                let errorData = '';
                
                // Send MCP request
                const mcpRequest = {
                    jsonrpc: '2.0',
                    id: Date.now(),
                    method: 'tools/call',
                    params: {
                        name: toolName,
                        arguments: parameters
                    }
                };
                
                console.log(`[GMAIL-MCP] 📤 Sending request:`, JSON.stringify(mcpRequest));
                
                mcpProcess.stdin.write(JSON.stringify(mcpRequest) + '\n');
                mcpProcess.stdin.end();
                
                mcpProcess.stdout.on('data', (data) => {
                    const output = data.toString();
                    console.log(`[GMAIL-MCP] 📥 stdout:`, output.substring(0, 200) + '...');
                    responseData += output;
                });
                
                mcpProcess.stderr.on('data', (data) => {
                    const error = data.toString();
                    console.log(`[GMAIL-MCP] 📥 stderr:`, error.substring(0, 200) + '...');
                    errorData += error;
                });
                
                mcpProcess.on('close', (code) => {
                    console.log(`[GMAIL-MCP] 🏁 Process closed with code: ${code}`);
                    
                    // Cleanup temporary files
                    try {
                        require('fs').rmSync(credentialsDir, { recursive: true, force: true });
                        console.log(`[GMAIL-MCP] 🧹 Cleaned up temp directory`);
                    } catch (e) {
                        console.warn(`[GMAIL-MCP] ⚠️ Cleanup warning:`, e.message);
                    }
                    
                    if (code === 0 && responseData) {
                        try {
                            // Parse Gmail MCP response
                            const lines = responseData.trim().split('\n');
                            let result = null;
                            
                            for (const line of lines) {
                                if (line.trim()) {
                                    try {
                                        const parsed = JSON.parse(line.trim());
                                        if (parsed.result) {
                                            result = parsed.result;
                                            break;
                                        }
                                    } catch (e) {
                                        console.log(`[GMAIL-MCP] 📄 Non-JSON line:`, line.substring(0, 100));
                                    }
                                }
                            }
                            
                            if (result) {
                                console.log(`[GMAIL-MCP] ✅ Success for ${toolName}`);
                                resolve({
                                    success: true,
                                    result: result,
                                    tool_name: toolName,
                                    source: 'gongrzhe_gmail_mcp'
                                });
                            } else {
                                console.log(`[GMAIL-MCP] ⚠️ No result found, returning raw response`);
                                resolve({
                                    success: false,
                                    result: responseData.substring(0, 1000),
                                    tool_name: toolName,
                                    message: 'Gmail MCP server responded but no structured result found'
                                });
                            }
                            
                        } catch (error) {
                            console.error(`[GMAIL-MCP] ❌ Parse error:`, error);
                            resolve({
                                error: `Failed to parse Gmail MCP response: ${error.message}`,
                                raw_response: responseData.substring(0, 1000)
                            });
                        }
                    } else {
                        console.error(`[GMAIL-MCP] ❌ Process failed with code ${code}`);
                        console.error(`[GMAIL-MCP] ❌ Error output:`, errorData.substring(0, 500));
                        console.error(`[GMAIL-MCP] ❌ Standard output:`, responseData.substring(0, 500));
                        console.error(`[GMAIL-MCP] ❌ Environment check:`, {
                            google_client_id: process.env.GOOGLE_CLIENT_ID ? 'SET' : 'MISSING',
                            google_client_secret: process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'MISSING',
                            credentials_file: credentialsFile,
                            token_file: tokenFile
                        });
                        resolve({
                            error: `Gmail MCP server failed with code ${code}`,
                            stderr: errorData.substring(0, 500),
                            stdout: responseData.substring(0, 500),
                            debug_info: {
                                exit_code: code,
                                tool_name: toolName,
                                has_credentials: !!process.env.GOOGLE_CLIENT_ID,
                                platform: process.platform
                            }
                        });
                    }
                });
                
                mcpProcess.on('error', (error) => {
                    console.error(`[GMAIL-MCP] ❌ Process error:`, error);
                    
                    // Cleanup on error
                    try {
                        require('fs').rmSync(credentialsDir, { recursive: true, force: true });
                    } catch (e) {
                        // Ignore cleanup errors
                    }
                    
                    resolve({
                        error: `Failed to spawn Gmail MCP server: ${error.message}`,
                        details: `Platform: ${process.platform}`
                    });
                });
                
                // Timeout after 30 seconds
                setTimeout(() => {
                    if (!mcpProcess.killed) {
                        console.log(`[GMAIL-MCP] ⏰ Timeout - killing process`);
                        mcpProcess.kill('SIGTERM');
                        
                        try {
                            require('fs').rmSync(credentialsDir, { recursive: true, force: true });
                        } catch (e) {
                            // Ignore cleanup errors
                        }
                        
                        resolve({
                            error: 'Gmail MCP server timeout after 30 seconds'
                        });
                    }
                }, 30000);
                
            } catch (error) {
                console.error(`[GMAIL-MCP] ❌ Setup error:`, error);
                resolve({
                    error: `Failed to setup Gmail MCP: ${error.message}`
                });
            }
        });
    }

    async _executeGoogleDriveTool(toolName, parameters, oauthTokens) {
        return new Promise((resolve, reject) => {
            try {
                // Validate Google OAuth credentials
                if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
                    console.error(`[GDRIVE-MCP] ❌ Missing Google OAuth credentials`);
                    console.error(`[GDRIVE-MCP] ❌ GOOGLE_CLIENT_ID: ${process.env.GOOGLE_CLIENT_ID ? 'SET' : 'MISSING'}`);
                    console.error(`[GDRIVE-MCP] ❌ GOOGLE_CLIENT_SECRET: ${process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'MISSING'}`);
                    resolve({
                        error: 'Missing Google OAuth credentials in environment variables'
                    });
                    return;
                }

                // Create temporary credentials directory
                const tempDir = require('os').tmpdir();
                const credentialsDir = path.join(tempDir, `mcp-gdrive-${Date.now()}`);
                require('fs').mkdirSync(credentialsDir, { recursive: true });
                
                // Create Google Cloud credentials file (what the MCP server expects)
                const gcpCredentialsFile = path.join(credentialsDir, 'gcp-oauth.keys.json');
                const gcpCredentials = {
                    installed: {
                        client_id: process.env.GOOGLE_CLIENT_ID,
                        client_secret: process.env.GOOGLE_CLIENT_SECRET,
                        auth_uri: "https://accounts.google.com/o/oauth2/auth",
                        token_uri: "https://oauth2.googleapis.com/token",
                        redirect_uris: ["http://localhost"]
                    }
                };
                
                // Create saved credentials file (what the server uses for tokens)
                const savedCredentialsFile = path.join(credentialsDir, '.gdrive-server-credentials.json');
                
                // Parse and format the expiry date properly
                let expiryDate = null;
                if (oauthTokens.expires_at) {
                    try {
                        expiryDate = new Date(oauthTokens.expires_at).toISOString();
                    } catch (e) {
                        console.warn(`[GDRIVE-MCP] ⚠️ Invalid expiry date format: ${oauthTokens.expires_at}`);
                        // Set expiry to 1 hour from now as fallback
                        expiryDate = new Date(Date.now() + 3600000).toISOString();
                    }
                }
                
                const savedCredentials = {
                    type: "authorized_user",
                    client_id: process.env.GOOGLE_CLIENT_ID,
                    client_secret: process.env.GOOGLE_CLIENT_SECRET,
                    refresh_token: oauthTokens.refresh_token,
                    // Try both formats to see which works
                    ...(expiryDate && { expiry_date: expiryDate }),
                    ...(expiryDate && { expires_at: expiryDate })
                };
                
                require('fs').writeFileSync(gcpCredentialsFile, JSON.stringify(gcpCredentials, null, 2));
                require('fs').writeFileSync(savedCredentialsFile, JSON.stringify(savedCredentials, null, 2));
                
                console.log(`[GDRIVE-MCP] 📁 Created credentials in: ${credentialsDir}`);
                console.log(`[GDRIVE-MCP] 📄 Files: gcp-oauth.keys.json, .gdrive-server-credentials.json`);
                console.log(`[GDRIVE-MCP] ⏰ Token expires: ${expiryDate}`);
                
                // Windows-compatible spawn
                const isWindows = process.platform === 'win32';
                
                const mcpProcess = spawn(
                    isWindows ? 'cmd' : 'npx',
                    isWindows ? ['/c', 'npx', '-y', '@isaacphi/mcp-gdrive'] : ['-y', '@isaacphi/mcp-gdrive'],
                    {
                        stdio: ['pipe', 'pipe', 'pipe'],
                        env: {
                            ...process.env,
                            CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
                            CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
                            GDRIVE_CREDS_DIR: credentialsDir,
                            PATH: process.env.PATH
                        }
                    }
                );
                
                let responseData = '';
                let errorData = '';
                
                // Send MCP request
                const mcpRequest = {
                    jsonrpc: '2.0',
                    id: Date.now(),
                    method: 'tools/call',
                    params: {
                        name: toolName,
                        arguments: parameters
                    }
                };
                
                console.log(`[GDRIVE-MCP] 📤 Sending request:`, JSON.stringify(mcpRequest));
                
                mcpProcess.stdin.write(JSON.stringify(mcpRequest) + '\n');
                mcpProcess.stdin.end();
                
                mcpProcess.stdout.on('data', (data) => {
                    const output = data.toString();
                    console.log(`[GDRIVE-MCP] 📥 stdout:`, output);
                    responseData += output;
                });
                
                mcpProcess.stderr.on('data', (data) => {
                    const error = data.toString();
                    console.log(`[GDRIVE-MCP] 📥 stderr:`, error);
                    errorData += error;
                    
                    if (error.includes('Setting up automatic token refresh')) {
                        console.log(`[GDRIVE-MCP] ✅ Token refresh configured`);
                    }
                });
                
                mcpProcess.on('close', (code) => {
                    console.log(`[GDRIVE-MCP] 🏁 Process closed with code: ${code}`);
                    
                    // Cleanup temporary files
                    try {
                        require('fs').rmSync(credentialsDir, { recursive: true, force: true });
                        console.log(`[GDRIVE-MCP] 🧹 Cleaned up temp directory`);
                    } catch (e) {
                        console.warn(`[GDRIVE-MCP] ⚠️ Cleanup warning:`, e.message);
                    }
                    
                    if (code === 0 && responseData) {
                        try {
                            console.log(`[GDRIVE-MCP] 📥 Raw response:`, responseData.substring(0, 500) + '...');
                            
                            // Parse MCP response - handle multiple JSON objects
                            const lines = responseData.trim().split('\n');
                            let result = null;
                            
                            for (const line of lines) {
                                if (line.trim()) {
                                    try {
                                        const parsed = JSON.parse(line.trim());
                                        if (parsed.result) {
                                            result = parsed.result;
                                            break;
                                        }
                                    } catch (e) {
                                        // Skip non-JSON lines
                                        console.log(`[GDRIVE-MCP] 📄 Non-JSON line:`, line.substring(0, 100));
                                    }
                                }
                            }
                            
                            if (result) {
                                console.log(`[GDRIVE-MCP] ✅ Success for ${toolName}`);
                                resolve({
                                    success: true,
                                    result: result,
                                    tool_name: toolName,
                                    source: 'isaacphi_mcp_gdrive'
                                });
                            } else {
                                console.log(`[GDRIVE-MCP] ⚠️ No result found, returning raw response`);
                                resolve({
                                    success: false,
                                    result: responseData,
                                    tool_name: toolName,
                                    message: 'MCP server responded but no structured result found'
                                });
                            }
                            
                        } catch (error) {
                            console.error(`[GDRIVE-MCP] ❌ Parse error:`, error);
                            resolve({
                                error: `Failed to parse MCP response: ${error.message}`,
                                raw_response: responseData.substring(0, 1000)
                            });
                        }
                    } else {
                        console.error(`[GDRIVE-MCP] ❌ Process failed with code ${code}`);
                        console.error(`[GDRIVE-MCP] ❌ Error output:`, errorData.substring(0, 500));
                        console.error(`[GDRIVE-MCP] ❌ Standard output:`, responseData.substring(0, 500));
                        console.error(`[GDRIVE-MCP] ❌ Environment check:`, {
                            google_client_id: process.env.GOOGLE_CLIENT_ID ? 'SET' : 'MISSING',
                            google_client_secret: process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'MISSING',
                            credentials_dir: credentialsDir,
                            has_oauth_tokens: !!oauthTokens,
                            has_access_token: !!(oauthTokens && oauthTokens.access_token),
                            has_refresh_token: !!(oauthTokens && oauthTokens.refresh_token)
                        });

                        resolve({
                            error: `Google Drive MCP server failed with code ${code}`,
                            stderr: errorData.substring(0, 500),
                            stdout: responseData.substring(0, 500),
                            debug_info: {
                                exit_code: code,
                                tool_name: toolName,
                                has_credentials: !!process.env.GOOGLE_CLIENT_ID,
                                platform: process.platform,
                                oauth_token_status: {
                                    has_access_token: !!(oauthTokens && oauthTokens.access_token),
                                    has_refresh_token: !!(oauthTokens && oauthTokens.refresh_token),
                                    expires_at: oauthTokens && oauthTokens.expires_at
                                }
                            }
                        });
                    }
                });
                
                mcpProcess.on('error', (error) => {
                    console.error(`[GDRIVE-MCP] ❌ Process error:`, error);
                    
                    // Cleanup on error
                    try {
                        require('fs').rmSync(credentialsDir, { recursive: true, force: true });
                    } catch (e) {
                        // Ignore cleanup errors
                    }
                    
                    resolve({
                        error: `Failed to spawn MCP server: ${error.message}`,
                        details: `Platform: ${process.platform}, Command: ${isWindows ? 'cmd /c npx' : 'npx'}`
                    });
                });
                
                // Timeout after 30 seconds
                setTimeout(() => {
                    if (!mcpProcess.killed) {
                        console.log(`[GDRIVE-MCP] ⏰ Timeout - killing process`);
                        mcpProcess.kill('SIGTERM');
                        
                        // Cleanup on timeout
                        try {
                            require('fs').rmSync(credentialsDir, { recursive: true, force: true });
                        } catch (e) {
                            // Ignore cleanup errors
                        }
                        
                        resolve({
                            error: 'MCP server timeout after 30 seconds'
                        });
                    }
                }, 30000);
                
            } catch (error) {
                console.error(`[GDRIVE-MCP] ❌ Setup error:`, error);
                resolve({
                    error: `Failed to setup Google Drive MCP: ${error.message}`
                });
            }
        });
    }

    // === OAUTH AND UTILITY METHODS ===

    async _getOAuthConnections(employeeToken) {
        try {
            if (employeeToken === 'anonymous') {
                return {};
            }
            
            const url = `${this.oauthServiceUrl}/oauth/google/status`;
            console.log(`[OAUTH] 🔗 Requesting OAuth status from: ${url}`);
            
            const response = await axios.get(url, {
                headers: { Authorization: `Bearer ${employeeToken}` },
                timeout: 5000
            });
            
            console.log(`[OAUTH] ✅ OAuth status response:`, response.data);
            return { google: response.data };
            
        } catch (error) {
            console.warn(`[OAUTH] ⚠️ Failed to get OAuth status:`, error.message);
            return {};
        }
    }

    async getOAuthConnections(employeeToken) {
        return await this._getOAuthConnections(employeeToken);
    }

    async _testN8NConnection() {
        try {
            // Test basic connectivity to N8N endpoint with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(this.n8nSseUrl, {
                method: 'HEAD',
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            return {
                success: response.ok,
                status: response.ok ? 'n8n_accessible' : 'n8n_unreachable',
                http_status: response.status,
                persistent_sse: this.getSSEConnectionStatus()
            };
        } catch (error) {
            return {
                success: false,
                status: 'n8n_connection_error',
                error: error.message,
                persistent_sse: this.getSSEConnectionStatus()
            };
        }
    }

    _formatToolName(toolName) {
        return toolName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    // Cleanup method for graceful shutdown
    cleanup() {
        console.log(`[SSE-PERSISTENT] 🧹 Cleaning up SSE connections...`);

        // Stop heartbeat monitoring
        this._stopHeartbeat();

        // Close SSE connection if active
        if (this.persistentSSE.connection) {
            try {
                this.persistentSSE.connection.body.destroy();
            } catch (error) {
                console.warn(`[SSE-PERSISTENT] ⚠️ Error closing connection:`, error.message);
            }
        }

        // Clear connection state
        this.persistentSSE.connected = false;
        this.persistentSSE.connecting = false;
        this.persistentSSE.connection = null;

        // Reject any pending requests
        for (const [requestId, { reject }] of this.persistentSSE.pendingRequests) {
            reject(new Error('Service shutting down'));
        }
        this.persistentSSE.pendingRequests.clear();

        console.log(`[SSE-PERSISTENT] ✅ Cleanup completed`);
    }

    _getToolSchema(toolName, serverType) {
        // Basic schemas for personal tools
        const schemas = {
            gdrive: {
                gdrive_search: {
                    type: 'object',
                    properties: {
                        query: { type: 'string', description: 'Search query for Drive files' }
                    },
                    required: ['query']
                },
                gdrive_read_file: {
                    type: 'object',
                    properties: {
                        fileId: { type: 'string', description: 'Google Drive file ID' }
                    },
                    required: ['fileId']
                }
            }
        };

        return schemas[serverType]?.[toolName] || {
            type: 'object',
            properties: {},
            required: []
        };
    }
}

module.exports = SmartMCPProxy;