#!/usr/bin/env node

/**
 * Load Local Environment Variables
 * Loads environment variables from .env.local for development
 */

const fs = require('fs');
const path = require('path');

function loadLocalEnv() {
    const envPath = path.join(process.cwd(), '.env.local');
    
    if (!fs.existsSync(envPath)) {
        console.error('❌ .env.local file not found');
        process.exit(1);
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    let loaded = 0;
    
    for (const line of lines) {
        const trimmed = line.trim();
        
        // Skip comments and empty lines
        if (!trimmed || trimmed.startsWith('#')) {
            continue;
        }
        
        // Parse key=value
        const equalIndex = trimmed.indexOf('=');
        if (equalIndex === -1) {
            continue;
        }
        
        const key = trimmed.substring(0, equalIndex).trim();
        const value = trimmed.substring(equalIndex + 1).trim();
        
        // Only set if not already set
        if (!process.env[key]) {
            process.env[key] = value;
            loaded++;
        }
    }
    
    console.log(`✅ Loaded ${loaded} environment variables from .env.local`);
    
    // Verify critical variables
    const critical = ['N8N_SSE_URL', 'AUTH_SERVICE_URL', 'INTEGRATIONS_SERVICE_URL'];
    const missing = critical.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
        console.error(`❌ Missing critical environment variables: ${missing.join(', ')}`);
        process.exit(1);
    }
    
    console.log(`✅ All critical environment variables are set`);
}

// Load environment if called directly
if (require.main === module) {
    loadLocalEnv();
} else {
    // Auto-load when required
    loadLocalEnv();
}

module.exports = loadLocalEnv;
