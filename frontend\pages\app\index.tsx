import { GetServerSideProps } from 'next';
import { Sidebar } from '../../components/shared/Sidebar';
import { DashboardOverview } from '../../components/dashboard/DashboardOverview';
import { ProtectedRoute } from '../../components/auth/ProtectedRoute';
import { useAuth } from '../../components/auth/AuthContext';

export default function AppDashboard() {
  const { employee } = useAuth();



  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-900 text-white">
        <Sidebar />
        <main className="flex-1 flex flex-col overflow-hidden">
          {/* Top bar */}
          <div className="bg-gray-900 border-b border-gray-800 flex-shrink-0">
            <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-semibold text-white">{employee?.workspace?.name || 'Workspace'}</h1>
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-green-400 text-sm">Live</span>
              </div>

              <div className="flex items-center space-x-4">
                <input
                  type="text"
                  placeholder="Ask about trends, patterns, client insights, or any company data..."
                  className="bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 w-96 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {employee?.name?.split(' ').map(n => n[0]).join('') || 'U'}
                </div>
                <span className="text-white text-sm">{employee?.name || 'User'}</span>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto">
            <DashboardOverview />
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}

// Prevent static generation for protected routes
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};


