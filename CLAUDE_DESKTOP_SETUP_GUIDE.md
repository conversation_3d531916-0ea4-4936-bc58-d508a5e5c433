# Claude Desktop MCP Setup Guide

## Overview

This guide will help you set up <PERSON> to work with your Uru Workspace Platform tools through the Model Context Protocol (MCP).

## Prerequisites

- <PERSON> installed on your computer
- Active Uru Workspace Platform account
- Network access to your Uru platform instance

## Step-by-Step Setup

### 1. Generate Your Configuration

1. Log into your Uru Workspace Platform
2. Navigate to the Dashboard
3. Click on the **"Claude Desktop"** tab
4. Click **"Generate Configuration"** button
5. Wait for the configuration to be generated successfully

### 2. Download Required Files

You need two files for the setup:

1. **Configuration File**: Click "Download Config" to save `claude_desktop_config.json`
2. **MCP Server File**: Click "Download Server" to save `uru-claude-desktop-server.js`

### 3. Locate Claude Desktop Configuration Directory

The location depends on your operating system:

- **Windows**: `%APPDATA%\Claude\`
- **macOS**: `~/Library/Application Support/Claude/`
- **Linux**: `~/.config/claude/`

### 4. Install the Files

1. Place both downloaded files in your Claude Desktop configuration directory
2. Ensure both files are in the same directory
3. The files should be:
   - `claude_desktop_config.json`
   - `uru-claude-desktop-server.js`

### 5. Restart Claude Desktop

1. Completely close Claude Desktop
2. Restart the application
3. Claude Desktop will automatically load the new MCP configuration

### 6. Verify Setup

1. Open Claude Desktop
2. Look for Uru workspace tools in the available tools
3. Try using a tool to verify connectivity

## Troubleshooting

### Common Issues

#### "Failed to connect to MCP proxy"
- **Cause**: Network connectivity issues or incorrect proxy URL
- **Solution**: Check your internet connection and ensure the proxy URL is correct

#### "Authentication failed"
- **Cause**: Expired or invalid employee token
- **Solution**: Regenerate your configuration from the Uru Workspace Platform dashboard

#### "Tool execution failed"
- **Cause**: Various issues with tool execution
- **Solution**: Check the MCP proxy logs and ensure your OAuth connections are active

#### "MCP server file not found"
- **Cause**: Files not placed in correct directory or incorrect file names
- **Solution**: Ensure both files are in the Claude Desktop configuration directory with correct names

### Debug Mode

To enable debug mode for more detailed logging:

1. Edit your `claude_desktop_config.json` file
2. Change `"DEBUG_MODE": "false"` to `"DEBUG_MODE": "true"`
3. Restart Claude Desktop
4. Check the console output for detailed logs

### Configuration Validation

Use the "Test Configuration" button in the Uru Workspace Platform dashboard to validate your setup before using it with Claude Desktop.

## Environment Variables

The MCP server uses these environment variables (automatically set by the configuration):

- `MCP_PROXY_URL`: URL of your Uru MCP proxy
- `EMPLOYEE_TOKEN`: Your authentication token
- `DEBUG_MODE`: Enable/disable debug logging

## Security Notes

- Keep your employee token secure and don't share it
- Tokens expire after 90 days and need to be regenerated
- Each employee needs their own configuration

## Support

If you continue to experience issues:

1. Check the troubleshooting section above
2. Use the configuration validation tool
3. Contact your Uru Workspace Platform administrator
4. Check the MCP proxy service status

## Advanced Configuration

### Custom Proxy URLs

For development or custom deployments, you may need to modify the `MCP_PROXY_URL` in your configuration file.

### Multiple Workspaces

Each workspace requires its own configuration. Generate separate configurations for each workspace you need to access.
