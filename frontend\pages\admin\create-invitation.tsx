import React, { useState, useEffect } from 'react';
import { GetServerSideProps } from 'next';
import { UruLogo } from '../../components/shared/UruLogo';
import { UserPlus, AlertCircle, CheckCircle, Copy, RefreshCw } from 'lucide-react';
import { apiService } from '../../utils/api';
import { handleError, getUserFriendlyMessage } from '../../utils/errorHandler';

interface Workspace {
  id: string;
  name: string;
  slug: string;
}

export default function CreateInvitationPage() {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [workspaceSlug, setWorkspaceSlug] = useState('');
  const [role, setRole] = useState('member');
  const [isLoading, setIsLoading] = useState(false);
  const [invitation, setInvitation] = useState<any>(null);
  const [error, setError] = useState('');
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [loadingWorkspaces, setLoadingWorkspaces] = useState(true);

  // Load workspaces on component mount
  useEffect(() => {
    loadWorkspaces();
  }, []);

  const loadWorkspaces = async () => {
    try {
      setLoadingWorkspaces(true);
      const workspaceList = await apiService.getWorkspaces();
      setWorkspaces(workspaceList);

      // Set default workspace if available
      if (workspaceList.length > 0 && !workspaceSlug) {
        setWorkspaceSlug(workspaceList[0].slug);
      }
    } catch (err: any) {
      setError(`Failed to load workspaces: ${err.message}`);
    } finally {
      setLoadingWorkspaces(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setInvitation(null);
    setIsLoading(true);

    try {
      // Validate inputs
      if (!email || !name || !workspaceSlug) {
        throw new Error('Please fill in all required fields');
      }

      // Find the workspace
      const workspace = workspaces.find(w => w.slug === workspaceSlug);
      if (!workspace) {
        throw new Error(`Workspace '${workspaceSlug}' not found. Please refresh workspaces or select a different one.`);
      }

      // Create invitation using API service
      const result = await apiService.createInvitation(workspace.id, email, name, role);
      setInvitation(result);

      // Clear form
      setEmail('');
      setName('');
    } catch (err: any) {
      const errorDetails = handleError(err, 'Create Invitation');
      const userMessage = getUserFriendlyMessage(errorDetails, 'Create invitation failed');

      // Add specific context for invitation creation
      if (errorDetails.type === 'validation' && errorDetails.message.includes('duplicate')) {
        setError(`An employee with email '${email}' already exists in this workspace. Please use a different email or check existing employees.`);
      } else {
        setError(userMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const createTestInvitation = async () => {
    setIsLoading(true);
    setError('');
    setInvitation(null);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_OAUTH_URL || 'https://oauth.uruenterprises.com'}/dev/test-invitation`);

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Failed to create test invitation';

        try {
          const errorJson = JSON.parse(errorText);
          errorMessage = errorJson.detail || errorJson.message || errorText;
        } catch {
          errorMessage = errorText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const result = await response.json();
      setInvitation(result);
    } catch (err: any) {
      setError(err.message || 'Failed to create test invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const createDefaultWorkspaces = async () => {
    setLoadingWorkspaces(true);
    setError('');

    try {
      const workspacesToCreate = [
        { name: 'Ignition Consultants', slug: 'ignition-consultants' },
        { name: 'Dev Workspace', slug: 'dev-workspace' }
      ];

      for (const workspace of workspacesToCreate) {
        try {
          await apiService.createWorkspace(workspace.name, workspace.slug);
        } catch (error: any) {
          // Don't throw error if workspace already exists
          if (!error.message.includes('already exists') && !error.message.includes('duplicate')) {
            console.warn(`Failed to create workspace ${workspace.name}:`, error.message);
          }
        }
      }

      // Reload workspaces
      await loadWorkspaces();
    } catch (err: any) {
      setError(`Failed to create default workspaces: ${err.message}`);
    } finally {
      setLoadingWorkspaces(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-6">
            <UruLogo size="lg" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Create Employee Invitation
          </h1>
          <p className="text-gray-400">
            Generate invitation links for new employees (Development Tool)
          </p>
        </div>

        {/* Workspace Setup */}
        {!loadingWorkspaces && workspaces.length === 0 && (
          <div className="bg-yellow-600/10 border border-yellow-600/30 rounded-lg p-4 mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-yellow-400 font-medium">Setup Required</h3>
                <p className="text-gray-400 text-sm">No workspaces found. Create default workspaces to get started.</p>
              </div>
              <button
                onClick={createDefaultWorkspaces}
                disabled={loadingWorkspaces}
                className="bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {loadingWorkspaces ? 'Creating...' : 'Create Default Workspaces'}
              </button>
            </div>
          </div>
        )}

        {/* Quick Test Button */}
        <div className="bg-blue-600/10 border border-blue-600/30 rounded-lg p-4 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-blue-400 font-medium">Quick Test</h3>
              <p className="text-gray-400 text-sm">Create a test <NAME_EMAIL></p>
            </div>
            <button
              onClick={createTestInvitation}
              disabled={isLoading || loadingWorkspaces}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              {isLoading ? 'Creating...' : 'Create Test Invitation'}
            </button>
          </div>
        </div>

        {/* Manual Form */}
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-semibold text-white mb-4">Manual Invitation</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-600/10 border border-red-600/30 rounded-lg p-4 flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                <span className="text-red-400 text-sm">{error}</span>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Full Name
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="John Doe"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Workspace
                  <button
                    type="button"
                    onClick={loadWorkspaces}
                    disabled={loadingWorkspaces}
                    className="ml-2 text-blue-400 hover:text-blue-300 disabled:text-gray-500"
                    title="Refresh workspaces"
                  >
                    <RefreshCw className={`w-4 h-4 inline ${loadingWorkspaces ? 'animate-spin' : ''}`} />
                  </button>
                </label>
                {loadingWorkspaces ? (
                  <div className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400">
                    Loading workspaces...
                  </div>
                ) : workspaces.length > 0 ? (
                  <select
                    value={workspaceSlug}
                    onChange={(e) => setWorkspaceSlug(e.target.value)}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a workspace...</option>
                    {workspaces.map((workspace) => (
                      <option key={workspace.id} value={workspace.slug}>
                        {workspace.name} ({workspace.slug})
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="w-full bg-red-600/10 border border-red-600/30 rounded-lg px-3 py-2 text-red-400">
                    No workspaces available. Please create a workspace first.
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Role
                </label>
                <select
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="member">Member</option>
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                </select>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading || loadingWorkspaces || workspaces.length === 0}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Creating invitation...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <UserPlus className="w-4 h-4" />
                  <span>Create Invitation</span>
                </div>
              )}
            </button>
          </form>
        </div>

        {/* Invitation Result */}
        {invitation && (
          <div className="bg-green-600/10 border border-green-600/30 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <CheckCircle className="w-6 h-6 text-green-400" />
              <h3 className="text-lg font-semibold text-white">Invitation Created!</h3>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Invitation Link
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={invitation.invite_link}
                    readOnly
                    className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm"
                  />
                  <button
                    onClick={() => copyToClipboard(invitation.invite_link)}
                    className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-2 rounded-lg transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Invite Token
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={invitation.invite_token}
                    readOnly
                    className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm font-mono"
                  />
                  <button
                    onClick={() => copyToClipboard(invitation.invite_token)}
                    className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-2 rounded-lg transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="text-sm text-gray-400">
                <p><strong>Expires:</strong> {new Date(invitation.expires_at).toLocaleString()}</p>
                <p><strong>Message:</strong> {invitation.message}</p>
              </div>

              <div className="pt-3 border-t border-gray-600">
                <a
                  href={invitation.invite_link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <span>Test Invitation Link</span>
                  <span>→</span>
                </a>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="text-center mt-8">
          <a 
            href="/login" 
            className="text-blue-400 hover:text-blue-300 transition-colors"
          >
            ← Back to Login
          </a>
        </div>
      </div>
    </div>
  );
}

// Prevent static generation for admin pages
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
