@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

body {
  font-family: 'Inter', sans-serif;
}

/* Fix text overflow and spacing issues */
h1, h2, h3, h4, h5, h6 {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Ensure proper gradient text rendering */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Fix section headings - prevent text cutoff while maintaining proper spacing */
.section-heading {
  line-height: 1.25 !important;
  padding-top: 0.5rem !important; /* Minimal top padding to prevent cutoff */
  padding-bottom: 0.75rem !important; /* Reduced bottom padding */
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  overflow: visible !important;
}

/* Container spacing - reduced to prevent excessive gaps */
.section-heading-container {
  overflow: visible !important;
}

/* Tech stack grid - Clean 8-column layout with larger cards */
.tech-stack-grid {
  display: grid !important;
  grid-template-columns: repeat(8, 1fr) !important;
  place-items: stretch !important;
  justify-items: stretch !important;
  align-items: stretch !important;
  justify-content: center !important;
  grid-auto-rows: 1fr !important;
  width: 100% !important;
  max-width: 100% !important;
}

.tech-stack-item {
  display: flex !important;
  flex-direction: column !important;
  text-align: center !important;
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  min-height: 100px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  aspect-ratio: 1 / 1 !important;
}

.tech-stack-icon-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
  flex-shrink: 0 !important;
  position: relative !important;
  overflow: hidden !important;
}

/* High-quality logo rendering */
.tech-stack-icon-container img {
  display: block !important;
  margin: 0 auto !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  object-position: center !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
  transition: all 0.3s ease !important;
  border-radius: 4px !important;
}

/* SVG specific styling */
.tech-stack-icon-container svg {
  display: block !important;
  margin: 0 auto !important;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  transition: all 0.3s ease !important;
}

/* Ensure grid items are properly positioned */
.tech-stack-grid > * {
  justify-self: center !important;
  align-self: stretch !important;
}

/* Enhanced hover effects */
.tech-stack-icon-container:hover img {
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3)) brightness(1.1) !important;
  transform: scale(1.05) !important;
}

.tech-stack-icon-container:hover svg {
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3)) !important;
  transform: scale(1.05) !important;
}

/* Text content improvements */
.line-clamp-1 {
  display: -webkit-box !important;
  -webkit-line-clamp: 1 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  word-break: break-word !important;
}

.line-clamp-2 {
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  word-break: break-word !important;
}

/* Prevent text overflow and ensure proper spacing */
.tech-stack-item h3,
.tech-stack-item p,
.tech-stack-item span {
  max-width: 100% !important;
  word-wrap: break-word !important;
  hyphens: auto !important;
}

/* Ensure proper vertical spacing and perfect alignment */
.tech-stack-item > div {
  min-height: 0 !important;
  flex-shrink: 1 !important;
}

/* Perfect grid alignment - ensure all items are exactly the same size */
.tech-stack-grid > .tech-stack-item {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
}

/* Ensure consistent spacing and alignment */
.tech-stack-grid {
  align-content: center !important;
  justify-content: center !important;
}

/* Perfect centering for grid container */
.tech-stack-grid {
  margin-left: auto !important;
  margin-right: auto !important;
  width: fit-content !important;
  max-width: 100% !important;
}

/* Logo improvements */
.uru-logo-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.uru-logo-container img {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
}

/* Responsive improvements - 8-column grid with larger logos */
@media (max-width: 640px) {
  .text-7xl, .text-6xl, .text-5xl, .text-4xl {
    line-height: 1.3 !important;
    padding: 0.75rem 0.5rem 1.25rem 0.5rem !important;
  }

  /* Mobile: 8 columns with larger elements */
  .tech-stack-grid {
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 0.5rem !important;
    padding: 0 0.5rem !important;
  }

  .tech-stack-item {
    min-height: 100px !important;
  }

  /* Larger content on mobile */
  .tech-stack-item h3 {
    font-size: 0.8rem !important;
    line-height: 1.2 !important;
    font-weight: 600 !important;
  }

  .tech-stack-item .tech-stack-icon-container {
    width: 2rem !important;
    height: 2rem !important;
    margin-bottom: 0.5rem !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* Small tablet: 8 columns with medium elements */
  .tech-stack-grid {
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 0.75rem !important;
  }

  .tech-stack-item {
    min-height: 120px !important;
  }

  .tech-stack-item h3 {
    font-size: 1rem !important;
    font-weight: 600 !important;
  }

  .tech-stack-item .tech-stack-icon-container {
    width: 3rem !important;
    height: 3rem !important;
    margin-bottom: 0.75rem !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* Medium screens: 8 columns with large elements */
  .tech-stack-grid {
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 1rem !important;
  }

  .tech-stack-item {
    min-height: 140px !important;
  }

  .tech-stack-item h3 {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
  }

  .tech-stack-item .tech-stack-icon-container {
    width: 4rem !important;
    height: 4rem !important;
    margin-bottom: 0.75rem !important;
  }
}

@media (min-width: 1025px) {
  /* Large screens: 8 columns with extra large elements */
  .tech-stack-grid {
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 1.5rem !important;
    max-width: 1400px !important;
  }

  .tech-stack-item {
    min-height: 160px !important;
  }

  .tech-stack-item h3 {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
  }

  .tech-stack-item .tech-stack-icon-container {
    width: 5rem !important;
    height: 5rem !important;
    margin-bottom: 1rem !important;
  }
}

/* Ensure proper image loading and fallbacks */
.tech-stack-icon-container img[src=""],
.tech-stack-icon-container img:not([src]) {
  display: none !important;
}

/* Loading state for images */
.tech-stack-icon-container img {
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.tech-stack-icon-container img[src]:not([src=""]) {
  opacity: 1 !important;
}
