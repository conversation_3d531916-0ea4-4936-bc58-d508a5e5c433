# Uru Workspace Platform

A scalable multi-tenant platform that enables companies to manage employee access to AI tools with OAuth integration and intelligent data routing.

## 🚀 Quick Start

### Prerequisites
- **Docker Desktop** (for local development)
- **Node.js 18+** (for running setup scripts)
- **Supabase Account** (for database)
- **Google Cloud Console** (for OAuth)

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd uru-workspace-platform

# Run the interactive setup
npm run setup
```

The setup script will guide you through configuring:
- Database credentials (Supabase)
- Google OAuth settings
- Security keys (JWT, encryption)
- Service URLs (auto-configured for environment)

### 2. Local Development

```bash
# Start development environment with Docker Compose
docker-compose -f docker-compose.dev.yml up --build

# Or use the development build script
./build-dev-containers.sh
```

**Development URLs:**
- Frontend: http://localhost:3000
- Auth Service: http://localhost:8003
- Integration Service: http://localhost:8002
- Composio Service: http://localhost:8001
- MCP Proxy: http://localhost:3001

### 3. Production Deployment (Elestio)

```bash
# Generate production environment variables
npm run fix:production

# Deploy to production
npm run deploy:production
```

**Production URLs:**
- Frontend: https://app.uruenterprises.com
- Auth Service: https://auth.uruenterprises.com
- Integration Service: https://integrations.uruenterprises.com
- MCP Proxy: https://mcp.uruenterprises.com
- Composio Service: (internal only)

## 🧪 Testing & Diagnostics

### Test Environment Configuration
```bash
npm run test:env
```

### Test Service Communication
```bash
npm run test:services
```

### Run All Tests
```bash
npm run test:all
```

### Production Health Check
```bash
npm run fix:production
```

## 🐳 Docker Commands

### Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up --build

# Start in background
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down

# Build specific service
docker-compose -f docker-compose.dev.yml build [service-name]
```

### Production (Elestio)
```bash
# Start production environment
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Health checks
curl https://app.uruenterprises.com/api/health
curl https://auth.uruenterprises.com/health
curl https://integrations.uruenterprises.com/health
curl https://mcp.uruenterprises.com/health
```

### Maintenance
```bash
# Clean up Docker resources
docker-compose down --rmi all --volumes --remove-orphans

# Build all images (development)
docker-compose -f docker-compose.dev.yml build --no-cache

# Build all images (production)
docker-compose build --no-cache
```

## 🏗️ Architecture

### Services

1. **Frontend** (Next.js)
   - User interface and authentication flows
   - Server-side rendering with Pages Router
   - Health check: `/api/health`

2. **Auth Service** (FastAPI)
   - User authentication and JWT management
   - Employee workspace management
   - Health check: `/health`

3. **Integration Service** (FastAPI)
   - Google OAuth integration
   - Third-party service authorization
   - Health check: `/health`

4. **Composio Service** (FastAPI)
   - White-labeled MCP tool integration
   - Personal productivity tools (Gmail, Drive, Calendar)
   - Health check: `/health`

5. **MCP Proxy** (Node.js)
   - Model Context Protocol integration
   - N8N workflow integration
   - Tool orchestration
   - Health check: `/health`

### Network Architecture

- **Development**: Direct localhost communication
- **Production**: Docker internal network with external reverse proxy

### Database

- **Supabase PostgreSQL** for user data and workspace management
- **Real-time subscriptions** for live updates
- **Row Level Security** for multi-tenant isolation

## 🔒 Security

### Authentication Flow

1. User accesses frontend
2. Auth service handles user login and JWT creation
3. Integration service manages Google OAuth for third-party services
4. Composio service provides white-labeled tool access
5. JWT used for subsequent API calls across all services
6. MCP proxy validates JWT with auth service

### Security Features

- **JWT-based authentication** with secure signing
- **Fernet encryption** for sensitive data
- **CORS protection** with environment-specific origins
- **HTTPS enforcement** in production
- **Environment variable security** (no secrets in code)

## 🔧 Configuration

### Environment Variables

The platform uses environment detection to automatically configure URLs and settings:

- **Development**: Uses localhost URLs, debug mode enabled
- **Production**: Uses uruenterprises.com domains, optimized for performance

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `SUPABASE_URL` | Supabase project URL | `https://xxx.supabase.co` |
| `SUPABASE_KEY` | Supabase anon key | `eyJhbGciOiJIUzI1NiIs...` |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | `xxx.apps.googleusercontent.com` |
| `GOOGLE_CLIENT_SECRET` | Google OAuth secret | `GOCSPX-xxx` |
| `JWT_SECRET` | JWT signing secret | `secure-random-string` |
| `ENCRYPTION_KEY` | Fernet encryption key | `base64-encoded-key` |

## 🚨 Troubleshooting

### Common Issues

1. **Services won't start**
   ```bash
   # Check Docker status
   docker --version
   docker-compose ps

   # Check logs
   npm run logs:dev
   ```

2. **Environment variables missing**
   ```bash
   # Re-run setup
   npm run setup

   # Test configuration
   npm run test:env
   ```

3. **Service communication failures**
   ```bash
   # Test connectivity
   npm run test:services

   # Check network
   docker network ls
   ```

4. **OAuth authentication issues**
   - Verify Google OAuth configuration
   - Check redirect URIs match environment
   - Ensure client ID/secret are correct

### Debug Commands

```bash
# Check service health
curl http://localhost:8003/health  # Auth Service
curl http://localhost:8002/health  # Integration Service
curl http://localhost:8001/health  # Composio Service
curl http://localhost:3001/health  # MCP Proxy
curl http://localhost:3000/api/health  # Frontend

# View container logs
docker-compose logs auth-service
docker-compose logs integration-service
docker-compose logs composio-service
docker-compose logs mcp-proxy
docker-compose logs frontend
```

### Getting Help

1. **Check logs first**: `npm run logs:dev`
2. **Run diagnostics**: `npm run test:all`
3. **Review troubleshooting guide**: `TROUBLESHOOTING.md`
4. **Check environment setup**: `npm run test:env`

## 📁 Project Structure

```
uru-workspace-platform/
├── frontend/                 # Next.js frontend application
├── auth-service/            # FastAPI authentication service
├── integration-service/     # FastAPI OAuth integration service
├── composio-service/        # FastAPI Composio bridge service
├── mcp-proxy/              # Node.js MCP proxy service
├── shared/                 # Shared utilities and configuration
├── docker-compose.yml      # Production Docker configuration
├── docker-compose.dev.yml  # Development Docker configuration
├── setup-environment.js    # Interactive environment setup
├── test-environment.js     # Environment configuration test
├── test-services.js        # Service communication test
├── fix-production.js       # Production diagnostics
└── elestio-production.env # Production environment variables
```

## 🔄 Development Workflow

1. **Setup**: Run `npm run setup` to configure environment
2. **Develop**: Use `npm run start:dev` for local development
3. **Test**: Run `npm run test:all` to verify functionality
4. **Deploy**: Use `npm run deploy:production` for production

## 📝 Environment Files

- `.env.development` - Template with all required variables and localhost defaults
- `.env` - Your local configuration (auto-generated by setup)
- `elestio-env-template.txt` - Production environment template

## 🌐 Deployment Platforms

### Local Development
- **Docker Desktop** with development compose file
- **Hot reload** enabled for faster development
- **Debug mode** with detailed logging

### Production (Elestio)
- **Managed Docker hosting** with automatic scaling
- **HTTPS termination** with SSL certificates
- **Environment variable management** through dashboard
- **Health monitoring** and automatic restarts

## 📊 Monitoring

### Health Checks
- All services include health check endpoints
- Docker health checks with automatic restarts
- Service dependency management with wait conditions

### Logging
- Structured logging with environment-specific levels
- Docker log aggregation
- Error tracking and debugging information

---

## 🎯 Next Steps

1. **Run the setup**: `npm run setup`
2. **Start development**: `npm run start:dev`
3. **Test everything**: `npm run test:all`
4. **Deploy to production**: `npm run deploy:production`

For detailed troubleshooting, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).
