#!/usr/bin/env python3
"""
Setup Integration System for Uru Workspace Platform
Creates database tables and populates initial integration definitions
"""

import os
import sys
import logging
from datetime import datetime, timezone
from supabase import create_client, Client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegrationSystemSetup:
    def __init__(self):
        # Get environment variables
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        
        if not self.supabase_url or not self.supabase_key:
            logger.error("❌ Missing required environment variables: SUPABASE_URL, SUPABASE_KEY")
            sys.exit(1)
        
        # Initialize Supabase client
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        logger.info("✅ Supabase client initialized")
    
    def create_tables(self):
        """Create integration management tables"""
        logger.info("🔨 Creating integration management tables...")
        
        # Read SQL file
        sql_file_path = os.path.join(os.path.dirname(__file__), 'create-integration-tables.sql')
        
        try:
            with open(sql_file_path, 'r') as f:
                sql_content = f.read()
            
            # Execute SQL (Note: Supabase doesn't support direct SQL execution via client)
            # We'll create tables using individual operations
            logger.info("📝 SQL file read successfully")
            logger.info("⚠️  Please execute the SQL file manually in Supabase Dashboard > SQL Editor")
            logger.info(f"📁 SQL file location: {sql_file_path}")
            
            # For now, we'll assume tables exist and proceed with data population
            return True
            
        except FileNotFoundError:
            logger.error(f"❌ SQL file not found: {sql_file_path}")
            return False
        except Exception as e:
            logger.error(f"❌ Error reading SQL file: {e}")
            return False
    
    def populate_integration_definitions(self):
        """Populate integration definitions table with initial data"""
        logger.info("📊 Populating integration definitions...")
        
        # Define integration data
        integrations = [
            # Tier 1: Essential Business Communication & Collaboration
            {
                "name": "Slack",
                "description": "Team communication, channel management, file sharing",
                "category": "Communication",
                "composio_app_name": "slack",
                "auth_type": "OAUTH2",
                "default_scopes": ["channels:read", "chat:write", "files:read", "files:write"],
                "capabilities": ["send_message", "list_channels", "upload_file", "read_messages"],
                "tier": 1,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg"
            },
            {
                "name": "Microsoft Teams",
                "description": "Enterprise communication, meetings, collaboration",
                "category": "Communication",
                "composio_app_name": "microsoft_teams",
                "auth_type": "OAUTH2",
                "default_scopes": ["https://graph.microsoft.com/Chat.ReadWrite", "https://graph.microsoft.com/Team.ReadBasic.All"],
                "capabilities": ["send_message", "list_teams", "create_meeting", "manage_channels"],
                "tier": 1,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/microsoft-teams-logo.jpeg"
            },
            {
                "name": "Notion",
                "description": "Knowledge management, documentation, project planning",
                "category": "Productivity",
                "composio_app_name": "notion",
                "auth_type": "OAUTH2",
                "default_scopes": ["read", "write"],
                "capabilities": ["create_page", "update_page", "search_pages", "manage_databases"],
                "tier": 1,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg"
            },
            {
                "name": "Airtable",
                "description": "Database management, project tracking, workflow automation",
                "category": "Productivity",
                "composio_app_name": "airtable",
                "auth_type": "OAUTH2",
                "default_scopes": ["data.records:read", "data.records:write", "schema.bases:read"],
                "capabilities": ["create_record", "update_record", "list_records", "manage_tables"],
                "tier": 1,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/airtable.svg"
            },
            {
                "name": "Asana",
                "description": "Project management, task tracking, team coordination",
                "category": "Project Management",
                "composio_app_name": "asana",
                "auth_type": "OAUTH2",
                "default_scopes": ["default"],
                "capabilities": ["create_task", "update_task", "list_projects", "manage_teams"],
                "tier": 1,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/asana.png"
            },
            
            # Tier 2: CRM & Sales Productivity
            {
                "name": "HubSpot",
                "description": "CRM, marketing automation, sales pipeline management",
                "category": "CRM",
                "composio_app_name": "hubspot",
                "auth_type": "OAUTH2",
                "default_scopes": ["crm.objects.contacts.read", "crm.objects.deals.read", "crm.objects.companies.read"],
                "capabilities": ["manage_contacts", "track_deals", "create_companies", "send_emails"],
                "tier": 2,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/hubspot.webp"
            },
            {
                "name": "Salesforce",
                "description": "Enterprise CRM, lead management, sales analytics",
                "category": "CRM",
                "composio_app_name": "salesforce",
                "auth_type": "OAUTH2",
                "default_scopes": ["api", "refresh_token", "offline_access"],
                "capabilities": ["manage_leads", "track_opportunities", "create_accounts", "run_reports"],
                "tier": 2,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/salesforce.svg"
            },
            {
                "name": "Pipedrive",
                "description": "Sales pipeline management, deal tracking",
                "category": "CRM",
                "composio_app_name": "pipedrive",
                "auth_type": "OAUTH2",
                "default_scopes": ["base"],
                "capabilities": ["manage_deals", "track_pipeline", "create_activities", "manage_contacts"],
                "tier": 2,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pipedrive.svg"
            },
            
            # Tier 3: Document & File Management
            {
                "name": "Dropbox",
                "description": "File storage, sharing, collaboration",
                "category": "File Management",
                "composio_app_name": "dropbox",
                "auth_type": "OAUTH2",
                "default_scopes": ["files.content.read", "files.content.write", "files.metadata.read"],
                "capabilities": ["upload_files", "download_files", "share_files", "manage_folders"],
                "tier": 3,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/dropbox.svg"
            },
            {
                "name": "Google Docs",
                "description": "Document creation, real-time collaboration",
                "category": "File Management",
                "composio_app_name": "googledocs",
                "auth_type": "OAUTH2",
                "default_scopes": ["https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/drive.file"],
                "capabilities": ["create_document", "edit_document", "share_document", "export_document"],
                "tier": 3,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-docs.svg"
            },
            
            # Tier 4: Specialized Business Tools
            {
                "name": "Calendly",
                "description": "Meeting scheduling, appointment booking",
                "category": "Scheduling",
                "composio_app_name": "calendly",
                "auth_type": "OAUTH2",
                "default_scopes": ["default"],
                "capabilities": ["schedule_meeting", "list_events", "manage_availability", "send_invites"],
                "tier": 4,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/calendly.svg"
            },
            {
                "name": "Zoom",
                "description": "Video conferencing, webinars, recordings",
                "category": "Communication",
                "composio_app_name": "zoom",
                "auth_type": "OAUTH2",
                "default_scopes": ["meeting:write", "meeting:read", "webinar:write"],
                "capabilities": ["create_meeting", "start_meeting", "manage_webinars", "access_recordings"],
                "tier": 4,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zoom.svg"
            },
            
            # Tier 5: Marketing & Analytics
            {
                "name": "Mailchimp",
                "description": "Email marketing, automation campaigns",
                "category": "Marketing",
                "composio_app_name": "mailchimp",
                "auth_type": "OAUTH2",
                "default_scopes": ["default"],
                "capabilities": ["create_campaign", "manage_lists", "send_emails", "track_analytics"],
                "tier": 5,
                "logo_url": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mailchimp.svg"
            }
        ]
        
        try:
            # Insert integrations using upsert to handle conflicts
            for integration in integrations:
                result = self.supabase.table('integration_definitions').upsert(
                    integration,
                    on_conflict='composio_app_name'
                ).execute()
                
                if result.data:
                    logger.info(f"✅ Added/Updated integration: {integration['name']}")
                else:
                    logger.warning(f"⚠️  No data returned for integration: {integration['name']}")
            
            logger.info(f"🎉 Successfully populated {len(integrations)} integration definitions")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error populating integration definitions: {e}")
            return False
    
    def verify_setup(self):
        """Verify the integration system setup"""
        logger.info("🔍 Verifying integration system setup...")
        
        try:
            # Check integration_definitions table
            definitions_result = self.supabase.table('integration_definitions').select('*').execute()
            definitions_count = len(definitions_result.data) if definitions_result.data else 0
            
            logger.info(f"📊 Integration definitions: {definitions_count}")
            
            # Check employee_integrations table
            employee_integrations_result = self.supabase.table('employee_integrations').select('*').execute()
            employee_integrations_count = len(employee_integrations_result.data) if employee_integrations_result.data else 0
            
            logger.info(f"👥 Employee integrations: {employee_integrations_count}")
            
            if definitions_count > 0:
                logger.info("✅ Integration system setup verified successfully")
                return True
            else:
                logger.error("❌ No integration definitions found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error verifying setup: {e}")
            return False
    
    def run_setup(self):
        """Run the complete integration system setup"""
        logger.info("🚀 Starting integration system setup...")
        
        # Step 1: Create tables
        if not self.create_tables():
            logger.error("❌ Failed to create tables")
            return False
        
        # Step 2: Populate integration definitions
        if not self.populate_integration_definitions():
            logger.error("❌ Failed to populate integration definitions")
            return False
        
        # Step 3: Verify setup
        if not self.verify_setup():
            logger.error("❌ Setup verification failed")
            return False
        
        logger.info("🎉 Integration system setup completed successfully!")
        return True

def main():
    """Main function"""
    logger.info("🔧 Uru Integration System Setup")
    
    setup = IntegrationSystemSetup()
    success = setup.run_setup()
    
    if success:
        logger.info("✅ Setup completed successfully")
        sys.exit(0)
    else:
        logger.error("❌ Setup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
