#!/usr/bin/env python3
"""
Apply Remember Me Tokens Database Schema
This script creates the remember_me_tokens table and related functions
"""

import os
import sys
from supabase import create_client, Client
from datetime import datetime

def load_environment():
    """Load environment variables from .env file"""
    env_file = os.path.join(os.path.dirname(__file__), '..', '.env')
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

def get_supabase_client() -> Client:
    """Initialize Supabase client"""
    load_environment()
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
    
    return create_client(supabase_url, supabase_key)

def apply_schema(supabase: Client):
    """Apply the remember me tokens schema"""
    
    # Read the SQL schema file
    schema_file = os.path.join(os.path.dirname(__file__), 'create-remember-me-tokens-table.sql')
    
    if not os.path.exists(schema_file):
        raise FileNotFoundError(f"Schema file not found: {schema_file}")
    
    with open(schema_file, 'r') as f:
        sql_content = f.read()
    
    # Split the SQL into individual statements
    statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
    
    print(f"Applying {len(statements)} SQL statements...")
    
    for i, statement in enumerate(statements, 1):
        try:
            # Skip comments and empty statements
            if statement.startswith('--') or not statement:
                continue
                
            print(f"Executing statement {i}/{len(statements)}...")
            
            # Execute the SQL statement
            result = supabase.rpc('exec_sql', {'sql': statement}).execute()
            
            if result.data:
                print(f"✓ Statement {i} executed successfully")
            else:
                print(f"⚠ Statement {i} executed with no data returned")
                
        except Exception as e:
            print(f"✗ Error executing statement {i}: {str(e)}")
            print(f"Statement: {statement[:100]}...")
            # Continue with other statements
            continue
    
    print("\nSchema application completed!")

def verify_schema(supabase: Client):
    """Verify that the schema was applied correctly"""
    try:
        # Check if the table exists
        result = supabase.table('remember_me_tokens').select('count', count='exact').limit(0).execute()
        print("✓ remember_me_tokens table exists and is accessible")
        
        # Check if functions exist by trying to call them
        try:
            result = supabase.rpc('cleanup_expired_remember_me_tokens').execute()
            print("✓ cleanup_expired_remember_me_tokens function exists")
        except Exception as e:
            print(f"⚠ cleanup_expired_remember_me_tokens function may not exist: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Schema verification failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("Remember Me Tokens Schema Application")
    print("=" * 50)
    
    try:
        # Initialize Supabase client
        print("Initializing Supabase client...")
        supabase = get_supabase_client()
        print("✓ Supabase client initialized")
        
        # Apply schema
        print("\nApplying remember me tokens schema...")
        apply_schema(supabase)
        
        # Verify schema
        print("\nVerifying schema...")
        if verify_schema(supabase):
            print("\n✅ Remember me tokens schema applied successfully!")
        else:
            print("\n❌ Schema verification failed")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
