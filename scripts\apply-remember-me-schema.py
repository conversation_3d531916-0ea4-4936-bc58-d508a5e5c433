#!/usr/bin/env python3
"""
Apply Remember Me Tokens Database Schema
This script creates the remember_me_tokens table and related functions
"""

import os
import sys
from supabase import create_client, Client
from datetime import datetime

def load_environment():
    """Load environment variables from .env file"""
    env_file = os.path.join(os.path.dirname(__file__), '..', '.env')
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

def get_supabase_client() -> Client:
    """Initialize Supabase client"""
    load_environment()
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
    
    return create_client(supabase_url, supabase_key)

def apply_schema(supabase: Client):
    """Apply the remember me tokens schema"""

    print("Creating remember_me_tokens table directly...")

    # Create the table with a simplified approach
    try:
        # First, try to create the table using a simple insert/upsert approach
        # This will create the table if it doesn't exist
        test_data = {
            "employee_id": "00000000-0000-0000-0000-000000000000",
            "workspace_id": "00000000-0000-0000-0000-000000000000",
            "token_hash": "test_hash",
            "selector": "test_selector",
            "device_fingerprint": {},
            "user_agent": "test",
            "ip_address": "127.0.0.1",
            "expires_at": "2025-01-01T00:00:00Z"
        }

        # Try to insert test data - this will fail if table doesn't exist
        result = supabase.table('remember_me_tokens').insert(test_data).execute()
        print("✓ Table already exists")

        # Clean up test data
        supabase.table('remember_me_tokens').delete().eq('selector', 'test_selector').execute()

    except Exception as e:
        error_msg = str(e)
        if 'does not exist' in error_msg or 'relation' in error_msg:
            print("Table doesn't exist, need to create it manually...")
            print("\n" + "="*60)
            print("MANUAL SCHEMA APPLICATION REQUIRED")
            print("="*60)
            print("\nPlease run the following SQL in your Supabase SQL Editor:")
            print("\n1. Go to https://supabase.com/dashboard")
            print("2. Select your project")
            print("3. Go to SQL Editor")
            print("4. Run the contents of: scripts/create-remember-me-tokens-table.sql")
            print("\nAlternatively, copy and paste this simplified table creation:")

            simplified_sql = """
-- Create remember_me_tokens table
CREATE TABLE IF NOT EXISTS remember_me_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID NOT NULL,
  workspace_id UUID NOT NULL,
  token_hash VARCHAR(255) NOT NULL UNIQUE,
  selector VARCHAR(64) NOT NULL UNIQUE,
  device_fingerprint JSONB DEFAULT '{}',
  user_agent TEXT,
  ip_address INET,
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_revoked BOOLEAN DEFAULT false,
  revoked_at TIMESTAMP WITH TIME ZONE,
  revoked_reason VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_employee_id ON remember_me_tokens(employee_id);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_selector ON remember_me_tokens(selector);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_token_hash ON remember_me_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_remember_me_tokens_expires_at ON remember_me_tokens(expires_at);

-- Add foreign key constraint
ALTER TABLE remember_me_tokens
ADD CONSTRAINT fk_remember_me_tokens_employee_id
FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE;

-- Enable RLS
ALTER TABLE remember_me_tokens ENABLE ROW LEVEL SECURITY;

-- Grant permissions
GRANT SELECT ON remember_me_tokens TO authenticated;
GRANT ALL ON remember_me_tokens TO service_role;
"""
            print(simplified_sql)
            print("\n" + "="*60)
            return False
        else:
            print(f"✗ Unexpected error: {error_msg}")
            return False

    print("\nSchema application completed!")
    return True

def verify_schema(supabase: Client):
    """Verify that the schema was applied correctly"""
    try:
        # Check if the table exists
        result = supabase.table('remember_me_tokens').select('count', count='exact').limit(0).execute()
        print("✓ remember_me_tokens table exists and is accessible")
        
        # Check if functions exist by trying to call them
        try:
            result = supabase.rpc('cleanup_expired_remember_me_tokens').execute()
            print("✓ cleanup_expired_remember_me_tokens function exists")
        except Exception as e:
            print(f"⚠ cleanup_expired_remember_me_tokens function may not exist: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Schema verification failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("Remember Me Tokens Schema Application")
    print("=" * 50)
    
    try:
        # Initialize Supabase client
        print("Initializing Supabase client...")
        supabase = get_supabase_client()
        print("✓ Supabase client initialized")
        
        # Apply schema
        print("\nApplying remember me tokens schema...")
        schema_applied = apply_schema(supabase)

        if schema_applied:
            # Verify schema
            print("\nVerifying schema...")
            if verify_schema(supabase):
                print("\n✅ Remember me tokens schema applied successfully!")
            else:
                print("\n❌ Schema verification failed")
                sys.exit(1)
        else:
            print("\n⚠️ Manual schema application required - see instructions above")
            sys.exit(0)
            
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
