# ===========================================
# URU WORKSPACE PLATFORM - PRODUCTION ENVIRONMENT
# Elestio Docker Deployment Configuration
# ===========================================

# ===========================================
# ENVIRONMENT DETECTION
# ===========================================
NODE_ENV=production
ENVIRONMENT=production
DEBUG=false

# ===========================================
# DATABASE CONFIGURATION (SUPABASE)
# ===========================================
SUPABASE_URL=https://sipvdxjupgnymlshsoro.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNpcHZkeGp1cGdueW1sc2hzb3JvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0ODQzODIsImV4cCI6MjA2NjA2MDM4Mn0.iY-BFFxDXkbI1qiPGqK9u9Y-F5MWuYptTsmSlLuehIs
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# ===========================================
# AUTHENTICATION & SECURITY
# ===========================================
JWT_SECRET=uru-workspace-platform-super-secret-jwt-key-2025
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
ENCRYPTION_KEY=jeUI2_9ZoSiCyETMA45CyEEaJhtbmCO909bpUFqu3Ks=

# ===========================================
# GOOGLE OAUTH CONFIGURATION
# ===========================================
GOOGLE_CLIENT_ID=949099265240-g5p5m29mdpr2jisrbmbkksm84f9q89bv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# OAuth Redirect URIs for Production
GOOGLE_REDIRECT_URI_AUTH=https://auth.uruenterprises.com/oauth/google/callback
GOOGLE_REDIRECT_URI_INTEGRATIONS=https://integrations.uruenterprises.com/oauth/google/callback

# ===========================================
# COMPOSIO INTEGRATION
# ===========================================
# IMPORTANT: Replace with your actual Composio API key from https://app.composio.dev/settings
# This is a placeholder that MUST be updated before production deployment
URU_COMPOSIO_API_KEY=YOUR_ACTUAL_COMPOSIO_API_KEY_HERE
URU_COMPOSIO_BASE_URL=https://backend.composio.dev/api

# ===========================================
# SERVICE URLS (PRODUCTION)
# ===========================================
FRONTEND_URL=https://app.uruenterprises.com
AUTH_SERVICE_URL=http://auth-service:8003
INTEGRATIONS_SERVICE_URL=http://integration-service:8002
COMPOSIO_SERVICE_URL=http://composio-service:8001
MCP_PROXY_URL=http://mcp-proxy:3001

# ===========================================
# FRONTEND CONFIGURATION (NEXT.JS)
# ===========================================
NEXT_PUBLIC_API_URL=https://app.uruenterprises.com
NEXT_PUBLIC_AUTH_URL=https://auth.uruenterprises.com
NEXT_PUBLIC_INTEGRATIONS_URL=https://integrations.uruenterprises.com
NEXT_PUBLIC_MCP_URL=https://mcp.uruenterprises.com
NEXT_PUBLIC_COMPOSIO_URL=https://composio.uruenterprises.com

# ===========================================
# CORS CONFIGURATION
# ===========================================
CORS_ORIGINS=https://app.uruenterprises.com,https://auth.uruenterprises.com,https://integrations.uruenterprises.com,https://mcp.uruenterprises.com

# ===========================================
# EXTERNAL INTEGRATIONS
# ===========================================
N8N_SSE_URL=https://n8n-uru-u46170.vm.elestio.app/mcp/4caa0a7f-1251-45a8-97a5-7663841a2c9b/sse
# IMPORTANT: Replace with your actual OpenAI API key from https://platform.openai.com/api-keys
# This is a placeholder that MUST be updated before production deployment
OPENAI_API_KEY=YOUR_ACTUAL_OPENAI_API_KEY_HERE

# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
APP_HOST=0.0.0.0
APP_PORT=8000
PORT=3000
HOSTNAME=0.0.0.0

# ===========================================
# SERVICE-SPECIFIC PORTS
# ===========================================
# Auth Service
AUTH_SERVICE_PORT=8003

# Integration Service  
INTEGRATION_SERVICE_PORT=8002

# Composio Service
COMPOSIO_SERVICE_PORT=8001

# MCP Proxy Service
MCP_PROXY_PORT=3001

# Frontend Service
FRONTEND_PORT=3000
