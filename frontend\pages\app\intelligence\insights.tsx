import React from 'react';
import { GetServerSideProps } from 'next';
import { Sidebar } from '../../../components/shared/Sidebar';
import { ProtectedRoute } from '../../../components/auth/ProtectedRoute';
import { Brain, Target, TrendingUp, DollarSign, Users, Calendar } from 'lucide-react';

export default function InsightsPage() {
  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-900 text-white">
      <Sidebar />
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <div className="bg-gray-900 border-b border-gray-800 flex-shrink-0">
          <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-white">Ignition Consultants</h1>
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-green-400 text-sm">Live</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                JM
              </div>
              <span className="text-white text-sm">Jackson Moss</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Pattern Discovery Engine */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-6">
                <Brain className="w-6 h-6 text-cyan-400" />
                <h2 className="text-xl font-semibold text-white">Pattern Discovery Engine</h2>
              </div>
              <p className="text-gray-400 mb-6">Automatically identifies patterns, trends, and opportunities across all company data sources.</p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-green-400 font-semibold mb-2">Revenue Growth Pattern</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    Clients who receive monthly strategic guidance show 35% higher revenue growth compared to those with quarterly check-ins.
                  </p>
                  <div className="text-xs text-gray-400">
                    Insight: Consider standardizing monthly touchpoints for all clients to maximize growth potential.
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-blue-400 font-semibold mb-2">Communication Impact</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    Response times under 4 hours correlate with 60% higher client satisfaction and 50% better engagement duration.
                  </p>
                  <div className="text-xs text-gray-400">
                    Insight: Maintaining fast response times is critical for client satisfaction and retention.
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-orange-400 font-semibold mb-2">Technology Adoption Opportunity</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    73% increase in AI/automation questions suggests $2.4M annual revenue opportunity in technology consulting.
                  </p>
                  <div className="text-xs text-gray-400">
                    Insight: Strong market demand for technology consulting services among existing client base.
                  </div>
                </div>
              </div>
            </div>

            {/* Predictive Analytics */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-6">
                <Target className="w-6 h-6 text-purple-400" />
                <h2 className="text-xl font-semibold text-white">Predictive Analytics</h2>
              </div>
              <p className="text-gray-400 mb-6">AI-powered predictions based on historical patterns and current trends.</p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">Q1 2025</div>
                  <div className="text-white font-medium mb-1">Predicted 25% increase in strategic engagements</div>
                  <div className="text-gray-400 text-sm">Based on current client inquiry patterns and satisfaction trends</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400 mb-2">15 Clients</div>
                  <div className="text-white font-medium mb-1">Likely to request technology consulting</div>
                  <div className="text-gray-400 text-sm">Based on conversation analysis and expressed interests</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-400 mb-2">3 Industries</div>
                  <div className="text-white font-medium mb-1">Emerging expansion opportunities</div>
                  <div className="text-gray-400 text-sm">Healthcare, fintech, and manufacturing showing strong interest patterns</div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-cyan-400 mb-2">$4.2M</div>
                  <div className="text-white font-medium mb-1">Projected annual revenue potential</div>
                  <div className="text-gray-400 text-sm">From identified opportunities and service expansion</div>
                </div>
              </div>
            </div>

            {/* Actionable Recommendations */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-6">
                <TrendingUp className="w-6 h-6 text-green-400" />
                <h2 className="text-xl font-semibold text-white">Actionable Recommendations</h2>
              </div>
              <p className="text-gray-400 mb-6">Data-driven recommendations for business growth and optimization.</p>

              <div className="space-y-4">
                <div className="flex items-start space-x-4 p-4 bg-green-600/10 border border-green-600/30 rounded-lg">
                  <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">HIGH</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-green-400 font-semibold mb-2">Implement Monthly Check-in Standard</h4>
                    <p className="text-gray-300 text-sm mb-2">
                      Standardize monthly touchpoints for all clients to improve satisfaction scores by 85% and retention by 60%.
                    </p>
                    <div className="text-xs text-gray-400">
                      Expected Impact: $1.2M additional annual revenue from improved retention and upselling opportunities
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-blue-600/10 border border-blue-600/30 rounded-lg">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">MED</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-blue-400 font-semibold mb-2">Develop Digital CFO Service Package</h4>
                    <p className="text-gray-300 text-sm mb-2">
                      Create technology consulting service targeting 12 clients who expressed AI/automation interest.
                    </p>
                    <div className="text-xs text-gray-400">
                      Expected Impact: $2.4M annual revenue potential from new service line
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-purple-600/10 border border-purple-600/30 rounded-lg">
                  <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">MED</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-purple-400 font-semibold mb-2">Expand Fractional CFO Services</h4>
                    <p className="text-gray-300 text-sm mb-2">
                      Target healthcare, fintech, and manufacturing verticals based on emerging client interest patterns.
                    </p>
                    <div className="text-xs text-gray-400">
                      Expected Impact: 3 new industry verticals, $800K additional annual revenue
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-orange-600/10 border border-orange-600/30 rounded-lg">
                  <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">LOW</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-orange-400 font-semibold mb-2">Optimize Response Time Processes</h4>
                    <p className="text-gray-300 text-sm mb-2">
                      Target sub-3 hour response times to further improve client satisfaction and differentiate from competitors.
                    </p>
                    <div className="text-xs text-gray-400">
                      Expected Impact: 15% improvement in client satisfaction scores
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
    </ProtectedRoute>
  );
}

// Prevent static generation for protected routes
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
