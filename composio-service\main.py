"""
Uru Composio Bridge Service

This service provides a fully white-labeled interface to Composio's MCP tools.
All Composio branding, URLs, and implementation details are hidden from end users.
Only Uru-branded endpoints and responses are exposed.
"""

import os
import json
import secrets
import logging
import urllib.parse
import re
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

import httpx
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import RedirectResponse, JSONResponse
from pydantic import BaseModel, Field
from supabase import create_client, Client
from cryptography.fernet import Fernet
from integration_config import integration_registry, IntegrationConfig, AuthType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
COMPOSIO_API_KEY = os.getenv("URU_COMPOSIO_API_KEY")
COMPOSIO_BASE_URL = os.getenv("URU_COMPOSIO_BASE_URL", "https://backend.composio.dev/api")
JWT_SECRET = os.getenv("JWT_SECRET")
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")
# Parse CORS origins with fallback
cors_origins_env = os.getenv("CORS_ORIGINS", "")
if cors_origins_env:
    try:
        # Try JSON parsing first
        import json
        CORS_ORIGINS = json.loads(cors_origins_env)
    except:
        # Fall back to comma-separated parsing
        CORS_ORIGINS = [origin.strip() for origin in cors_origins_env.split(",") if origin.strip()]
else:
    # Default development origins for new architecture
    CORS_ORIGINS = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8001",
        "http://localhost:8002",
        "http://localhost:8003"
    ]

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3000")
AUTH_SERVICE_URL = os.getenv("AUTH_SERVICE_URL", "http://localhost:8003")
INTEGRATIONS_SERVICE_URL = os.getenv("INTEGRATIONS_SERVICE_URL", "http://localhost:8002")
COMPOSIO_SERVICE_URL = os.getenv("COMPOSIO_SERVICE_URL", "http://localhost:8001")

# Google OAuth scopes for different apps
GOOGLE_SCOPES = {
    "gmail": [
        "https://www.googleapis.com/auth/gmail.modify",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ],
    "drive": [
        "https://www.googleapis.com/auth/drive",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ],
    "calendar": [
        "https://www.googleapis.com/auth/calendar",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ]
}

# Initialize encryption (will be properly initialized in lifespan)
cipher_suite = None

# Initialize Supabase (will be properly initialized in lifespan)
supabase: Client = None

# HTTP client for Composio API calls (will be initialized in lifespan)
http_client = None

# Security
security = HTTPBearer()

# ===========================================
# SECURITY MIDDLEWARE
# ===========================================

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to all responses"""

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

        # HSTS for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        # CSP for API endpoints
        response.headers["Content-Security-Policy"] = "default-src 'none'; frame-ancestors 'none';"

        return response

class RequestIDMiddleware(BaseHTTPMiddleware):
    """Add unique request ID for tracing"""

    async def dispatch(self, request: Request, call_next):
        import uuid
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id

        return response

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Sanitize error responses for production"""

    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Log full error details
            request_id = getattr(request.state, 'request_id', 'unknown')
            logger.error(f"Request {request_id} failed: {str(e)}", exc_info=True)

            # Return sanitized error response
            if isinstance(e, HTTPException):
                return JSONResponse(
                    status_code=e.status_code,
                    content={
                        "error": e.detail,
                        "request_id": request_id,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
            else:
                # Generic error for unexpected exceptions
                return JSONResponse(
                    status_code=500,
                    content={
                        "error": "Internal server error",
                        "request_id": request_id,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )

# ===========================================
# HTTP PROTECTION MIDDLEWARE
# ===========================================

class RequestSizeLimitMiddleware(BaseHTTPMiddleware):
    """Limit request body size to prevent DoS attacks"""

    def __init__(self, app, max_size: int = 10 * 1024 * 1024):  # 10MB default
        super().__init__(app)
        self.max_size = max_size

    async def dispatch(self, request: Request, call_next):
        # Check Content-Length header
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                if size > self.max_size:
                    return JSONResponse(
                        status_code=413,
                        content={
                            "error": "Request entity too large",
                            "max_size_mb": self.max_size / (1024 * 1024),
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    )
            except ValueError:
                pass  # Invalid Content-Length, let it through for now

        return await call_next(request)

class TimeoutMiddleware(BaseHTTPMiddleware):
    """Add request timeout protection"""

    def __init__(self, app, timeout_seconds: int = 30):
        super().__init__(app)
        self.timeout_seconds = timeout_seconds

    async def dispatch(self, request: Request, call_next):
        try:
            import asyncio
            return await asyncio.wait_for(
                call_next(request),
                timeout=self.timeout_seconds
            )
        except asyncio.TimeoutError:
            return JSONResponse(
                status_code=408,
                content={
                    "error": "Request timeout",
                    "timeout_seconds": self.timeout_seconds,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

# ===========================================
# INPUT VALIDATION
# ===========================================

class InputValidator:
    """Comprehensive input validation and sanitization"""

    # Regex patterns for validation
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    UUID_PATTERN = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')
    WORKSPACE_SLUG_PATTERN = re.compile(r'^[a-z0-9-]{3,50}$')

    # Dangerous patterns to block
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\bUNION\s+SELECT\b)"
    ]

    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>.*?</iframe>"
    ]

    @classmethod
    def validate_email(cls, email: str) -> bool:
        """Validate email format"""
        if not email or len(email) > 254:
            return False
        return bool(cls.EMAIL_PATTERN.match(email))

    @classmethod
    def validate_uuid(cls, uuid_str: str) -> bool:
        """Validate UUID format"""
        if not uuid_str:
            return False
        return bool(cls.UUID_PATTERN.match(uuid_str.lower()))

    @classmethod
    def validate_workspace_slug(cls, slug: str) -> bool:
        """Validate workspace slug format"""
        if not slug:
            return False
        return bool(cls.WORKSPACE_SLUG_PATTERN.match(slug))

    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 1000) -> str:
        """Sanitize string input"""
        if not isinstance(value, str):
            raise ValueError("Input must be a string")

        # Truncate if too long
        if len(value) > max_length:
            raise ValueError(f"Input too long (max {max_length} characters)")

        # Check for SQL injection patterns
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError("Potentially malicious input detected")

        # Check for XSS patterns
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError("Potentially malicious input detected")

        # Basic sanitization
        value = value.strip()

        return value

    @classmethod
    def validate_tool_parameters(cls, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize tool parameters"""
        if not isinstance(parameters, dict):
            raise ValueError("Parameters must be a dictionary")

        sanitized = {}
        for key, value in parameters.items():
            # Validate key
            if not isinstance(key, str) or len(key) > 100:
                raise ValueError(f"Invalid parameter key: {key}")

            # Sanitize key
            clean_key = cls.sanitize_string(key, 100)

            # Sanitize value based on type
            if isinstance(value, str):
                clean_value = cls.sanitize_string(value, 10000)
            elif isinstance(value, (int, float, bool)):
                clean_value = value
            elif isinstance(value, list):
                # Validate list items
                if len(value) > 100:
                    raise ValueError("Too many list items")
                clean_value = [cls.sanitize_string(str(item), 1000) if isinstance(item, str) else item for item in value]
            elif isinstance(value, dict):
                # Recursively validate nested dict
                clean_value = cls.validate_tool_parameters(value)
            else:
                raise ValueError(f"Unsupported parameter type: {type(value)}")

            sanitized[clean_key] = clean_value

        return sanitized

# Pydantic models
class ComposioEntityRequest(BaseModel):
    """Request to create or retrieve Composio entity"""
    pass

class ComposioConnectRequest(BaseModel):
    """Request to connect an app via Composio"""
    redirect_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = {}

class ComposioExecuteRequest(BaseModel):
    """Request to execute MCP tool via Composio"""
    tool_name: str
    parameters: Dict[str, Any] = {}

class ComposioWebhookRequest(BaseModel):
    """Webhook payload from Composio"""
    event_type: str
    entity_id: str
    connection_id: Optional[str] = None
    app_name: Optional[str] = None
    data: Dict[str, Any] = {}

# Startup/shutdown handlers
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler"""
    logger.info("🚀 Uru Composio Bridge Service starting up...")

    # Debug environment variables
    logger.info(f"GOOGLE_CLIENT_ID: {GOOGLE_CLIENT_ID}")
    logger.info(f"GOOGLE_CLIENT_SECRET: {'***' if GOOGLE_CLIENT_SECRET else 'None'}")
    logger.info(f"FRONTEND_URL: {FRONTEND_URL}")
    logger.info(f"AUTH_SERVICE_URL: {AUTH_SERVICE_URL}")
    logger.info(f"INTEGRATIONS_SERVICE_URL: {INTEGRATIONS_SERVICE_URL}")

    # Validate required environment variables
    critical_vars = [
        "SUPABASE_URL", "SUPABASE_KEY", "JWT_SECRET", "ENCRYPTION_KEY"
    ]
    optional_vars = ["URU_COMPOSIO_API_KEY"]

    missing_critical = [var for var in critical_vars if not os.getenv(var)]
    missing_optional = [var for var in optional_vars if not os.getenv(var)]

    if missing_critical:
        logger.error(f"❌ Missing critical environment variables: {missing_critical}")
        logger.error("💡 Please ensure these environment variables are set in your Elestio deployment:")
        for var in missing_critical:
            logger.error(f"   - {var}")
        logger.error("🔧 Check your Elestio environment variables configuration")
        raise RuntimeError(f"Missing critical environment variables: {missing_critical}")

    if missing_optional:
        logger.warning(f"⚠️ Missing optional environment variables: {missing_optional}")
        logger.warning("🔧 Composio integration features will be limited without these variables")
        logger.warning("💡 Add URU_COMPOSIO_API_KEY to enable full Composio functionality")

    # Initialize encryption
    global cipher_suite
    try:
        if ENCRYPTION_KEY:
            # Handle both base64-encoded keys and raw string keys
            try:
                # First try to use the key as-is (assuming it's base64-encoded)
                cipher_suite = Fernet(ENCRYPTION_KEY)
                logger.info("✅ Encryption initialized with base64-encoded key")
            except Exception:
                # If that fails, try encoding it as UTF-8 (for raw string keys)
                cipher_suite = Fernet(ENCRYPTION_KEY.encode())
                logger.info("✅ Encryption initialized with UTF-8 encoded key")
        else:
            # This should not happen due to validation above, but handle gracefully
            cipher_suite = Fernet(Fernet.generate_key())
            logger.warning("⚠️ Generated new encryption key - tokens will not persist across restarts")
    except Exception as e:
        logger.error(f"❌ Failed to initialize encryption: {e}")
        logger.error(f"ENCRYPTION_KEY format: {type(ENCRYPTION_KEY)} length: {len(ENCRYPTION_KEY) if ENCRYPTION_KEY else 0}")
        raise RuntimeError(f"Encryption initialization failed: {e}")

    # Initialize Supabase with optimized connection settings
    global supabase
    try:
        # Configure Supabase client with connection pooling
        supabase_options = {
            'postgrest': {
                'timeout': 30,
                'max_retries': 3,
                'retry_delay': 1
            },
            'realtime': {
                'timeout': 30
            }
        }

        supabase = create_client(
            SUPABASE_URL,
            SUPABASE_KEY,
            options=supabase_options
        )

        # Test connection and log performance
        start_time = datetime.now(timezone.utc)
        test_result = supabase.table('employees').select('id').limit(1).execute()
        connection_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

        logger.info(f"✅ Supabase client initialized (connection test: {connection_time:.2f}ms)")

    except Exception as e:
        logger.error(f"❌ Failed to initialize Supabase client: {e}")
        raise RuntimeError(f"Supabase initialization failed: {e}")

    # Initialize HTTP client with comprehensive timeout configuration
    global http_client
    timeout_config = httpx.Timeout(
        connect=10.0,  # Connection timeout
        read=30.0,     # Read timeout
        write=10.0,    # Write timeout
        pool=5.0       # Pool timeout
    )
    http_client = httpx.AsyncClient(
        timeout=timeout_config,
        limits=httpx.Limits(
            max_keepalive_connections=20,
            max_connections=100,
            keepalive_expiry=30.0
        )
    )

    # Test Composio API connectivity (only if API key is available)
    if COMPOSIO_API_KEY:
        try:
            response = await http_client.get(
                f"{COMPOSIO_BASE_URL}/v1/entities",
                headers={"X-API-Key": COMPOSIO_API_KEY}
            )
            if response.status_code == 200:
                logger.info("✅ Composio API connectivity verified")
            else:
                logger.warning(f"⚠️ Composio API returned status {response.status_code}")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Composio API: {e}")
    else:
        logger.warning("⚠️ Skipping Composio API test - no API key provided")

    # Test Supabase connectivity
    try:
        result = supabase.table('employees').select('id').limit(1).execute()
        logger.info("✅ Supabase connectivity verified")
    except Exception as e:
        logger.error(f"❌ Failed to connect to Supabase: {e}")

    logger.info("🎉 Uru Composio Bridge Service ready!")

    yield

    # Cleanup
    if http_client:
        await http_client.aclose()
    logger.info("👋 Uru Composio Bridge Service shutting down...")

# Initialize FastAPI app with security configurations
app = FastAPI(
    title="Uru Composio Bridge Service",
    description="White-labeled MCP tool integration via Composio",
    version="1.0.0",
    lifespan=lifespan,
    # Security configurations
    docs_url="/docs" if os.getenv("ENVIRONMENT") == "development" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") == "development" else None,
    openapi_url="/openapi.json" if os.getenv("ENVIRONMENT") == "development" else None
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS if CORS_ORIGINS else ["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Apply security middleware (order matters - last added is executed first)
app.add_middleware(ErrorHandlingMiddleware)
app.add_middleware(RequestIDMiddleware)
app.add_middleware(SecurityHeadersMiddleware)

# Apply HTTP protection middleware
app.add_middleware(TimeoutMiddleware, timeout_seconds=30)
app.add_middleware(RequestSizeLimitMiddleware, max_size=10 * 1024 * 1024)  # 10MB limit

# Authentication dependency
async def get_current_employee(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Validate JWT token and return employee data"""
    try:
        import jwt
        
        # Decode JWT
        payload = jwt.decode(credentials.credentials, JWT_SECRET, algorithms=["HS256"])
        employee_id = payload.get("employee_id")
        
        if not employee_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        # Get employee data with workspace
        result = supabase.table('employees').select(
            "*, workspaces!inner(id, slug, name)"
        ).eq("id", employee_id).single().execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Employee not found"
            )
        
        return result.data

    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )

# ===========================================
# WORKSPACE VALIDATION MIDDLEWARE
# ===========================================

async def validate_workspace_access(employee: Dict[str, Any], target_workspace_id: str = None) -> bool:
    """Validate that employee has access to the specified workspace"""
    try:
        employee_workspace_id = employee.get("workspace_id")
        if not employee_workspace_id:
            logger.error(f"Employee {employee.get('id')} has no workspace_id")
            return False

        # If target workspace is specified, ensure it matches employee's workspace
        if target_workspace_id and target_workspace_id != employee_workspace_id:
            logger.warning(f"Employee {employee.get('id')} attempted to access workspace {target_workspace_id} but belongs to {employee_workspace_id}")
            return False

        return True

    except Exception as e:
        logger.error(f"Workspace validation error: {e}")
        return False

async def get_employee_with_workspace_validation(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    target_workspace_id: str = None
) -> Dict[str, Any]:
    """Get current employee and validate workspace access"""
    employee = await get_current_employee(credentials)

    if not await validate_workspace_access(employee, target_workspace_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: insufficient workspace permissions"
        )

    return employee

# ===========================================
# PERSISTENT RATE LIMITING SYSTEM
# ===========================================

RATE_LIMIT_REQUESTS = 100  # requests per window
RATE_LIMIT_WINDOW = 3600  # 1 hour in seconds

async def check_rate_limit(employee_id: str, workspace_id: str = None) -> bool:
    """Check if employee has exceeded rate limit using database storage"""
    try:
        now = datetime.now(timezone.utc)
        window_start = now - timedelta(seconds=RATE_LIMIT_WINDOW)

        # Clean old entries from database
        try:
            supabase.table('rate_limit_requests').delete().lt(
                'timestamp', window_start.isoformat()
            ).execute()
        except Exception as e:
            logger.warning(f"Failed to clean old rate limit entries: {e}")

        # Count current requests for this employee
        try:
            result = supabase.table('rate_limit_requests').select('id').eq(
                'employee_id', employee_id
            ).gte('timestamp', window_start.isoformat()).execute()

            current_count = len(result.data)

            # Check if under limit
            if current_count >= RATE_LIMIT_REQUESTS:
                # Log rate limit hit for monitoring
                if workspace_id:
                    await log_audit_event(
                        employee_id=employee_id,
                        workspace_id=workspace_id,
                        event_type="rate_limit_exceeded",
                        event_details={
                            "current_count": current_count,
                            "limit": RATE_LIMIT_REQUESTS,
                            "window_seconds": RATE_LIMIT_WINDOW
                        },
                        success=False,
                        error_message="Rate limit exceeded"
                    )
                return False

            # Add current request to database
            rate_limit_record = {
                'employee_id': employee_id,
                'workspace_id': workspace_id,
                'timestamp': now.isoformat(),
                'endpoint': 'tool_execution',  # Could be made dynamic
                'ip_address': None  # Could be extracted from request
            }

            supabase.table('rate_limit_requests').insert(rate_limit_record).execute()
            return True

        except Exception as e:
            logger.error(f"Database rate limit check failed: {e}")
            # Fallback to in-memory for this request
            return await _fallback_memory_rate_limit(employee_id)

    except Exception as e:
        logger.error(f"Rate limit check error: {e}")
        return True  # Allow on error to prevent blocking

# Fallback in-memory rate limiting
_memory_rate_store = {}

async def _fallback_memory_rate_limit(employee_id: str) -> bool:
    """Fallback in-memory rate limiting when database is unavailable"""
    try:
        now = datetime.now(timezone.utc)
        window_start = now - timedelta(seconds=RATE_LIMIT_WINDOW)

        if employee_id not in _memory_rate_store:
            _memory_rate_store[employee_id] = []

        # Clean old entries
        _memory_rate_store[employee_id] = [
            timestamp for timestamp in _memory_rate_store[employee_id]
            if timestamp > window_start
        ]

        # Check limit
        if len(_memory_rate_store[employee_id]) >= RATE_LIMIT_REQUESTS:
            return False

        # Add current request
        _memory_rate_store[employee_id].append(now)
        return True

    except Exception as e:
        logger.error(f"Fallback rate limit error: {e}")
        return True

async def rate_limited_employee(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """Get employee with rate limiting applied"""
    employee = await get_current_employee(credentials)
    workspace_id = employee.get("workspace_id")

    if not await check_rate_limit(employee["id"], workspace_id):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded. Please try again later."
        )

    return employee

# ===========================================
# AUDIT LOGGING SYSTEM
# ===========================================

async def log_audit_event(
    employee_id: str,
    workspace_id: str,
    event_type: str,
    event_details: Dict[str, Any],
    success: bool = True,
    error_message: str = None
):
    """Log audit events for compliance and monitoring"""
    try:
        audit_record = {
            "employee_id": employee_id,
            "workspace_id": workspace_id,
            "event_type": event_type,
            "event_details": event_details,
            "success": success,
            "error_message": error_message,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "composio-service",
            "ip_address": None,  # Could be extracted from request
            "user_agent": None   # Could be extracted from request
        }

        # Store in audit_logs table (create if doesn't exist)
        supabase.table('audit_logs').insert(audit_record).execute()

        logger.info(f"Audit log created: {event_type} for employee {employee_id}")

    except Exception as e:
        logger.error(f"Failed to create audit log: {e}")
        # Don't raise exception to avoid breaking main functionality

# Utility functions
def encrypt_data(data: str) -> str:
    """Encrypt sensitive data"""
    return cipher_suite.encrypt(data.encode()).decode()

def decrypt_data(encrypted_data: str) -> str:
    """Decrypt sensitive data"""
    return cipher_suite.decrypt(encrypted_data.encode()).decode()

async def get_or_create_composio_entity(employee_id: str, employee_email: str, workspace_id: str) -> str:
    """Get or create Composio entity for employee with workspace isolation"""
    try:
        # Check if employee already has a Composio entity
        result = supabase.table('employees').select('composio_entity_id').eq('id', employee_id).single().execute()

        if result.data and result.data.get('composio_entity_id'):
            return result.data['composio_entity_id']

        # Create workspace-isolated Composio entity
        # Include workspace ID for proper multi-tenant isolation
        entity_id = f"uru_ws_{workspace_id}_emp_{employee_id}"

        # Update employee record with entity ID
        supabase.table('employees').update({
            'composio_entity_id': entity_id,
            'composio_entity_created_at': datetime.now(timezone.utc).isoformat()
        }).eq('id', employee_id).execute()

        # Log audit event
        await log_audit_event(
            employee_id=employee_id,
            workspace_id=workspace_id,
            event_type="composio_entity_created",
            event_details={
                "entity_id": entity_id,
                "employee_email": employee_email
            }
        )

        logger.info(f"Created workspace-isolated Composio entity {entity_id} for employee {employee_id}")
        return entity_id

    except Exception as e:
        logger.error(f"Error managing Composio entity: {e}")
        await log_audit_event(
            employee_id=employee_id,
            workspace_id=workspace_id,
            event_type="composio_entity_creation_failed",
            event_details={"error": str(e)},
            success=False,
            error_message=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Entity management failed"
        )

async def refresh_oauth_token(employee_id: str, app_name: str, connection: Dict[str, Any], workspace_id: str = None) -> bool:
    """Refresh OAuth token for a connection with enhanced error handling and audit logging"""
    try:
        if not connection.get("refresh_token"):
            logger.warning(f"No refresh token available for {app_name} connection")
            if workspace_id:
                await log_audit_event(
                    employee_id=employee_id,
                    workspace_id=workspace_id,
                    event_type="token_refresh_failed",
                    event_details={"app_name": app_name, "reason": "no_refresh_token"},
                    success=False,
                    error_message="No refresh token available"
                )
            return False

        # Log refresh attempt
        if workspace_id:
            await log_audit_event(
                employee_id=employee_id,
                workspace_id=workspace_id,
                event_type="token_refresh_started",
                event_details={"app_name": app_name}
            )

        refresh_token = decrypt_data(connection["refresh_token"])

        token_data = {
            "client_id": GOOGLE_CLIENT_ID,
            "client_secret": GOOGLE_CLIENT_SECRET,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }

        response = await http_client.post(
            "https://oauth2.googleapis.com/token",
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if response.status_code != 200:
            error_msg = f"Token refresh failed for {app_name}: {response.text}"
            logger.error(error_msg)

            # Mark connection as expired in oauth_tokens table
            provider_name = f"composio_{app_name}"
            supabase.table('oauth_tokens').update({
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": {"status": "expired", "refresh_failed_at": datetime.now(timezone.utc).isoformat()}
            }).eq("employee_id", employee_id).eq("provider", provider_name).execute()

            # Log failure
            if workspace_id:
                await log_audit_event(
                    employee_id=employee_id,
                    workspace_id=workspace_id,
                    event_type="token_refresh_failed",
                    event_details={
                        "app_name": app_name,
                        "status_code": response.status_code,
                        "error_response": response.text
                    },
                    success=False,
                    error_message=error_msg
                )
            return False

        tokens = response.json()
        access_token = tokens.get("access_token")
        expires_in = tokens.get("expires_in", 3600)
        new_refresh_token = tokens.get("refresh_token", refresh_token)

        # Update tokens in oauth_tokens table
        provider_name = f"composio_{app_name}"
        supabase.table('oauth_tokens').update({
            "access_token": encrypt_data(access_token),
            "refresh_token": encrypt_data(new_refresh_token),
            "expires_at": (datetime.now(timezone.utc) + timedelta(seconds=expires_in)).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }).eq("employee_id", employee_id).eq("provider", provider_name).execute()

        # Log successful refresh
        if workspace_id:
            await log_audit_event(
                employee_id=employee_id,
                workspace_id=workspace_id,
                event_type="token_refresh_completed",
                event_details={
                    "app_name": app_name,
                    "expires_in": expires_in,
                    "new_expiry": (datetime.now(timezone.utc) + timedelta(seconds=expires_in)).isoformat()
                }
            )

        logger.info(f"Successfully refreshed token for {app_name}")
        return True

    except Exception as e:
        error_msg = f"Error refreshing token for {app_name}: {e}"
        logger.error(error_msg)
        if workspace_id:
            await log_audit_event(
                employee_id=employee_id,
                workspace_id=workspace_id,
                event_type="token_refresh_error",
                event_details={"app_name": app_name, "error": str(e)},
                success=False,
                error_message=error_msg
            )
        return False

async def get_or_create_auth_config(app_name: str) -> str:
    """Get or create Composio auth config (integration) for an app"""
    try:
        # Check if we already have an auth config for this app
        # We'll store auth config IDs in a simple cache or database
        # For now, let's create one each time (Composio allows multiple configs)

        auth_config_data = {
            "name": f"Uru {app_name.title()} Integration",
            "appName": app_name,
            "authMode": "OAUTH2",
            "useComposioAuth": True  # Use Composio's managed OAuth for simplicity
        }

        response = await http_client.post(
            f"{COMPOSIO_BASE_URL}/v1/integrations",
            headers={"X-API-Key": COMPOSIO_API_KEY, "Content-Type": "application/json"},
            json=auth_config_data
        )

        if response.status_code not in [200, 201]:
            logger.error(f"Failed to create auth config for {app_name}: {response.text}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create auth config for {app_name}"
            )

        result = response.json()
        auth_config_id = result.get("id")

        logger.info(f"Created auth config for {app_name}: {auth_config_id}")
        return auth_config_id

    except Exception as e:
        logger.error(f"Error creating auth config for {app_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create auth config for {app_name}"
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "uru-composio-bridge",
        "version": "1.0.0",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "composio_api": "connected" if COMPOSIO_API_KEY else "not_configured",
        "database": "connected"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with service information"""
    return {
        "service": "Uru Composio Bridge",
        "description": "White-labeled MCP tool integration",
        "version": "1.0.0",
        "endpoints": {
            "health": "GET /health",
            "entities": "POST /api/uru/composio/entities",
            "connect": "POST /api/uru/composio/connect/{app_name}",
            "execute": "POST /api/uru/composio/execute",
            "webhook": "POST /api/uru/composio/webhook"
        }
    }

# ===========================================
# METRICS COLLECTION AND MONITORING
# ===========================================

class MetricsCollector:
    """Collect application metrics for monitoring"""

    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.response_times = []
        self.active_connections = 0
        self.oauth_operations = {
            'successful': 0,
            'failed': 0,
            'token_refreshes': 0
        }
        self.tool_executions = {
            'successful': 0,
            'failed': 0,
            'total_time': 0
        }
        self.rate_limit_hits = 0
        self.workspace_activity = {}

    def record_request(self, method: str, path: str, status_code: int, response_time: float):
        """Record request metrics"""
        self.request_count += 1
        self.response_times.append(response_time)

        # Keep only last 1000 response times
        if len(self.response_times) > 1000:
            self.response_times = self.response_times[-1000:]

        if status_code >= 400:
            self.error_count += 1

    def record_oauth_operation(self, operation: str, success: bool, workspace_id: str = None):
        """Record OAuth operation metrics"""
        if success:
            self.oauth_operations['successful'] += 1
        else:
            self.oauth_operations['failed'] += 1

        if operation == 'token_refresh':
            self.oauth_operations['token_refreshes'] += 1

        if workspace_id:
            if workspace_id not in self.workspace_activity:
                self.workspace_activity[workspace_id] = {'oauth_ops': 0, 'tool_execs': 0}
            self.workspace_activity[workspace_id]['oauth_ops'] += 1

    def record_tool_execution(self, success: bool, execution_time: float, workspace_id: str = None):
        """Record tool execution metrics"""
        if success:
            self.tool_executions['successful'] += 1
        else:
            self.tool_executions['failed'] += 1

        self.tool_executions['total_time'] += execution_time

        if workspace_id:
            if workspace_id not in self.workspace_activity:
                self.workspace_activity[workspace_id] = {'oauth_ops': 0, 'tool_execs': 0}
            self.workspace_activity[workspace_id]['tool_execs'] += 1

    def record_rate_limit_hit(self):
        """Record rate limit hit"""
        self.rate_limit_hits += 1

    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics snapshot"""
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0

        # System metrics (optional - requires psutil)
        try:
            import psutil
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
        except ImportError:
            cpu_percent = 0
            memory = type('obj', (object,), {'percent': 0, 'available': 0})()
            disk = type('obj', (object,), {'percent': 0, 'free': 0})()

        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'requests': {
                'total': self.request_count,
                'errors': self.error_count,
                'error_rate': self.error_count / max(self.request_count, 1),
                'avg_response_time': avg_response_time,
                'rate_limit_hits': self.rate_limit_hits
            },
            'oauth': self.oauth_operations,
            'tool_executions': self.tool_executions,
            'workspace_activity': self.workspace_activity,
            'system': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3) if hasattr(memory, 'available') else 0,
                'disk_percent': disk.percent if hasattr(disk, 'percent') else 0,
                'disk_free_gb': disk.free / (1024**3) if hasattr(disk, 'free') else 0
            },
            'active_connections': self.active_connections
        }

# Global metrics collector
metrics_collector = MetricsCollector()

# ===========================================
# HEALTH CHECK HELPER FUNCTIONS
# ===========================================

async def _check_database_health() -> Dict[str, Any]:
    """Check database connectivity and performance"""
    try:
        start_time = datetime.now(timezone.utc)

        # Test basic connectivity
        result = supabase.table('employees').select('id').limit(1).execute()

        # Test write capability (audit log)
        test_log = {
            'employee_id': '00000000-0000-0000-0000-000000000000',
            'workspace_id': '00000000-0000-0000-0000-000000000000',
            'event_type': 'health_check',
            'event_details': {'test': True},
            'service': 'composio-service',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        supabase.table('audit_logs').insert(test_log).execute()

        response_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

        return {
            "healthy": True,
            "response_time_ms": round(response_time, 2),
            "read_test": "passed",
            "write_test": "passed"
        }

    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "read_test": "failed",
            "write_test": "failed"
        }

async def _check_composio_api_health() -> Dict[str, Any]:
    """Check Composio API connectivity"""
    try:
        if not COMPOSIO_API_KEY or COMPOSIO_API_KEY == "YOUR_ACTUAL_COMPOSIO_API_KEY_HERE":
            return {
                "healthy": False,
                "error": "API key not configured",
                "configured": False
            }

        start_time = datetime.now(timezone.utc)

        response = await http_client.get(
            f"{COMPOSIO_BASE_URL}/v1/apps",
            headers={"x-api-key": COMPOSIO_API_KEY},
            timeout=5.0
        )

        response_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

        return {
            "healthy": response.status_code == 200,
            "status_code": response.status_code,
            "response_time_ms": round(response_time, 2),
            "configured": True
        }

    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "configured": bool(COMPOSIO_API_KEY and COMPOSIO_API_KEY != "YOUR_ACTUAL_COMPOSIO_API_KEY_HERE")
        }

async def _check_oauth_token_health() -> Dict[str, Any]:
    """Check OAuth token health across workspaces"""
    try:
        # Get token expiration stats
        result = supabase.table('oauth_tokens').select(
            'expires_at, provider, employee_id'
        ).execute()

        now = datetime.now(timezone.utc)
        total_tokens = len(result.data)
        expired_tokens = 0
        expiring_soon = 0  # Within 24 hours

        for token in result.data:
            if token.get('expires_at'):
                try:
                    expires_at = datetime.fromisoformat(token['expires_at'].replace('Z', '+00:00'))
                    if expires_at < now:
                        expired_tokens += 1
                    elif expires_at < now + timedelta(hours=24):
                        expiring_soon += 1
                except:
                    expired_tokens += 1  # Treat parse errors as expired

        health_percentage = (total_tokens - expired_tokens) / max(total_tokens, 1) * 100
        healthy = expired_tokens <= total_tokens * 0.1  # Less than 10% expired

        return {
            "healthy": healthy,
            "total_tokens": total_tokens,
            "expired_tokens": expired_tokens,
            "expiring_soon": expiring_soon,
            "health_percentage": round(health_percentage, 2)
        }

    except Exception as e:
        return {
            "healthy": False,
            "error": str(e)
        }

async def _check_auth_service_health() -> Dict[str, Any]:
    """Check auth service connectivity"""
    try:
        start_time = datetime.now(timezone.utc)

        response = await http_client.get(
            f"{AUTH_SERVICE_URL}/health",
            timeout=5.0
        )

        response_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

        return {
            "healthy": response.status_code == 200,
            "status_code": response.status_code,
            "response_time_ms": round(response_time, 2)
        }

    except Exception as e:
        return {
            "healthy": False,
            "error": str(e)
        }

async def _check_rate_limit_health() -> Dict[str, Any]:
    """Check rate limiting system health"""
    try:
        # Test rate limit table accessibility
        result = supabase.table('rate_limit_requests').select('id').limit(1).execute()

        # Get recent rate limit activity
        recent_time = datetime.now(timezone.utc) - timedelta(hours=1)
        recent_requests = supabase.table('rate_limit_requests').select('id').gte(
            'timestamp', recent_time.isoformat()
        ).execute()

        return {
            "healthy": True,
            "table_accessible": True,
            "recent_requests": len(recent_requests.data)
        }

    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "table_accessible": False
        }

# ===========================================
# HEALTH CHECK AND MONITORING ENDPOINTS
# ===========================================

@app.get("/health")
async def health_check():
    """Enhanced health check endpoint with comprehensive dependency validation"""
    start_time = datetime.now(timezone.utc)

    # Database health check
    db_status = await _check_database_health()

    # Composio API health check
    composio_status = await _check_composio_api_health()

    # OAuth token health check
    oauth_status = await _check_oauth_token_health()

    # Auth service connectivity check
    auth_service_status = await _check_auth_service_health()

    # Rate limiting system health check
    rate_limit_status = await _check_rate_limit_health()

    # Overall health determination
    critical_checks = [db_status["healthy"], composio_status["healthy"]]
    important_checks = [oauth_status["healthy"], auth_service_status["healthy"]]

    overall_healthy = all(critical_checks) and any(important_checks)

    health_status = {
        "status": "healthy" if overall_healthy else "unhealthy",
        "service": "composio-service",
        "version": "1.0.0",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "response_time_ms": (datetime.now(timezone.utc) - start_time).total_seconds() * 1000,
        "checks": {
            "database": db_status,
            "composio_api": composio_status,
            "oauth_tokens": oauth_status,
            "auth_service": auth_service_status,
            "rate_limiting": rate_limit_status,
            "encryption": {
                "healthy": bool(cipher_suite),
                "configured": bool(ENCRYPTION_KEY)
            }
        },
        "environment": {
            "composio_api_configured": COMPOSIO_API_KEY != "YOUR_ACTUAL_COMPOSIO_API_KEY_HERE",
            "google_oauth_configured": bool(GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET),
            "encryption_configured": bool(ENCRYPTION_KEY),
            "cors_origins": len(CORS_ORIGINS) if CORS_ORIGINS else 0
        },
        "summary": {
            "critical_systems": "healthy" if all(critical_checks) else "unhealthy",
            "important_systems": "healthy" if all(important_checks) else "degraded",
            "total_checks": len(critical_checks) + len(important_checks),
            "passing_checks": sum(critical_checks + important_checks)
        }
    }

    status_code = 200 if overall_healthy else 503
    return JSONResponse(content=health_status, status_code=status_code)

@app.get("/health/oauth-status")
async def oauth_status_check(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Check OAuth connection status for current employee"""
    try:
        workspace_id = employee.get("workspace_id")
        employee_id = employee["id"]

        # Get all OAuth connections for this employee
        connections = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee_id
        ).execute()

        oauth_status = {
            "employee_id": employee_id,
            "workspace_id": workspace_id,
            "total_connections": len(connections.data),
            "connections": [],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        for connection in connections.data:
            provider = connection["provider"]
            expires_at = connection.get("expires_at")

            # Check if token is expired
            is_expired = False
            if expires_at:
                try:
                    expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                    is_expired = datetime.now(timezone.utc) > expiry_time
                except:
                    is_expired = True

            connection_status = {
                "provider": provider,
                "connected": True,
                "expired": is_expired,
                "expires_at": expires_at,
                "scopes": connection.get("scopes", []),
                "last_updated": connection.get("updated_at")
            }

            oauth_status["connections"].append(connection_status)

        return oauth_status

    except Exception as e:
        logger.error(f"OAuth status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check OAuth status"
        )

# ===========================================
# MONITORING AND METRICS ENDPOINTS
# ===========================================

@app.get("/metrics")
async def get_metrics(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get application metrics for monitoring (admin only)"""
    try:
        # Validate admin access - only workspace admins can access metrics
        workspace_id = employee.get("workspace_id")
        employee_role = employee.get("role", "").lower()

        if employee_role not in ["admin", "owner"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: admin privileges required"
            )

        metrics_data = metrics_collector.get_metrics()

        # Add database-specific metrics
        try:
            # Get recent audit log activity (workspace-scoped)
            recent_time = datetime.now(timezone.utc) - timedelta(hours=1)
            audit_count = supabase.table('audit_logs').select('id').eq(
                'workspace_id', workspace_id
            ).gte('timestamp', recent_time.isoformat()).execute()

            # Get OAuth token health (workspace-scoped)
            # Get employee IDs from current workspace
            workspace_employees = supabase.table('employees').select('id').eq(
                'workspace_id', workspace_id
            ).execute()

            employee_ids = [emp['id'] for emp in workspace_employees.data]

            if employee_ids:
                oauth_tokens = supabase.table('oauth_tokens').select('expires_at, provider').in_(
                    'employee_id', employee_ids
                ).execute()
            else:
                oauth_tokens = type('obj', (object,), {'data': []})()  # Empty result

            expired_count = 0
            for token in oauth_tokens.data:
                if token.get('expires_at'):
                    try:
                        expires_at = datetime.fromisoformat(token['expires_at'].replace('Z', '+00:00'))
                        if expires_at < datetime.now(timezone.utc):
                            expired_count += 1
                    except:
                        expired_count += 1

            metrics_data['database'] = {
                'recent_audit_logs': len(audit_count.data),
                'total_oauth_tokens': len(oauth_tokens.data),
                'expired_oauth_tokens': expired_count
            }

        except Exception as e:
            logger.warning(f"Failed to collect database metrics: {e}")
            metrics_data['database'] = {'error': str(e)}

        return metrics_data

    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to collect metrics"
        )

@app.get("/alerts")
async def get_alerts(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get current system alerts (admin only)"""
    try:
        # Validate admin access
        employee_role = employee.get("role", "").lower()
        if employee_role not in ["admin", "owner"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: admin privileges required"
            )
        # Get current metrics
        metrics_data = metrics_collector.get_metrics()

        # Simple alert manager implementation
        alerts = []

        # Error rate alert
        error_rate = metrics_data['requests']['error_rate']
        if error_rate > 0.05:  # 5% error rate
            alerts.append({
                'type': 'error_rate',
                'severity': 'critical' if error_rate > 0.1 else 'warning',
                'message': f"High error rate: {error_rate:.2%}",
                'value': error_rate,
                'threshold': 0.05,
                'timestamp': datetime.now(timezone.utc).isoformat()
            })

        # Response time alert
        avg_response_time = metrics_data['requests']['avg_response_time']
        if avg_response_time > 2.0:  # 2 seconds
            alerts.append({
                'type': 'response_time',
                'severity': 'warning',
                'message': f"High response time: {avg_response_time:.2f}s",
                'value': avg_response_time,
                'threshold': 2.0,
                'timestamp': datetime.now(timezone.utc).isoformat()
            })

        # OAuth failure rate alert
        oauth = metrics_data['oauth']
        total_oauth = oauth['successful'] + oauth['failed']
        if total_oauth > 0:
            oauth_failure_rate = oauth['failed'] / total_oauth
            if oauth_failure_rate > 0.1:  # 10% failure rate
                alerts.append({
                    'type': 'oauth_failure_rate',
                    'severity': 'critical',
                    'message': f"High OAuth failure rate: {oauth_failure_rate:.2%}",
                    'value': oauth_failure_rate,
                    'threshold': 0.1,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })

        # Rate limiting alert
        if metrics_data['requests']['rate_limit_hits'] > 10:
            alerts.append({
                'type': 'rate_limiting',
                'severity': 'warning',
                'message': f"High rate limit hits: {metrics_data['requests']['rate_limit_hits']}",
                'value': metrics_data['requests']['rate_limit_hits'],
                'threshold': 10,
                'timestamp': datetime.now(timezone.utc).isoformat()
            })

        return {
            'alerts': alerts,
            'alert_count': len(alerts),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Alert collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to collect alerts"
        )

@app.get("/workspace-activity")
async def get_workspace_activity(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get workspace-specific activity metrics"""
    try:
        workspace_id = employee.get("workspace_id")
        if not workspace_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Workspace ID not found"
            )

        # Get recent activity from audit logs
        recent_time = datetime.now(timezone.utc) - timedelta(hours=24)

        audit_logs = supabase.table('audit_logs').select(
            'event_type, success, timestamp'
        ).eq('workspace_id', workspace_id).gte(
            'timestamp', recent_time.isoformat()
        ).execute()

        # Aggregate activity data
        activity_summary = {
            'total_events': len(audit_logs.data),
            'successful_events': sum(1 for log in audit_logs.data if log.get('success', True)),
            'failed_events': sum(1 for log in audit_logs.data if not log.get('success', True)),
            'event_types': {},
            'hourly_activity': {}
        }

        # Count by event type
        for log in audit_logs.data:
            event_type = log.get('event_type', 'unknown')
            activity_summary['event_types'][event_type] = activity_summary['event_types'].get(event_type, 0) + 1

            # Hourly breakdown
            try:
                timestamp = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
                hour_key = timestamp.strftime('%Y-%m-%d %H:00')
                activity_summary['hourly_activity'][hour_key] = activity_summary['hourly_activity'].get(hour_key, 0) + 1
            except:
                pass

        # Get OAuth token status for this workspace
        oauth_tokens = supabase.table('oauth_tokens').select(
            'provider, expires_at, created_at'
        ).in_('employee_id',
            supabase.table('employees').select('id').eq('workspace_id', workspace_id).execute().data
        ).execute()

        activity_summary['oauth_tokens'] = {
            'total': len(oauth_tokens.data),
            'by_provider': {}
        }

        for token in oauth_tokens.data:
            provider = token.get('provider', 'unknown')
            activity_summary['oauth_tokens']['by_provider'][provider] = \
                activity_summary['oauth_tokens']['by_provider'].get(provider, 0) + 1

        return {
            'workspace_id': workspace_id,
            'activity_summary': activity_summary,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Workspace activity collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to collect workspace activity"
        )

@app.get("/database-performance")
async def get_database_performance(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get database performance metrics and optimization status (admin only)"""
    try:
        # Validate admin access
        employee_role = employee.get("role", "").lower()
        if employee_role not in ["admin", "owner"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: admin privileges required"
            )

        workspace_id = employee.get("workspace_id")

        performance_data = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'connection_test': {},
            'table_stats': {},
            'index_usage': {},
            'query_performance': {}
        }

        # Test database connection performance
        try:
            start_time = datetime.now(timezone.utc)
            supabase.table('employees').select('id').limit(1).execute()
            connection_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

            performance_data['connection_test'] = {
                'response_time_ms': round(connection_time, 2),
                'status': 'healthy' if connection_time < 100 else 'slow'
            }
        except Exception as e:
            performance_data['connection_test'] = {
                'status': 'failed',
                'error': str(e)
            }

        # Get table statistics
        try:
            # Test query performance on key tables
            tables_to_test = ['audit_logs', 'oauth_tokens', 'employees', 'workspaces']

            for table in tables_to_test:
                start_time = datetime.now(timezone.utc)
                result = supabase.table(table).select('id').limit(10).execute()
                query_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

                performance_data['table_stats'][table] = {
                    'query_time_ms': round(query_time, 2),
                    'record_count': len(result.data),
                    'status': 'optimal' if query_time < 50 else 'needs_optimization'
                }
        except Exception as e:
            performance_data['table_stats']['error'] = str(e)

        # Test index usage with common queries
        try:
            # Test audit logs query performance (should use indexes)
            start_time = datetime.now(timezone.utc)
            recent_time = datetime.now(timezone.utc) - timedelta(hours=24)

            audit_result = supabase.table('audit_logs').select('id').gte(
                'timestamp', recent_time.isoformat()
            ).limit(100).execute()

            audit_query_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

            performance_data['index_usage']['audit_logs_recent'] = {
                'query_time_ms': round(audit_query_time, 2),
                'records_found': len(audit_result.data),
                'index_effective': audit_query_time < 100
            }

            # Test OAuth tokens query performance
            start_time = datetime.now(timezone.utc)
            oauth_result = supabase.table('oauth_tokens').select('id, expires_at').limit(50).execute()
            oauth_query_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

            performance_data['index_usage']['oauth_tokens'] = {
                'query_time_ms': round(oauth_query_time, 2),
                'records_found': len(oauth_result.data),
                'index_effective': oauth_query_time < 50
            }

        except Exception as e:
            performance_data['index_usage']['error'] = str(e)

        # Overall performance assessment
        avg_query_time = 0
        query_count = 0

        for table_stats in performance_data['table_stats'].values():
            if isinstance(table_stats, dict) and 'query_time_ms' in table_stats:
                avg_query_time += table_stats['query_time_ms']
                query_count += 1

        if query_count > 0:
            avg_query_time = avg_query_time / query_count

        performance_data['summary'] = {
            'overall_status': 'optimal' if avg_query_time < 50 else 'needs_optimization',
            'average_query_time_ms': round(avg_query_time, 2),
            'connection_healthy': performance_data['connection_test'].get('status') == 'healthy',
            'recommendations': []
        }

        # Add recommendations based on performance
        if avg_query_time > 100:
            performance_data['summary']['recommendations'].append(
                "Consider running database maintenance and updating statistics"
            )

        if performance_data['connection_test'].get('response_time_ms', 0) > 200:
            performance_data['summary']['recommendations'].append(
                "Database connection is slow - check network and server resources"
            )

        return performance_data

    except Exception as e:
        logger.error(f"Database performance check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check database performance"
        )

# ===========================================
# COMPOSIO BRIDGE API ENDPOINTS
# ===========================================

@app.post("/api/uru/composio/entities")
async def create_or_get_entity(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Create or retrieve Composio entity for employee (white-labeled)"""
    try:
        entity_id = await get_or_create_composio_entity(
            employee["id"],
            employee["email"]
        )

        return {
            "success": True,
            "entity_id": entity_id,
            "message": "Entity ready for app connections"
        }

    except Exception as e:
        logger.error(f"Entity creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to prepare workspace for app connections"
        )

@app.post("/api/uru/composio/connect/{app_name}")
async def connect_app(
    app_name: str,
    request: ComposioConnectRequest,
    employee: Dict[str, Any] = Depends(rate_limited_employee)
):
    """Initiate app connection via our own OAuth flow (fully white-labeled)"""
    try:
        # Validate workspace access
        if not await validate_workspace_access(employee):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient workspace permissions"
            )

        workspace_id = employee.get("workspace_id")
        employee_id = employee["id"]

        # Validate and sanitize input parameters
        try:
            # Validate app name
            app_name_clean = InputValidator.sanitize_string(app_name, 50)
            if app_name_clean.lower() not in GOOGLE_SCOPES:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported app: {app_name_clean}"
                )

            # Validate redirect URL if provided
            if request.redirect_url:
                redirect_url_clean = InputValidator.sanitize_string(request.redirect_url, 500)
            else:
                redirect_url_clean = None

            # Validate metadata if provided
            if request.metadata:
                metadata_clean = InputValidator.validate_tool_parameters(request.metadata)
            else:
                metadata_clean = {}

        except ValueError as e:
            await log_audit_event(
                employee_id=employee_id,
                workspace_id=workspace_id,
                event_type="oauth_connection_validation_failed",
                event_details={
                    "app_name": app_name,
                    "validation_error": str(e)
                },
                success=False,
                error_message=str(e)
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid input: {str(e)}"
            )

        # Check if already connected using oauth_tokens table
        provider_name = f"composio_{app_name_clean.lower()}"
        existing_token = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if existing_token.data:
            return {
                "success": True,
                "app_name": app_name_clean,
                "already_connected": True,
                "message": f"Your {app_name_clean.title()} account is already connected"
            }

        # Generate OAuth state for security
        oauth_state = secrets.token_urlsafe(32)

        # Store OAuth state in database
        oauth_record = {
            "employee_id": employee["id"],
            "provider": app_name_clean.lower(),
            "state_token": oauth_state,
            "redirect_url": redirect_url_clean or f"{FRONTEND_URL}/app/settings",
            "requested_scopes": GOOGLE_SCOPES[app_name_clean.lower()],
            "expires_at": (datetime.now(timezone.utc) + timedelta(minutes=10)).isoformat()
        }

        supabase.table('oauth_states').upsert(
            oauth_record,
            on_conflict="employee_id,provider"
        ).execute()

        # Build Google OAuth URL
        scopes = " ".join(GOOGLE_SCOPES[app_name.lower()])
        oauth_params = {
            "client_id": GOOGLE_CLIENT_ID,
            "redirect_uri": f"{COMPOSIO_SERVICE_URL}/api/uru/composio/oauth/callback/{app_name}",
            "scope": scopes,
            "response_type": "code",
            "state": oauth_state,
            "access_type": "offline",
            "prompt": "consent"
        }

        authorization_url = "https://accounts.google.com/o/oauth2/v2/auth?" + urllib.parse.urlencode(oauth_params)

        logger.info(f"Generated OAuth URL for {app_name} for employee {employee['id']}")

        return {
            "success": True,
            "app_name": app_name,
            "authorization_url": authorization_url,
            "state": oauth_state,
            "message": f"Visit the authorization URL to connect your {app_name.title()} account"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"App connection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect app"
        )

@app.get("/api/uru/composio/oauth/callback/{app_name}")
async def oauth_callback(
    app_name: str,
    code: str,
    state: str,
    error: Optional[str] = None
):
    """Handle OAuth callback from Google (fully white-labeled)"""
    try:
        if error:
            logger.error(f"OAuth error for {app_name}: {error}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=oauth_denied&app={app_name}",
                status_code=302
            )

        # Verify OAuth state
        oauth_state_result = supabase.table('oauth_states').select("*").eq(
            "state_token", state
        ).eq("provider", app_name.lower()).single().execute()

        if not oauth_state_result.data:
            logger.error(f"Invalid OAuth state: {state}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=invalid_state&app={app_name}",
                status_code=302
            )

        oauth_state_data = oauth_state_result.data
        employee_id = oauth_state_data["employee_id"]
        redirect_url = oauth_state_data["redirect_url"]

        # Exchange code for tokens
        token_data = {
            "client_id": GOOGLE_CLIENT_ID,
            "client_secret": GOOGLE_CLIENT_SECRET,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": f"{COMPOSIO_SERVICE_URL}/api/uru/composio/oauth/callback/{app_name}"
        }

        token_response = await http_client.post(
            "https://oauth2.googleapis.com/token",
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if token_response.status_code != 200:
            logger.error(f"Token exchange failed: {token_response.text}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=token_exchange_failed&app={app_name}",
                status_code=302
            )

        tokens = token_response.json()
        access_token = tokens.get("access_token")
        refresh_token = tokens.get("refresh_token")
        expires_in = tokens.get("expires_in", 3600)

        if not access_token:
            logger.error("No access token received")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=no_access_token&app={app_name}",
                status_code=302
            )

        # Get user info from Google
        user_info_response = await http_client.get(
            "https://www.googleapis.com/oauth2/v2/userinfo",
            headers={"Authorization": f"Bearer {access_token}"}
        )

        user_info = {}
        if user_info_response.status_code == 200:
            user_info = user_info_response.json()

        # Store tokens in oauth_tokens table (standardized approach)
        provider_name = f"composio_{app_name.lower()}"
        oauth_record = {
            "employee_id": employee_id,
            "provider": provider_name,
            "access_token": encrypt_data(access_token),
            "refresh_token": encrypt_data(refresh_token) if refresh_token else None,
            "expires_at": (datetime.now(timezone.utc) + timedelta(seconds=expires_in)).isoformat(),
            "scopes": GOOGLE_SCOPES[app_name.lower()],
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "metadata": {
                "app_name": app_name.lower(),
                "connected_via": "uru_oauth",
                "google_user_id": user_info.get("id"),
                "google_email": user_info.get("email"),
                "user_info": user_info,
                "connected_at": datetime.now(timezone.utc).isoformat()
            }
        }

        # Upsert oauth token record
        supabase.table('oauth_tokens').upsert(
            oauth_record,
            on_conflict="employee_id,provider"
        ).execute()

        # Clean up OAuth state
        supabase.table('oauth_states').delete().eq("state_token", state).execute()

        logger.info(f"Successfully connected {app_name} for employee {employee_id}")

        # Redirect back to frontend with success
        return RedirectResponse(
            url=f"{redirect_url}?success=true&app={app_name}&connected=true",
            status_code=302
        )

    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        return RedirectResponse(
            url=f"{FRONTEND_URL}/app/settings?error=callback_failed&app={app_name}",
            status_code=302
        )

@app.post("/api/uru/composio/execute")
async def execute_tool(
    request: ComposioExecuteRequest,
    employee: Dict[str, Any] = Depends(rate_limited_employee)
):
    """Execute MCP tool using our stored OAuth tokens (fully white-labeled)"""
    try:
        # Validate workspace access
        if not await validate_workspace_access(employee):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient workspace permissions"
            )

        workspace_id = employee.get("workspace_id")
        employee_id = employee["id"]

        # Validate and sanitize input parameters
        try:
            # Validate tool name
            tool_name = InputValidator.sanitize_string(request.tool_name, 100)

            # Validate and sanitize tool parameters
            sanitized_parameters = InputValidator.validate_tool_parameters(request.parameters)

        except ValueError as e:
            await log_audit_event(
                employee_id=employee_id,
                workspace_id=workspace_id,
                event_type="tool_execution_validation_failed",
                event_details={
                    "tool_name": request.tool_name,
                    "validation_error": str(e)
                },
                success=False,
                error_message=str(e)
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid input: {str(e)}"
            )
        # Determine which app this tool belongs to
        tool_app_mapping = {
            "send_email": "gmail",
            "list_emails": "gmail",
            "read_email": "gmail",
            "gdrive_upload": "drive",
            "gdrive_download": "drive",
            "gdrive_list": "drive",
            "list_events": "calendar",
            "create_event": "calendar",
            "update_event": "calendar"
        }

        required_app = tool_app_mapping.get(request.tool_name)
        if not required_app:
            # For tools that don't require specific app auth, use Composio directly
            entity_id = employee.get("composio_entity_id")
            if not entity_id:
                entity_id = await get_or_create_composio_entity(employee_id, employee["email"], workspace_id)

            # Log audit event for tool execution
            await log_audit_event(
                employee_id=employee_id,
                workspace_id=workspace_id,
                event_type="tool_execution_started",
                event_details={
                    "tool_name": tool_name,
                    "parameters": sanitized_parameters,
                    "entity_id": entity_id,
                    "requires_oauth": False
                }
            )

            execution_data = {
                "user_id": entity_id,
                "tool_name": tool_name,
                "parameters": sanitized_parameters
            }

            response = await http_client.post(
                f"{COMPOSIO_BASE_URL}/v3/tools/execute",
                headers={"x-api-key": COMPOSIO_API_KEY, "Content-Type": "application/json"},
                json=execution_data
            )

            if response.status_code != 200:
                error_msg = f"Tool execution failed: {response.text}"
                logger.error(error_msg)

                # Log audit event for failure
                await log_audit_event(
                    employee_id=employee_id,
                    workspace_id=workspace_id,
                    event_type="tool_execution_failed",
                    event_details={
                        "tool_name": request.tool_name,
                        "error_response": response.text,
                        "status_code": response.status_code
                    },
                    success=False,
                    error_message=error_msg
                )

                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Tool execution failed"
                )

            execution_result = response.json()

            # Log successful execution
            await log_audit_event(
                employee_id=employee_id,
                workspace_id=workspace_id,
                event_type="tool_execution_completed",
                event_details={
                    "tool_name": request.tool_name,
                    "execution_time": execution_result.get("executionTime"),
                    "result_size": len(str(execution_result.get("result", {})))
                }
            )

            return {
                "success": True,
                "tool_name": request.tool_name,
                "result": execution_result.get("result", {}),
                "execution_time": execution_result.get("executionTime"),
                "message": "Tool executed successfully"
            }

        # Get the connection for the required app from oauth_tokens table
        provider_name = f"composio_{required_app}"
        connection_result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).single().execute()

        if not connection_result.data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No active {required_app} connection found. Please connect your {required_app.title()} account first."
            )

        connection = connection_result.data

        # Check if token needs refresh
        expires_at = datetime.fromisoformat(connection["expires_at"].replace('Z', '+00:00'))
        if expires_at <= datetime.now(timezone.utc) + timedelta(minutes=5):
            # Refresh token if needed
            await refresh_oauth_token(employee["id"], required_app, connection)
            # Re-fetch updated connection
            connection_result = supabase.table('oauth_tokens').select("*").eq(
                "employee_id", employee["id"]
            ).eq("provider", provider_name).single().execute()
            connection = connection_result.data

        # Decrypt access token
        access_token = decrypt_data(connection["access_token"])

        # Execute tool with our OAuth token via Composio
        entity_id = employee.get("composio_entity_id")
        if not entity_id:
            entity_id = await get_or_create_composio_entity(employee["id"], employee["email"])

        # Inject our OAuth token into the execution
        execution_data = {
            "user_id": entity_id,
            "tool_name": request.tool_name,
            "parameters": request.parameters,
            "auth_config": {
                "access_token": access_token,
                "token_type": "Bearer"
            }
        }

        response = await http_client.post(
            f"{COMPOSIO_BASE_URL}/v3/tools/execute",
            headers={"x-api-key": COMPOSIO_API_KEY, "Content-Type": "application/json"},
            json=execution_data
        )

        if response.status_code != 200:
            logger.error(f"Tool execution failed: {response.text}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Tool execution failed"
            )

        execution_result = response.json()

        # Return white-labeled response
        return {
            "success": True,
            "tool_name": request.tool_name,
            "result": execution_result.get("result", {}),
            "execution_time": execution_result.get("executionTime"),
            "message": "Tool executed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Tool execution error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Tool execution failed"
        )

@app.post("/api/uru/composio/webhook")
async def handle_webhook(request: ComposioWebhookRequest):
    """Handle Composio webhooks (white-labeled)"""
    try:
        logger.info(f"Received webhook: {request.event_type} for entity {request.entity_id}")

        # Extract employee ID from entity ID
        if not request.entity_id.startswith("uru_employee_"):
            logger.warning(f"Unknown entity format: {request.entity_id}")
            return {"success": True, "message": "Webhook processed"}

        employee_id = request.entity_id.replace("uru_employee_", "")

        # Handle different webhook events
        if request.event_type == "connection.created":
            await handle_connection_created(employee_id, request)
        elif request.event_type == "connection.deleted":
            await handle_connection_deleted(employee_id, request)
        elif request.event_type == "connection.updated":
            await handle_connection_updated(employee_id, request)
        else:
            logger.info(f"Unhandled webhook event: {request.event_type}")

        return {"success": True, "message": "Webhook processed"}

    except Exception as e:
        logger.error(f"Webhook processing failed: {e}")
        # Don't raise HTTP exceptions for webhooks - just log and return success
        return {"success": False, "error": str(e)}

async def handle_connection_created(employee_id: str, webhook_data: ComposioWebhookRequest):
    """Handle connection created webhook"""
    try:
        # Update oauth token metadata to reflect webhook status
        provider_name = f"composio_{webhook_data.app_name}"

        # Get existing token record
        existing = supabase.table('oauth_tokens').select("metadata").eq(
            "employee_id", employee_id
        ).eq("provider", provider_name).execute()

        if existing.data:
            current_metadata = existing.data[0].get("metadata", {})
            updated_metadata = {
                **current_metadata,
                **webhook_data.data,
                "webhook_received_at": datetime.now(timezone.utc).isoformat(),
                "composio_connection_id": webhook_data.connection_id,
                "status": "active"
            }

            supabase.table('oauth_tokens').update({
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": updated_metadata
            }).eq("employee_id", employee_id).eq("provider", provider_name).execute()

        logger.info(f"Connection activated for employee {employee_id}: {webhook_data.app_name}")

    except Exception as e:
        logger.error(f"Failed to handle connection created: {e}")

async def handle_connection_deleted(employee_id: str, webhook_data: ComposioWebhookRequest):
    """Handle connection deleted webhook"""
    try:
        # Delete connection from oauth_tokens table
        provider_name = f"composio_{webhook_data.app_name}"
        supabase.table('oauth_tokens').delete().eq(
            "employee_id", employee_id
        ).eq("provider", provider_name).execute()

        logger.info(f"Connection disconnected for employee {employee_id}: {webhook_data.app_name}")

    except Exception as e:
        logger.error(f"Failed to handle connection deleted: {e}")

async def handle_connection_updated(employee_id: str, webhook_data: ComposioWebhookRequest):
    """Handle connection updated webhook"""
    try:
        # Update oauth token metadata to reflect webhook status
        provider_name = f"composio_{webhook_data.app_name}"

        # Get existing token record
        existing = supabase.table('oauth_tokens').select("metadata").eq(
            "employee_id", employee_id
        ).eq("provider", provider_name).execute()

        if existing.data:
            current_metadata = existing.data[0].get("metadata", {})
            updated_metadata = {
                **current_metadata,
                **webhook_data.data,
                "webhook_received_at": datetime.now(timezone.utc).isoformat(),
                "composio_connection_id": webhook_data.connection_id
            }

            supabase.table('oauth_tokens').update({
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": updated_metadata
            }).eq("employee_id", employee_id).eq("provider", provider_name).execute()

        logger.info(f"Connection updated for employee {employee_id}: {webhook_data.app_name}")

    except Exception as e:
        logger.error(f"Failed to handle connection updated: {e}")

@app.get("/api/uru/composio/connections")
async def list_connections(employee: Dict[str, Any] = Depends(get_current_employee)):
    """List employee's app connections (white-labeled)"""
    try:
        # Get connections from oauth_tokens table
        result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).like("provider", "composio_%").execute()

        # Transform to white-labeled response
        connections = []
        for token in result.data:
            # Extract app name from provider (composio_gmail -> gmail)
            app_name = token["provider"].replace("composio_", "")

            connections.append({
                "app_name": app_name,
                "status": "active" if token.get("expires_at") else "inactive",
                "connected_at": token.get("created_at"),
                "last_updated": token.get("updated_at")
            })

        return {
            "success": True,
            "connections": connections,
            "total": len(connections)
        }

    except Exception as e:
        logger.error(f"Failed to list connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve connections"
        )

@app.delete("/api/uru/composio/connections/{app_name}")
async def disconnect_app(
    app_name: str,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Disconnect app (fully white-labeled)"""
    try:
        # Get connection details from oauth_tokens table
        provider_name = f"composio_{app_name.lower()}"
        result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).single().execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No connection found for {app_name}"
            )

        connection = result.data

        # Revoke Google OAuth token if we have one
        if connection.get("access_token"):
            try:
                access_token = decrypt_data(connection["access_token"])
                revoke_response = await http_client.post(
                    f"https://oauth2.googleapis.com/revoke?token={access_token}",
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                if revoke_response.status_code == 200:
                    logger.info(f"Successfully revoked Google token for {app_name}")
                else:
                    logger.warning(f"Failed to revoke Google token: {revoke_response.text}")
            except Exception as e:
                logger.warning(f"Error revoking Google token: {e}")

        # Delete the oauth token record
        supabase.table('oauth_tokens').delete().eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        logger.info(f"Successfully disconnected {app_name} for employee {employee['id']}")

        return {
            "success": True,
            "app_name": app_name,
            "message": f"{app_name.title()} disconnected successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to disconnect app: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disconnect app"
        )

# ============================================================================
# NEW MULTI-INTEGRATION ENDPOINTS
# ============================================================================

@app.get("/api/uru/integrations/available")
async def get_available_integrations(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get all available integrations organized by category"""
    try:
        integrations = integration_registry.get_all_integrations()
        categories = integration_registry.get_categories()

        # Organize integrations by category
        categorized_integrations = {}
        for category in categories:
            categorized_integrations[category] = []

        for app_name, config in integrations.items():
            integration_data = {
                "id": app_name,
                "name": config.display_name,
                "description": config.description,
                "category": config.category,
                "auth_type": config.auth_type.value,
                "tier": config.tier,
                "capabilities": config.capabilities,
                "logo_url": config.logo_url,
                "is_enabled": config.is_enabled,
                "documentation_url": config.documentation_url
            }
            categorized_integrations[config.category].append(integration_data)

        return {
            "success": True,
            "categories": categorized_integrations,
            "total_integrations": len(integrations)
        }

    except Exception as e:
        logger.error(f"Failed to get available integrations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available integrations"
        )

@app.get("/api/uru/integrations/connections")
async def get_user_connections(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get user's current integration connections with status"""
    try:
        # Get all oauth tokens for this employee
        oauth_result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).execute()

        connections = []
        for token_record in oauth_result.data:
            provider = token_record["provider"]

            # Extract app name from provider (remove composio_ prefix)
            if provider.startswith("composio_"):
                app_name = provider.replace("composio_", "")
                integration_config = integration_registry.get_integration(app_name)

                if integration_config:
                    # Check if token is expired
                    expires_at = token_record.get("expires_at")
                    is_expired = False
                    if expires_at:
                        try:
                            expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                            is_expired = expiry_time < datetime.now(timezone.utc)
                        except:
                            is_expired = True

                    connection_data = {
                        "id": app_name,
                        "name": integration_config.display_name,
                        "category": integration_config.category,
                        "logo_url": integration_config.logo_url,
                        "is_connected": True,
                        "is_expired": is_expired,
                        "connected_at": token_record.get("updated_at"),
                        "scopes": token_record.get("scopes", []),
                        "metadata": token_record.get("metadata", {})
                    }
                    connections.append(connection_data)

        return {
            "success": True,
            "connections": connections,
            "total_connections": len(connections)
        }

    except Exception as e:
        logger.error(f"Failed to get user connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve integration connections"
        )

@app.post("/api/uru/integrations/connect/{integration_id}")
async def connect_integration(
    integration_id: str,
    request: ComposioConnectRequest,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Connect to a specific integration using white-labeled OAuth"""
    try:
        # Get integration configuration
        integration_config = integration_registry.get_integration(integration_id)
        if not integration_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Integration '{integration_id}' not found"
            )

        if not integration_config.is_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Integration '{integration_id}' is not enabled"
            )

        # Check if already connected
        provider_name = f"composio_{integration_id}"
        existing_token = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if existing_token.data:
            return {
                "success": True,
                "integration_id": integration_id,
                "already_connected": True,
                "message": f"Your {integration_config.display_name} account is already connected"
            }

        # Handle different auth types
        if integration_config.auth_type == AuthType.OAUTH2:
            return await _handle_oauth2_connection(integration_config, employee, request)
        elif integration_config.auth_type == AuthType.API_KEY:
            return await _handle_api_key_connection(integration_config, employee, request)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Auth type '{integration_config.auth_type.value}' not yet supported"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Integration connection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect integration"
        )

async def _handle_oauth2_connection(
    integration_config: IntegrationConfig,
    employee: Dict[str, Any],
    request: ComposioConnectRequest
) -> Dict[str, Any]:
    """Handle OAuth2 connection flow"""
    if not integration_config.oauth_config:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth configuration missing for integration"
        )

    oauth_config = integration_config.oauth_config

    # Generate OAuth state for security
    oauth_state = secrets.token_urlsafe(32)

    # Store OAuth state in database
    oauth_record = {
        "employee_id": employee["id"],
        "provider": integration_config.composio_app_name,
        "state_token": oauth_state,
        "redirect_url": request.redirect_url or f"{FRONTEND_URL}/app/settings",
        "requested_scopes": oauth_config.scopes,
        "expires_at": (datetime.now(timezone.utc) + timedelta(minutes=10)).isoformat()
    }

    supabase.table('oauth_states').upsert(
        oauth_record,
        on_conflict="employee_id,provider"
    ).execute()

    # Build OAuth URL
    scopes = " ".join(oauth_config.scopes)
    oauth_params = {
        "client_id": os.getenv(oauth_config.client_id_env),
        "redirect_uri": f"{COMPOSIO_SERVICE_URL}{oauth_config.redirect_uri_path}/{integration_config.composio_app_name}",
        "scope": scopes,
        "response_type": "code",
        "state": oauth_state,
        "access_type": "offline",
        "prompt": "consent"
    }

    authorization_url = oauth_config.authorization_url + "?" + urllib.parse.urlencode(oauth_params)

    logger.info(f"Generated OAuth URL for {integration_config.composio_app_name} for employee {employee['id']}")

    return {
        "success": True,
        "integration_id": integration_config.composio_app_name,
        "authorization_url": authorization_url,
        "state": oauth_state,
        "message": f"Visit the authorization URL to connect your {integration_config.display_name} account"
    }

async def _handle_api_key_connection(
    integration_config: IntegrationConfig,
    employee: Dict[str, Any],
    request: ComposioConnectRequest
) -> Dict[str, Any]:
    """Handle API Key connection flow"""
    # API Key connections would be handled differently
    # For now, return not implemented
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="API Key authentication not yet implemented"
    )

@app.get("/api/uru/integrations/oauth/callback/{integration_id}")
async def integration_oauth_callback(
    integration_id: str,
    code: str,
    state: str,
    error: Optional[str] = None
):
    """Generic OAuth callback handler for all integrations"""
    try:
        if error:
            logger.error(f"OAuth error for {integration_id}: {error}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=oauth_denied&integration={integration_id}",
                status_code=302
            )

        # Get integration configuration
        integration_config = integration_registry.get_integration(integration_id)
        if not integration_config or not integration_config.oauth_config:
            logger.error(f"Invalid integration or missing OAuth config: {integration_id}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=invalid_integration&integration={integration_id}",
                status_code=302
            )

        # Verify OAuth state
        oauth_state_result = supabase.table('oauth_states').select("*").eq(
            "state_token", state
        ).eq("provider", integration_id).single().execute()

        if not oauth_state_result.data:
            logger.error(f"Invalid OAuth state: {state}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=invalid_state&integration={integration_id}",
                status_code=302
            )

        oauth_state_data = oauth_state_result.data
        employee_id = oauth_state_data["employee_id"]
        redirect_url = oauth_state_data["redirect_url"]
        oauth_config = integration_config.oauth_config

        # Exchange code for tokens
        token_data = {
            "client_id": os.getenv(oauth_config.client_id_env),
            "client_secret": os.getenv(oauth_config.client_secret_env),
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": f"{COMPOSIO_SERVICE_URL}{oauth_config.redirect_uri_path}/{integration_id}"
        }

        token_response = await http_client.post(
            oauth_config.token_url,
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if token_response.status_code != 200:
            logger.error(f"Token exchange failed for {integration_id}: {token_response.text}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=token_exchange_failed&integration={integration_id}",
                status_code=302
            )

        tokens = token_response.json()
        access_token = tokens.get("access_token")
        refresh_token = tokens.get("refresh_token")
        expires_in = tokens.get("expires_in", 3600)

        if not access_token:
            logger.error(f"No access token received for {integration_id}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=no_access_token&integration={integration_id}",
                status_code=302
            )

        # Store tokens in oauth_tokens table
        provider_name = f"composio_{integration_id}"
        oauth_record = {
            "employee_id": employee_id,
            "provider": provider_name,
            "access_token": encrypt_data(access_token),
            "refresh_token": encrypt_data(refresh_token) if refresh_token else None,
            "expires_at": (datetime.now(timezone.utc) + timedelta(seconds=expires_in)).isoformat(),
            "scopes": oauth_config.scopes,
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "metadata": {
                "integration_id": integration_id,
                "integration_name": integration_config.display_name,
                "connected_via": "uru_oauth",
                "auth_type": integration_config.auth_type.value,
                "connected_at": datetime.now(timezone.utc).isoformat()
            }
        }

        # Upsert oauth token record
        supabase.table('oauth_tokens').upsert(
            oauth_record,
            on_conflict="employee_id,provider"
        ).execute()

        # Clean up OAuth state
        supabase.table('oauth_states').delete().eq("state_token", state).execute()

        logger.info(f"Successfully connected {integration_id} for employee {employee_id}")

        # Redirect back to frontend with success
        return RedirectResponse(
            url=f"{redirect_url}?success=true&integration={integration_id}&connected=true",
            status_code=302
        )

    except Exception as e:
        logger.error(f"OAuth callback error for {integration_id}: {e}")
        return RedirectResponse(
            url=f"{FRONTEND_URL}/app/settings?error=callback_failed&integration={integration_id}",
            status_code=302
        )

@app.delete("/api/uru/integrations/disconnect/{integration_id}")
async def disconnect_integration(
    integration_id: str,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Disconnect an integration by removing stored tokens"""
    try:
        provider_name = f"composio_{integration_id}"

        # Delete OAuth tokens
        result = supabase.table('oauth_tokens').delete().eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Integration '{integration_id}' not connected"
            )

        logger.info(f"Disconnected {integration_id} for employee {employee['id']}")

        return {
            "success": True,
            "integration_id": integration_id,
            "message": f"Successfully disconnected {integration_id}"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to disconnect {integration_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disconnect integration"
        )

@app.post("/api/uru/integrations/execute")
async def execute_integration_tool(
    request: Dict[str, Any],
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Execute a tool for a specific integration"""
    try:
        tool_name = request.get("tool_name")
        parameters = request.get("parameters", {})

        if not tool_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="tool_name is required"
            )

        logger.info(f"Executing integration tool: {tool_name} for employee {employee['id']}")
        logger.info(f"Parameters: {parameters}")

        # Extract integration ID from tool name (e.g., "slack_send_message" -> "slack")
        integration_id = tool_name.split('_')[0]

        # Check if user has this integration connected
        provider_name = f"composio_{integration_id}"
        oauth_result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if not oauth_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Integration '{integration_id}' not connected"
            )

        oauth_record = oauth_result.data[0]

        # Check if token is expired
        expires_at = oauth_record.get("expires_at")
        if expires_at:
            try:
                expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                if expiry_time < datetime.now(timezone.utc):
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=f"Integration '{integration_id}' token expired"
                    )
            except:
                pass

        # Decrypt access token
        encrypted_token = oauth_record["access_token"]
        access_token = decrypt_data(encrypted_token)

        # Execute tool via Composio
        result = await _execute_composio_tool(integration_id, tool_name, parameters, access_token)

        logger.info(f"Tool execution completed: {tool_name}")

        return {
            "success": True,
            "result": result,
            "integration_id": integration_id,
            "tool_name": tool_name
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Integration tool execution failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute integration tool"
        )

async def _execute_composio_tool(integration_id: str, tool_name: str, parameters: Dict[str, Any], access_token: str):
    """Execute a tool via Composio API"""
    try:
        # Get integration configuration
        integration_config = integration_registry.get_integration(integration_id)
        if not integration_config:
            raise Exception(f"Integration '{integration_id}' not found in registry")

        # For now, return a mock response since we need to implement actual Composio API calls
        # In a real implementation, this would call the Composio API with the access token

        logger.info(f"Mock execution of {tool_name} for {integration_id}")

        # Mock responses based on tool type
        if "send_message" in tool_name:
            return {
                "message_id": f"msg_{secrets.token_hex(8)}",
                "status": "sent",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "channel": parameters.get("channel", "general"),
                "text": parameters.get("message", parameters.get("text", ""))
            }
        elif "list" in tool_name:
            return {
                "items": [
                    {
                        "id": f"item_{i}",
                        "name": f"Sample {tool_name.replace('_', ' ').title()} {i}",
                        "created_at": datetime.now(timezone.utc).isoformat()
                    }
                    for i in range(1, min(parameters.get("limit", 5) + 1, 11))
                ],
                "total": parameters.get("limit", 5)
            }
        elif "create" in tool_name:
            return {
                "id": f"created_{secrets.token_hex(8)}",
                "status": "created",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **{k: v for k, v in parameters.items() if k not in ["limit", "max_results"]}
            }
        else:
            return {
                "status": "success",
                "tool": tool_name,
                "integration": integration_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "parameters": parameters
            }

    except Exception as e:
        logger.error(f"Composio tool execution failed: {e}")
        raise Exception(f"Tool execution failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
