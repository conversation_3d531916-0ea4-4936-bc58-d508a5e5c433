# auth-service/token_monitor.py
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Set
from supabase import Client
import os

class TokenMonitor:
    def __init__(self, supabase: Client):
        self.supabase = supabase
        self.running = False
        self.check_interval = 60  # Check every 60 seconds
        self.refresh_buffer = 5 * 60  # Refresh 5 minutes before expiry
        self.logger = logging.getLogger(__name__)
        
    async def start_monitoring(self):
        """Start the proactive token refresh monitoring"""
        self.running = True
        self.logger.info(" Starting proactive token monitoring")
        
        while self.running:
            try:
                await self._check_and_refresh_tokens()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f" Token monitoring error: {e}")
                await asyncio.sleep(self.check_interval)  # Continue despite errors
    
    def stop_monitoring(self):
        """Stop the monitoring loop"""
        self.running = False
        self.logger.info(" Stopped token monitoring")
    
    async def _check_and_refresh_tokens(self):
        """Check all OAuth tokens and refresh if needed"""
        try:
            # Get all OAuth tokens that expire soon
            now = datetime.utcnow()
            refresh_threshold = now + timedelta(seconds=self.refresh_buffer)
            
            result = self.supabase.table('oauth_tokens').select(
                "employee_id, provider, expires_at, refresh_token"
            ).lt('expires_at', refresh_threshold.isoformat()).execute()
            
            if not result.data:
                return  # No tokens need refreshing
            
            self.logger.info(f"⏰ Found {len(result.data)} tokens that need refreshing")
            
            for token_data in result.data:
                if token_data['refresh_token']:  # Only refresh if we have a refresh token
                    await self._refresh_employee_token(
                        token_data['employee_id'],
                        token_data['provider']
                    )
                else:
                    self.logger.warning(
                        f" No refresh token for employee {token_data['employee_id']} "
                        f"provider {token_data['provider']} - requires re-auth"
                    )
                    
        except Exception as e:
            self.logger.error(f" Error checking tokens: {e}")
    
    async def _refresh_employee_token(self, employee_id: str, provider: str):
        """Refresh tokens for a specific employee and provider"""
        try:
            if provider == 'google':
                await self._refresh_google_token(employee_id)
            # Add other providers here as needed
            
        except Exception as e:
            self.logger.error(f" Failed to refresh {provider} token for employee {employee_id}: {e}")
    
    async def _refresh_google_token(self, employee_id: str):
        """Refresh Google OAuth tokens for an employee"""
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from auth import EmployeeAuth
        
        try:
            # Get current tokens
            auth_system = EmployeeAuth(self.supabase)
            tokens = await auth_system.get_oauth_tokens(employee_id, "google")
            
            if not tokens['refresh_token']:
                self.logger.warning(f" No refresh token for employee {employee_id}")
                return
            
            # Create credentials and refresh
            credentials = Credentials(
                token=tokens['access_token'],
                refresh_token=tokens['refresh_token'],
                token_uri="https://oauth2.googleapis.com/token",
                client_id=os.getenv("GOOGLE_CLIENT_ID"),
                client_secret=os.getenv("GOOGLE_CLIENT_SECRET"),
                scopes=tokens['scopes']
            )
            
            # Refresh the tokens
            credentials.refresh(Request())
            
            # Store the new tokens
            await auth_system.store_oauth_tokens(
                employee_id=employee_id,
                provider="google",
                access_token=credentials.token,
                refresh_token=credentials.refresh_token,
                expires_at=credentials.expiry,
                scopes=credentials.scopes
            )
            
            self.logger.info(f" Successfully refreshed Google tokens for employee {employee_id}")
            
        except Exception as e:
            self.logger.error(f" Google token refresh failed for employee {employee_id}: {e}")