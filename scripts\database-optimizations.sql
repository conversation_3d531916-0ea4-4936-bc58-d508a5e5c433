-- ===========================================
-- DATABASE OPTIMIZATIONS FOR URU WORKSPACE PLATFORM
-- Performance improvements, indexing, and connection pooling setup
-- ===========================================

-- ===========================================
-- AUDIT LOGS TABLE OPTIMIZATIONS
-- ===========================================

-- Create indexes for audit_logs table for efficient querying
CREATE INDEX IF NOT EXISTS idx_audit_logs_employee_timestamp ON audit_logs(employee_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_workspace_timestamp ON audit_logs(workspace_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_service ON audit_logs(service);
CREATE INDEX IF NOT EXISTS idx_audit_logs_success ON audit_logs(success);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp DESC);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_audit_logs_workspace_event_timestamp ON audit_logs(workspace_id, event_type, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_employee_event_timestamp ON audit_logs(employee_id, event_type, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_service_timestamp ON audit_logs(service, timestamp DESC);

-- Partial indexes for performance on filtered queries
CREATE INDEX IF NOT EXISTS idx_audit_logs_failed_events ON audit_logs(workspace_id, timestamp DESC) WHERE success = false;
CREATE INDEX IF NOT EXISTS idx_audit_logs_recent_events ON audit_logs(workspace_id, event_type) WHERE timestamp >= NOW() - INTERVAL '24 hours';

-- ===========================================
-- OAUTH TOKENS TABLE OPTIMIZATIONS
-- ===========================================

-- Indexes for oauth_tokens table
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_employee_provider ON oauth_tokens(employee_id, provider);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires_at ON oauth_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_workspace_provider ON oauth_tokens(workspace_id, provider) WHERE workspace_id IS NOT NULL;

-- Partial index for expired tokens
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expired ON oauth_tokens(employee_id, expires_at) WHERE expires_at < NOW();

-- ===========================================
-- EMPLOYEES TABLE OPTIMIZATIONS
-- ===========================================

-- Ensure proper indexes on employees table
CREATE INDEX IF NOT EXISTS idx_employees_workspace_id ON employees(workspace_id);
CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_created_at ON employees(created_at DESC);

-- ===========================================
-- WORKSPACES TABLE OPTIMIZATIONS
-- ===========================================

-- Indexes for workspaces table
CREATE INDEX IF NOT EXISTS idx_workspaces_slug ON workspaces(slug);
CREATE INDEX IF NOT EXISTS idx_workspaces_created_at ON workspaces(created_at DESC);

-- ===========================================
-- RATE LIMIT REQUESTS TABLE OPTIMIZATIONS
-- ===========================================

-- Additional indexes for rate_limit_requests (if not already created)
CREATE INDEX IF NOT EXISTS idx_rate_limit_employee_recent ON rate_limit_requests(employee_id, timestamp DESC) WHERE timestamp >= NOW() - INTERVAL '1 hour';
CREATE INDEX IF NOT EXISTS idx_rate_limit_cleanup ON rate_limit_requests(timestamp) WHERE timestamp < NOW() - INTERVAL '24 hours';

-- ===========================================
-- DATABASE MAINTENANCE FUNCTIONS
-- ===========================================

-- Function to analyze table statistics for query optimization
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
    ANALYZE audit_logs;
    ANALYZE oauth_tokens;
    ANALYZE employees;
    ANALYZE workspaces;
    ANALYZE rate_limit_requests;
    
    RAISE NOTICE 'Table statistics updated successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get table sizes and index usage
CREATE OR REPLACE FUNCTION get_table_performance_stats()
RETURNS TABLE(
    table_name TEXT,
    table_size TEXT,
    index_size TEXT,
    total_size TEXT,
    row_count BIGINT,
    index_usage_ratio NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename as table_name,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
        pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) + pg_indexes_size(schemaname||'.'||tablename)) as total_size,
        n_tup_ins + n_tup_upd + n_tup_del as row_count,
        CASE 
            WHEN seq_scan + idx_scan = 0 THEN 0
            ELSE ROUND((idx_scan::numeric / (seq_scan + idx_scan)) * 100, 2)
        END as index_usage_ratio
    FROM pg_stat_user_tables 
    WHERE schemaname = 'public'
    AND tablename IN ('audit_logs', 'oauth_tokens', 'employees', 'workspaces', 'rate_limit_requests')
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to identify slow queries and missing indexes
CREATE OR REPLACE FUNCTION get_query_performance_recommendations()
RETURNS TABLE(
    recommendation_type TEXT,
    table_name TEXT,
    recommendation TEXT,
    impact_level TEXT
) AS $$
BEGIN
    -- Check for tables with low index usage
    RETURN QUERY
    SELECT 
        'INDEX_USAGE'::TEXT as recommendation_type,
        tablename::TEXT as table_name,
        'Consider adding indexes - current usage: ' || 
        CASE 
            WHEN seq_scan + idx_scan = 0 THEN '0%'
            ELSE ROUND((idx_scan::numeric / (seq_scan + idx_scan)) * 100, 2)::TEXT || '%'
        END as recommendation,
        CASE 
            WHEN seq_scan > idx_scan * 2 THEN 'HIGH'
            WHEN seq_scan > idx_scan THEN 'MEDIUM'
            ELSE 'LOW'
        END as impact_level
    FROM pg_stat_user_tables 
    WHERE schemaname = 'public'
    AND tablename IN ('audit_logs', 'oauth_tokens', 'employees', 'workspaces', 'rate_limit_requests')
    AND seq_scan > 100  -- Only tables with significant sequential scans
    ORDER BY seq_scan DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===========================================
-- AUTOMATED MAINTENANCE PROCEDURES
-- ===========================================

-- Function to clean up old audit logs (retention policy)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM audit_logs 
    WHERE timestamp < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Update statistics after cleanup
    ANALYZE audit_logs;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to vacuum and reindex tables for maintenance
CREATE OR REPLACE FUNCTION perform_maintenance()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
BEGIN
    -- Vacuum analyze all tables
    VACUUM ANALYZE audit_logs;
    VACUUM ANALYZE oauth_tokens;
    VACUUM ANALYZE employees;
    VACUUM ANALYZE workspaces;
    VACUUM ANALYZE rate_limit_requests;
    
    result_text := 'Maintenance completed: VACUUM ANALYZE performed on all tables';
    
    -- Update table statistics
    PERFORM update_table_statistics();
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===========================================
-- CONNECTION POOLING CONFIGURATION
-- ===========================================

-- Set optimal PostgreSQL configuration for connection pooling
-- These should be set in postgresql.conf or via ALTER SYSTEM

-- Recommended settings for production (adjust based on your server specs):
-- max_connections = 200
-- shared_buffers = 256MB (25% of RAM)
-- effective_cache_size = 1GB (75% of RAM)
-- work_mem = 4MB
-- maintenance_work_mem = 64MB
-- checkpoint_completion_target = 0.9
-- wal_buffers = 16MB
-- default_statistics_target = 100

-- Create a view to monitor connection usage
CREATE OR REPLACE VIEW connection_stats AS
SELECT 
    state,
    COUNT(*) as connection_count,
    ROUND(COUNT(*) * 100.0 / (SELECT setting::int FROM pg_settings WHERE name = 'max_connections'), 2) as percentage
FROM pg_stat_activity 
WHERE pid <> pg_backend_pid()
GROUP BY state
ORDER BY connection_count DESC;

-- ===========================================
-- MONITORING AND ALERTING
-- ===========================================

-- Function to check database health metrics
CREATE OR REPLACE FUNCTION get_database_health_metrics()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'timestamp', NOW(),
        'connection_stats', (
            SELECT json_agg(row_to_json(cs))
            FROM connection_stats cs
        ),
        'table_sizes', (
            SELECT json_agg(row_to_json(tps))
            FROM get_table_performance_stats() tps
        ),
        'cache_hit_ratio', (
            SELECT ROUND(
                (sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 2
            )
            FROM pg_statio_user_tables
        ),
        'active_connections', (
            SELECT count(*) FROM pg_stat_activity WHERE state = 'active'
        ),
        'database_size', (
            SELECT pg_size_pretty(pg_database_size(current_database()))
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for monitoring functions
GRANT EXECUTE ON FUNCTION get_database_health_metrics() TO authenticated;
GRANT EXECUTE ON FUNCTION get_table_performance_stats() TO authenticated;
GRANT SELECT ON connection_stats TO authenticated;

-- Comments for documentation
COMMENT ON FUNCTION cleanup_old_audit_logs IS 'Removes audit log records older than specified retention period';
COMMENT ON FUNCTION perform_maintenance IS 'Performs routine database maintenance including VACUUM ANALYZE';
COMMENT ON FUNCTION get_database_health_metrics IS 'Returns comprehensive database health and performance metrics';
COMMENT ON VIEW connection_stats IS 'Real-time view of database connection usage by state';
