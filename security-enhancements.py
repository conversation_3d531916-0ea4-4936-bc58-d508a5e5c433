# ===========================================
# CRITICAL SECURITY ENHANCEMENTS FOR PRODUCTION
# ===========================================

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
import time
import uuid
import re
from typing import Dict, Any
import logging

# ===========================================
# SECURITY HEADERS MIDDLEWARE
# ===========================================

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to all responses"""
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        
        # HSTS for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # CSP for API endpoints
        response.headers["Content-Security-Policy"] = "default-src 'none'; frame-ancestors 'none';"
        
        return response

# ===========================================
# REQUEST ID MIDDLEWARE
# ===========================================

class RequestIDMiddleware(BaseHTTPMiddleware):
    """Add unique request ID for tracing"""
    
    async def dispatch(self, request: Request, call_next):
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        
        return response

# ===========================================
# INPUT VALIDATION UTILITIES
# ===========================================

class InputValidator:
    """Comprehensive input validation and sanitization"""
    
    # Regex patterns for validation
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    UUID_PATTERN = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')
    WORKSPACE_SLUG_PATTERN = re.compile(r'^[a-z0-9-]{3,50}$')
    
    # Dangerous patterns to block
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\bUNION\s+SELECT\b)"
    ]
    
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>.*?</iframe>"
    ]
    
    @classmethod
    def validate_email(cls, email: str) -> bool:
        """Validate email format"""
        if not email or len(email) > 254:
            return False
        return bool(cls.EMAIL_PATTERN.match(email))
    
    @classmethod
    def validate_uuid(cls, uuid_str: str) -> bool:
        """Validate UUID format"""
        if not uuid_str:
            return False
        return bool(cls.UUID_PATTERN.match(uuid_str.lower()))
    
    @classmethod
    def validate_workspace_slug(cls, slug: str) -> bool:
        """Validate workspace slug format"""
        if not slug:
            return False
        return bool(cls.WORKSPACE_SLUG_PATTERN.match(slug))
    
    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 1000) -> str:
        """Sanitize string input"""
        if not isinstance(value, str):
            raise ValueError("Input must be a string")
        
        # Truncate if too long
        if len(value) > max_length:
            raise ValueError(f"Input too long (max {max_length} characters)")
        
        # Check for SQL injection patterns
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError("Potentially malicious input detected")
        
        # Check for XSS patterns
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError("Potentially malicious input detected")
        
        # Basic sanitization
        value = value.strip()
        
        return value
    
    @classmethod
    def validate_tool_parameters(cls, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize tool parameters"""
        if not isinstance(parameters, dict):
            raise ValueError("Parameters must be a dictionary")
        
        sanitized = {}
        for key, value in parameters.items():
            # Validate key
            if not isinstance(key, str) or len(key) > 100:
                raise ValueError(f"Invalid parameter key: {key}")
            
            # Sanitize key
            clean_key = cls.sanitize_string(key, 100)
            
            # Sanitize value based on type
            if isinstance(value, str):
                clean_value = cls.sanitize_string(value, 10000)
            elif isinstance(value, (int, float, bool)):
                clean_value = value
            elif isinstance(value, list):
                # Validate list items
                if len(value) > 100:
                    raise ValueError("Too many list items")
                clean_value = [cls.sanitize_string(str(item), 1000) if isinstance(item, str) else item for item in value]
            elif isinstance(value, dict):
                # Recursively validate nested dict
                clean_value = cls.validate_tool_parameters(value)
            else:
                raise ValueError(f"Unsupported parameter type: {type(value)}")
            
            sanitized[clean_key] = clean_value
        
        return sanitized

# ===========================================
# REDIS-BASED RATE LIMITING
# ===========================================

import redis
from datetime import datetime, timedelta

class RedisRateLimiter:
    """Redis-based rate limiting for production"""
    
    def __init__(self, redis_url: str = None):
        try:
            if redis_url:
                self.redis_client = redis.from_url(redis_url)
            else:
                # Fallback to localhost Redis
                self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            
            # Test connection
            self.redis_client.ping()
            self.available = True
        except Exception as e:
            logging.warning(f"Redis not available, falling back to in-memory rate limiting: {e}")
            self.available = False
            self.memory_store = {}
    
    async def check_rate_limit(self, key: str, limit: int, window_seconds: int) -> bool:
        """Check if request is within rate limit"""
        if not self.available:
            return self._check_memory_rate_limit(key, limit, window_seconds)
        
        try:
            current_time = int(time.time())
            window_start = current_time - window_seconds
            
            # Use Redis sorted set for sliding window
            pipe = self.redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(key, window_seconds)
            
            results = pipe.execute()
            current_count = results[1]
            
            return current_count < limit
            
        except Exception as e:
            logging.error(f"Redis rate limit check failed: {e}")
            return True  # Allow on error
    
    def _check_memory_rate_limit(self, key: str, limit: int, window_seconds: int) -> bool:
        """Fallback in-memory rate limiting"""
        current_time = time.time()
        window_start = current_time - window_seconds
        
        if key not in self.memory_store:
            self.memory_store[key] = []
        
        # Clean old entries
        self.memory_store[key] = [
            timestamp for timestamp in self.memory_store[key]
            if timestamp > window_start
        ]
        
        # Check limit
        if len(self.memory_store[key]) >= limit:
            return False
        
        # Add current request
        self.memory_store[key].append(current_time)
        return True

# ===========================================
# ERROR HANDLING MIDDLEWARE
# ===========================================

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Sanitize error responses for production"""
    
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Log full error details
            request_id = getattr(request.state, 'request_id', 'unknown')
            logging.error(f"Request {request_id} failed: {str(e)}", exc_info=True)
            
            # Return sanitized error response
            if isinstance(e, HTTPException):
                return JSONResponse(
                    status_code=e.status_code,
                    content={
                        "error": e.detail,
                        "request_id": request_id,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )
            else:
                # Generic error for unexpected exceptions
                return JSONResponse(
                    status_code=500,
                    content={
                        "error": "Internal server error",
                        "request_id": request_id,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )

# ===========================================
# USAGE EXAMPLE
# ===========================================

def setup_security_middleware(app: FastAPI):
    """Setup all security middleware for FastAPI app"""
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
