import React from 'react';
import Link from 'next/link';
import { GetServerSideProps } from 'next';
import { UruLogo } from '../components/shared/UruLogo';
import { ArrowLeft, Building2, Zap, Brain, TrendingUp, Users, Clock, CheckCircle } from 'lucide-react';

export default function CaseStudiesPage() {
  const caseStudies = [
    {
      id: 1,
      company: "TechCorp Solutions",
      industry: "Software Development",
      size: "150 employees",
      challenge: "Data scattered across multiple tools making project tracking inefficient",
      solution: "Unified AI-powered data access across Slack, emails, and project management tools",
      results: [
        "40% faster project queries",
        "60% reduction in time spent searching for information",
        "Improved client communication response times",
        "Better project visibility for management"
      ],
      quote: "<PERSON><PERSON> transformed how we access project data. Instead of hunting through Slack, emails, and docs, our team gets instant answers about any project status or client requirement.",
      icon: Building2,
      color: "blue"
    },
    {
      id: 2,
      company: "Growth Dynamics",
      industry: "Marketing Agency",
      size: "75 employees",
      challenge: "Account managers struggled to access client data across 8 different tools",
      solution: "Centralized AI assistant providing instant access to campaign data and client insights",
      results: [
        "60% reduction in data lookup time",
        "25% improvement in client satisfaction scores",
        "Faster campaign optimization decisions",
        "Streamlined reporting processes"
      ],
      quote: "Our account managers can now instantly pull client performance data, campaign insights, and historical context without switching between 8 different tools.",
      icon: Zap,
      color: "purple"
    },
    {
      id: 3,
      company: "Meridian Consulting",
      industry: "Business Consulting",
      size: "45 employees",
      challenge: "Manual compilation of client analysis took weeks, limiting competitive advantage",
      solution: "Automated analysis across all client touchpoints with AI-powered insights",
      results: [
        "3x faster client analysis",
        "Deeper insights from cross-platform data",
        "Improved proposal quality and speed",
        "Enhanced competitive positioning"
      ],
      quote: "Uru gives us a competitive edge. We can analyze client patterns across all touchpoints and provide insights that would take weeks to compile manually.",
      icon: Brain,
      color: "cyan"
    },
    {
      id: 4,
      company: "DataFlow Inc",
      industry: "Financial Services",
      size: "200 employees",
      challenge: "Compliance reporting required manual data gathering from multiple systems",
      solution: "Automated compliance monitoring and reporting with real-time alerts",
      results: [
        "80% reduction in compliance reporting time",
        "100% accuracy in regulatory submissions",
        "Proactive risk identification",
        "Streamlined audit processes"
      ],
      quote: "Uru automated our most time-consuming compliance processes while ensuring we never miss critical regulatory requirements.",
      icon: TrendingUp,
      color: "green"
    }
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return {
          bg: 'from-uru-blue-600 to-uru-cyan-600',
          hover: 'hover:from-uru-blue-700 hover:to-uru-cyan-700',
          border: 'border-uru-blue-500/30',
          shadow: 'hover:shadow-xl-colored'
        };
      case 'purple':
        return {
          bg: 'from-uru-purple-600 to-uru-blue-600',
          hover: 'hover:from-uru-purple-700 hover:to-uru-blue-700',
          border: 'border-uru-purple-500/30',
          shadow: 'hover:shadow-xl hover:shadow-purple-500/20'
        };
      case 'cyan':
        return {
          bg: 'from-uru-cyan-600 to-green-600',
          hover: 'hover:from-uru-cyan-700 hover:to-green-700',
          border: 'border-uru-cyan-500/30',
          shadow: 'hover:shadow-xl hover:shadow-cyan-500/20'
        };
      case 'green':
        return {
          bg: 'from-green-600 to-uru-blue-600',
          hover: 'hover:from-green-700 hover:to-uru-blue-700',
          border: 'border-green-500/30',
          shadow: 'hover:shadow-xl hover:shadow-green-500/20'
        };
      default:
        return {
          bg: 'from-uru-blue-600 to-uru-cyan-600',
          hover: 'hover:from-uru-blue-700 hover:to-uru-cyan-700',
          border: 'border-uru-blue-500/30',
          shadow: 'hover:shadow-xl-colored'
        };
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-gray-900/98 backdrop-blur-md border-b border-gray-700/50 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <UruLogo size="md" />
              <span className="text-lg font-semibold text-white tracking-wide">Uru</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-all duration-200 font-medium px-4 py-2 rounded-lg hover:bg-gray-800/50"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Home</span>
              </Link>
              <Link
                href="/request-access"
                className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-6 py-2.5 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform"
              >
                Request Access
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative pt-32 pb-16">
        <div className="absolute inset-0 bg-gradient-to-br from-uru-blue-900/20 via-gray-900 to-uru-purple-900/20"></div>
        
        <div className="relative max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 tracking-tight leading-tight">
            Customer
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 block mt-2">
              Success Stories
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light">
            See how businesses are transforming their operations with AI-powered data intelligence
          </p>
        </div>
      </section>

      {/* Case Studies Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {caseStudies.map((study) => {
              const colors = getColorClasses(study.color);
              const IconComponent = study.icon;
              
              return (
                <div
                  key={study.id}
                  className={`bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-3xl p-8 border ${colors.border} ${colors.shadow} transition-all duration-300 group hover:scale-105 transform`}
                >
                  {/* Company Header */}
                  <div className="flex items-center space-x-4 mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${colors.bg} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white group-hover:text-blue-100 transition-colors">
                        {study.company}
                      </h3>
                      <p className="text-gray-400">{study.industry} • {study.size}</p>
                    </div>
                  </div>

                  {/* Challenge & Solution */}
                  <div className="space-y-6 mb-8">
                    <div>
                      <h4 className="text-lg font-semibold text-red-300 mb-3">Challenge</h4>
                      <p className="text-gray-300 leading-relaxed">{study.challenge}</p>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-green-300 mb-3">Solution</h4>
                      <p className="text-gray-300 leading-relaxed">{study.solution}</p>
                    </div>
                  </div>

                  {/* Results */}
                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-uru-cyan-300 mb-4">Results</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {study.results.map((result, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                          <span className="text-gray-300 text-sm">{result}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Quote */}
                  <blockquote className="bg-gray-900/60 rounded-xl p-6 border-l-4 border-uru-blue-500">
                    <p className="text-gray-200 leading-relaxed italic mb-4">
                      "{study.quote}"
                    </p>
                    <cite className="text-uru-blue-300 font-medium not-italic">
                      — {study.company} Team
                    </cite>
                  </blockquote>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/30 to-gray-800/30"></div>
        
        <div className="relative max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-5xl font-black text-white mb-8 tracking-tight leading-tight">
            Ready to Join These
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 block mt-2">
              Success Stories?
            </span>
          </h2>
          <p className="text-xl text-gray-300 mb-12 leading-relaxed">
            Transform your business intelligence with AI-powered data access and insights.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link
              href="/request-access"
              className="bg-gradient-to-r from-uru-blue-600 to-uru-cyan-600 hover:from-uru-blue-700 hover:to-uru-cyan-700 text-white px-10 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-glow-blue hover:scale-105 transform text-lg"
            >
              Get Started Today
            </Link>
            <button
              onClick={() => window.open('https://calendly.com/uru-discovery', '_blank')}
              className="border-2 border-uru-purple-500/50 hover:border-uru-purple-400 bg-uru-purple-500/10 hover:bg-uru-purple-500/20 text-uru-purple-300 hover:text-white px-10 py-4 rounded-xl font-semibold transition-all duration-300 backdrop-blur-sm hover:shadow-glow-purple hover:scale-105 transform text-lg"
            >
              Schedule Discovery Call
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}

// Prevent static generation for this page to avoid SSR issues
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
