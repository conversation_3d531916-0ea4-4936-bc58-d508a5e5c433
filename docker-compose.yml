# Uru Workspace Platform - Production Docker Compose
# This configuration uses microservices architecture with separate auth and integration services
# Environment variables are managed through Elestio's dashboard for production
# For local development, use: docker-compose -f docker-compose.dev.yml up

version: '3.8'

networks:
  uru-network:
    driver: bridge

services:
  # ===========================================
  # AUTHENTICATION SERVICE (Port 8003)
  # ===========================================
  
  auth-service:
    build: ./auth-service
    ports:
      - "8003:8003"
    networks:
      - uru-network
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      # Production must set these in Elestio environment variables
      - FRONTEND_URL=${FRONTEND_URL}
      - AUTH_SERVICE_URL=http://auth-service:8003
      - INTEGRATIONS_SERVICE_URL=http://integration-service:8002
      - APP_HOST=0.0.0.0
      - APP_PORT=8003
      - DEBUG=${DEBUG:-false}
      # Environment detection
      - NODE_ENV=${NODE_ENV:-production}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ===========================================
  # INTEGRATION SERVICE (Port 8002)
  # ===========================================
  
  integration-service:
    build: ./integration-service
    ports:
      - "8002:8002"
    networks:
      - uru-network
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      # Production must set these in Elestio environment variables
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI_INTEGRATIONS}
      - FRONTEND_URL=${FRONTEND_URL}
      - AUTH_SERVICE_URL=http://auth-service:8003
      - INTEGRATIONS_SERVICE_URL=http://integration-service:8002
      - JWT_SECRET=${JWT_SECRET}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - APP_HOST=0.0.0.0
      - APP_PORT=8002
      - DEBUG=${DEBUG:-false}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - COMPOSIO_SERVICE_URL=http://composio-service:8001
      # Environment detection
      - NODE_ENV=${NODE_ENV:-production}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    depends_on:
      auth-service:
        condition: service_healthy
      composio-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ===========================================
  # COMPOSIO SERVICE (Port 8001)
  # ===========================================
  
  composio-service:
    build: ./composio-service
    ports:
      - "8001:8001"
    networks:
      - uru-network
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - URU_COMPOSIO_API_KEY=${URU_COMPOSIO_API_KEY}
      - URU_COMPOSIO_BASE_URL=${URU_COMPOSIO_BASE_URL:-https://backend.composio.dev/api}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - FRONTEND_URL=${FRONTEND_URL}
      - AUTH_SERVICE_URL=http://auth-service:8003
      - INTEGRATIONS_SERVICE_URL=http://integration-service:8002
      - COMPOSIO_SERVICE_URL=http://composio-service:8001
      # Google OAuth credentials for white-labeled flow
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      # Environment detection
      - NODE_ENV=${NODE_ENV:-production}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ===========================================
  # MCP PROXY SERVICE (Port 3001)
  # ===========================================
  
  mcp-proxy:
    build: ./mcp-proxy
    ports:
      - "3001:3001"
    networks:
      - uru-network
    environment:
      - PORT=3001
      # Production must set these in Elestio environment variables
      - NODE_ENV=${NODE_ENV:-production}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - AUTH_SERVICE_URL=http://auth-service:8003
      - INTEGRATIONS_SERVICE_URL=http://integration-service:8002
      - MCP_PROXY_URL=http://mcp-proxy:3001
      - FRONTEND_URL=${FRONTEND_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - N8N_SSE_URL=${N8N_SSE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - COMPOSIO_SERVICE_URL=http://composio-service:8001
    depends_on:
      auth-service:
        condition: service_healthy
      integration-service:
        condition: service_healthy
      composio-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ===========================================
  # FRONTEND SERVICE (Port 3000)
  # ===========================================
  
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:3000}
        - NEXT_PUBLIC_AUTH_URL=${NEXT_PUBLIC_AUTH_URL:-http://localhost:8003}
        - NEXT_PUBLIC_INTEGRATIONS_URL=${NEXT_PUBLIC_INTEGRATIONS_URL:-http://localhost:8002}
        - NEXT_PUBLIC_MCP_URL=${NEXT_PUBLIC_MCP_URL:-http://localhost:3001}
    ports:
      - "3000:3000"
    networks:
      - uru-network
    environment:
      # Production must set these in Elestio environment variables
      - NODE_ENV=${NODE_ENV:-production}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_AUTH_URL=${NEXT_PUBLIC_AUTH_URL}
      - NEXT_PUBLIC_INTEGRATIONS_URL=${NEXT_PUBLIC_INTEGRATIONS_URL}
      - NEXT_PUBLIC_MCP_URL=${NEXT_PUBLIC_MCP_URL}
      - FRONTEND_URL=${FRONTEND_URL}
      - AUTH_SERVICE_URL=${AUTH_SERVICE_URL:-http://auth-service:8003}
      - INTEGRATIONS_SERVICE_URL=${INTEGRATIONS_SERVICE_URL:-http://integration-service:8002}
    depends_on:
      auth-service:
        condition: service_healthy
      integration-service:
        condition: service_healthy
      mcp-proxy:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
