import Link from 'next/link';
import { <PERSON>ru<PERSON>ogo } from './UruLogo';
import { Bar<PERSON>hart3, MessageSquare, TrendingUp, C<PERSON>, Users, Settings, Shield } from 'lucide-react';

const navItems = {
  intelligence: [
    { href: '/app', icon: BarChart3, label: 'Overview', active: true },
    { href: '/app/intelligence/ask', icon: MessageSquare, label: 'Ask Questions' },
    { href: '/app/intelligence/trends', icon: TrendingUp, label: 'Trends & Patterns' },
    { href: '/app/intelligence/insights', icon: Cpu, label: 'Insights Discovery' },
  ],
  workspace: [
    { href: '/app/settings', icon: Settings, label: 'Settings' },
    { href: '/app/team', icon: Users, label: 'Team Members' },
    { href: '/app/permissions', icon: Shield, label: 'Permissions' },
  ]
};

export const Sidebar = () => {
  const currentPath = '/app'; // This would be dynamic in a real app

  return (
    <div className="w-64 bg-gray-900 text-white flex flex-col border-r border-gray-800">
      <div className="flex items-center space-x-3 p-6 border-b border-gray-800">
        <UruLogo size="lg" className="flex-shrink-0" />
        <span className="font-black text-xl text-transparent bg-clip-text bg-gradient-to-r from-uru-blue-400 to-uru-cyan-400 tracking-tight">Uru</span>
      </div>

      <div className="flex-grow p-4">
        <nav className="space-y-6">
          <div>
            <h3 className="text-xs text-gray-500 uppercase font-semibold px-2 mb-3">Intelligence</h3>
            <ul className="space-y-1">
              {navItems.intelligence.map((item) => (
                <li key={item.href}>
                  <Link href={item.href} className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                    currentPath === item.href ? 'bg-gray-800 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                  }`}>
                    <item.icon className="w-4 h-4" />
                    <span className="text-sm">{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>



          <div>
            <h3 className="text-xs text-gray-500 uppercase font-semibold px-2 mb-3">Workspace</h3>
            <ul className="space-y-1">
              {navItems.workspace.map((item) => (
                <li key={item.href}>
                  <Link href={item.href} className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                    currentPath === item.href ? 'bg-gray-800 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                  }`}>
                    <item.icon className="w-4 h-4" />
                    <span className="text-sm">{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </nav>
      </div>

      <div className="border-t border-gray-800 pt-4">
        <div className="flex items-center space-x-3 p-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
              JM
            </div>
            <div>
              <p className="font-semibold text-white">Jackson Moss</p>
            </div>
        </div>
      </div>
    </div>
  );
}; 