# integration-service/auth.py
# Employee Authentication System

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import secrets
import hashlib
import jwt
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from supabase import Client
import os
from cryptography.fernet import Fernet

class EmployeeAuth:
    def __init__(self, supabase: Client):
        self.supabase = supabase
        self.jwt_secret = os.getenv("JWT_SECRET")
        self.jwt_algorithm = "HS256"
        # Extended JWT expiration for better user experience (8 hours)
        # Can be overridden by JWT_EXPIRE_MINUTES environment variable
        self.jwt_expire_minutes = int(os.getenv("JWT_EXPIRE_MINUTES", "480"))
        
        # For OAuth token encryption
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for OAuth tokens"""
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            # Generate a new key - in production, store this securely
            key = Fernet.generate_key().decode()
            print(f"Generated new encryption key: {key}")
            print("  Store this in your .env file as ENCRYPTION_KEY")
        return key.encode() if isinstance(key, str) else key
    
    async def create_employee_invitation(
        self, 
        workspace_id: str, 
        email: str, 
        name: str,
        role: str = "member"
    ) -> Dict[str, Any]:
        """Create an employee invitation"""
        
        # Generate secure invite token
        invite_token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(days=7)
        
        try:
            # Insert employee with invite token
            result = self.supabase.table('employees').insert({
                "workspace_id": workspace_id,
                "email": email,
                "name": name,
                "role": role,
                "status": "invited",
                "invite_token": invite_token,
                "invite_expires_at": expires_at.isoformat()
            }).execute()
            
            employee = result.data[0]
            
            # In a real app, you'd send an email here
            # Use environment variable for frontend URL or default to production
            frontend_url = os.getenv("FRONTEND_URL", "https://app.uruenterprises.com")
            invite_link = f"{frontend_url}/invite/{invite_token}"
            
            return {
                "employee_id": employee["id"],
                "invite_token": invite_token,
                "invite_link": invite_link,
                "expires_at": expires_at.isoformat(),
                "message": f"Invitation created for {email}"
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to create invitation: {str(e)}"
            )

    async def validate_invitation(self, invite_token: str) -> Dict[str, Any]:
        """Validate invitation token and return invitation details"""

        try:
            # Find employee by invite token
            result = self.supabase.table('employees').select(
                "*, workspaces!inner(id, slug, name)"
            ).eq("invite_token", invite_token).single().execute()

            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Invalid or expired invitation"
                )

            employee = result.data

            # Check if invitation is expired
            expires_at = datetime.fromisoformat(employee["invite_expires_at"].replace('Z', '+00:00'))
            if datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation has expired"
                )

            # Check if already accepted
            if employee["status"] != "invited":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation has already been accepted"
                )

            return {
                "employee_id": employee["id"],
                "email": employee["email"],
                "name": employee["name"],
                "role": employee["role"],
                "workspace_name": employee["workspaces"]["name"],
                "workspace_slug": employee["workspaces"]["slug"],
                "expires_at": employee["invite_expires_at"]
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to validate invitation: {str(e)}"
            )

    async def accept_invitation(
        self,
        invite_token: str,
        password: str
    ) -> Dict[str, Any]:
        """Accept an employee invitation and set password"""
        
        try:
            # Find employee by invite token
            result = self.supabase.table('employees').select("*").eq(
                "invite_token", invite_token
            ).single().execute()
            
            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Invalid or expired invitation"
                )
            
            employee = result.data
            
            # Check if invitation is expired
            expires_at = datetime.fromisoformat(employee["invite_expires_at"].replace('Z', '+00:00'))
            if datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation has expired"
                )
            
            # Hash password
            password_hash = self._hash_password(password)
            
            # Update employee - activate account
            self.supabase.table('employees').update({
                "password_hash": password_hash,
                "status": "active",
                "invite_token": None,
                "invite_expires_at": None
            }).eq("id", employee["id"]).execute()
            
            # Create session
            session_data = await self._create_session(employee["id"])

            # Automatically create Claude Desktop token for new user
            claude_token_data = await self._create_claude_desktop_token(employee["id"])

            return {
                "message": "Account activated successfully",
                "employee": {
                    "id": employee["id"],
                    "email": employee["email"],
                    "name": employee["name"],
                    "role": employee["role"],
                    "workspace_id": employee["workspace_id"]
                },
                "claude_desktop_token": claude_token_data,
                **session_data
            }
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to accept invitation: {str(e)}"
            )
    
    async def login(self, email: str, password: str, workspace_slug: str) -> Dict[str, Any]:
        """Employee login"""

        try:
            # Find employee in workspace
            result = self.supabase.table('employees').select(
                "*, workspaces!inner(id, slug, name)"
            ).eq("email", email).eq("workspaces.slug", workspace_slug).execute()

            # Check if employee exists in the specified workspace
            if not result.data or len(result.data) == 0:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid credentials"
                )

            employee = result.data[0]  # Get the first (and should be only) result

            # Check password
            if not self._verify_password(password, employee["password_hash"]):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid credentials"
                )

            # Check if account is active
            if employee["status"] != "active":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Account not activated"
                )

            # Update last login
            self.supabase.table('employees').update({
                "last_login": datetime.utcnow().isoformat()
            }).eq("id", employee["id"]).execute()

            # Create session
            session_data = await self._create_session(employee["id"])

            return {
                "message": "Login successful",
                "employee": {
                    "id": employee["id"],
                    "email": employee["email"],
                    "name": employee["name"],
                    "role": employee["role"],
                    "workspace_id": employee["workspace_id"],
                    "workspace": employee["workspaces"]
                },
                **session_data
            }

        except HTTPException:
            raise
        except Exception as e:
            # Log the actual error for debugging but return generic message for security
            print(f"Login error for {email} in {workspace_slug}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
    
    async def _create_session(self, employee_id: str) -> Dict[str, Any]:
        """Create a new session for employee"""
        
        # Generate session token
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(hours=24)
        
        # Store session in database
        self.supabase.table('user_sessions').insert({
            "employee_id": employee_id,
            "session_token": session_token,
            "expires_at": expires_at.isoformat()
        }).execute()
        
        # Create JWT token
        jwt_payload = {
            "employee_id": employee_id,
            "session_token": session_token,
            "exp": datetime.utcnow() + timedelta(minutes=self.jwt_expire_minutes),
            "iat": datetime.utcnow()
        }
        
        jwt_token = jwt.encode(jwt_payload, self.jwt_secret, algorithm=self.jwt_algorithm)
        
        return {
            "access_token": jwt_token,
            "token_type": "bearer",
            "expires_in": self.jwt_expire_minutes * 60,
            "session_expires_at": expires_at.isoformat()
        }

    async def _create_claude_desktop_token(self, employee_id: str) -> Dict[str, Any]:
        """Create a long-lived Claude Desktop token for employee"""

        try:
            # Generate Claude Desktop session token (90 days validity)
            claude_session_token = secrets.token_urlsafe(32)
            claude_expires_at = datetime.utcnow() + timedelta(days=90)

            # Store Claude Desktop session in database
            self.supabase.table('user_sessions').insert({
                "employee_id": employee_id,
                "session_token": claude_session_token,
                "expires_at": claude_expires_at.isoformat(),
                "session_type": "claude_desktop"
            }).execute()

            # Create JWT token for Claude Desktop
            claude_jwt_payload = {
                "employee_id": employee_id,
                "session_token": claude_session_token,
                "exp": claude_expires_at,
                "iat": datetime.utcnow(),
                "type": "claude_desktop"
            }

            claude_jwt_token = jwt.encode(claude_jwt_payload, self.jwt_secret, algorithm=self.jwt_algorithm)

            return {
                "claude_desktop_token": claude_jwt_token,
                "expires_at": claude_expires_at.isoformat(),
                "days_valid": 90,
                "message": "Claude Desktop token created automatically"
            }

        except Exception as e:
            # Don't fail the entire invitation acceptance if Claude token creation fails
            print(f"Warning: Failed to create Claude Desktop token: {str(e)}")
            return {
                "claude_desktop_token": None,
                "message": "Claude Desktop token creation failed, but account was activated successfully"
            }

    async def generate_token(self, employee_id: str) -> str:
        """Generate a new JWT token for an existing session"""
        try:
            # Get the most recent active session for the employee
            result = self.supabase.table('user_sessions').select("*").eq(
                "employee_id", employee_id
            ).is_("session_type", None).order("created_at", desc=True).limit(1).execute()

            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="No active session found"
                )

            session = result.data[0]
            session_token = session["session_token"]

            # Check if session is still valid
            expires_at = datetime.fromisoformat(session["expires_at"].replace('Z', '+00:00'))
            if datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Session expired"
                )

            # Create new JWT token with existing session
            jwt_payload = {
                "employee_id": employee_id,
                "session_token": session_token,
                "exp": datetime.utcnow() + timedelta(minutes=self.jwt_expire_minutes),
                "iat": datetime.utcnow()
            }

            jwt_token = jwt.encode(jwt_payload, self.jwt_secret, algorithm=self.jwt_algorithm)
            return jwt_token

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Token generation failed: {str(e)}"
            )

    async def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token and return employee data"""
        
        try:
            # Decode JWT
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            employee_id = payload.get("employee_id")
            session_token = payload.get("session_token")
            
            if not employee_id or not session_token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            # Verify session exists and is valid
            result = self.supabase.table('user_sessions').select("*").eq(
                "session_token", session_token
            ).eq("employee_id", employee_id).single().execute()
            
            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Session not found"
                )
            
            session = result.data
            expires_at = datetime.fromisoformat(session["expires_at"].replace('Z', '+00:00'))
            
            if datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Session expired"
                )
            
            # Get employee data
            employee_result = self.supabase.table('employees').select(
                "*, workspaces!inner(id, slug, name)"
            ).eq("id", employee_id).single().execute()
            
            if not employee_result.data:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Employee not found"
                )
            
            return employee_result.data
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired"
            )
        except jwt.PyJWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    async def store_oauth_tokens(
        self,
        employee_id: str,
        provider: str,
        access_token: str,
        refresh_token: Optional[str] = None,
        expires_at: Optional[datetime] = None,
        scopes: Optional[list] = None
    ) -> Dict[str, Any]:
        """Store encrypted OAuth tokens for employee"""
        
        try:
            # Encrypt tokens
            encrypted_access_token = self.cipher_suite.encrypt(access_token.encode()).decode()
            encrypted_refresh_token = None
            if refresh_token:
                encrypted_refresh_token = self.cipher_suite.encrypt(refresh_token.encode()).decode()
            
            # Use upsert to handle both insert and update cases
            result = self.supabase.table('oauth_tokens').upsert({
                "employee_id": employee_id,
                "provider": provider,
                "access_token": encrypted_access_token,
                "refresh_token": encrypted_refresh_token,
                "expires_at": expires_at.isoformat() if expires_at else None,
                "scopes": scopes or [],
                "updated_at": datetime.utcnow().isoformat()
            }, on_conflict='employee_id,provider').execute()  # Add the on_conflict parameter
            
            return {
                "message": f"{provider} tokens stored successfully",
                "provider": provider,
                "scopes": scopes,
                "expires_at": expires_at.isoformat() if expires_at else None
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to store OAuth tokens: {str(e)}"
            )
    
    async def get_oauth_tokens(self, employee_id: str, provider: str) -> Dict[str, Any]:
        """Get decrypted OAuth tokens for employee"""
        
        try:
            result = self.supabase.table('oauth_tokens').select("*").eq(
                "employee_id", employee_id
            ).eq("provider", provider).single().execute()
            
            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"No {provider} tokens found for employee"
                )
            
            token_data = result.data
            
            # Decrypt tokens
            access_token = self.cipher_suite.decrypt(token_data["access_token"].encode()).decode()
            refresh_token = None
            if token_data["refresh_token"]:
                refresh_token = self.cipher_suite.decrypt(token_data["refresh_token"].encode()).decode()
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_at": token_data["expires_at"],
                "scopes": token_data["scopes"]
            }
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve OAuth tokens: {str(e)}"
            )
    
    def _hash_password(self, password: str) -> str:
        """Hash password with salt"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """Verify password against stored hash"""
        if not stored_hash:
            return False
        
        try:
            salt, password_hash = stored_hash.split(':')
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash == computed_hash.hex()
        except ValueError:
            return False