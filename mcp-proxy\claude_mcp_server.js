// mcp-proxy/claude_mcp_server.js
// Fixed Claude Desktop MCP Server - No inheritance issues

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} = require('@modelcontextprotocol/sdk/types.js');
const axios = require('axios');

class ClaudeMCPServer {
    constructor() {
        // Remove the super() call - this class doesn't extend anything
        this.proxyUrl = 'http://localhost:3001';
        this.employeeToken = process.env.EMPLOYEE_TOKEN || 'anonymous';
        this.debugMode = process.env.DEBUG_MODE === 'true';
        
        // Add OAuth status caching to prevent repeated requests
        this.oauthStatusCache = {
            data: null,
            lastFetch: null,
            ttl: 30000 // Cache for 30 seconds
        };
        
        this.log('🔗 Claude MCP Server initializing...');
        this.log(`📡 Proxy URL: ${this.proxyUrl}`);
        this.log(`🔑 Employee Token: ${this.employeeToken.substring(0, 20)}...`);
        
        this.server = new Server(
            {
                name: 'uru-platform-claude',
                version: '2.0.0',
            },
            {
                capabilities: {
                    tools: {},
                },
            }
        );
        
        this.setupHandlers();
    }

    log(message) {
        if (this.debugMode) {
            console.log(`[CLAUDE-MCP] ${message}`);
        }
    }

    error(message) {
        // Always log errors
        console.error(`[CLAUDE-MCP] ❌ ${message}`);
    }

    // Add cached OAuth status method to prevent repeated requests
    async getCachedToolList() {
        const now = Date.now();
        
        // Return cached data if still valid
        if (this.oauthStatusCache.data && 
            this.oauthStatusCache.lastFetch && 
            (now - this.oauthStatusCache.lastFetch) < this.oauthStatusCache.ttl) {
            this.log('📋 Using cached tool list');
            return this.oauthStatusCache.data;
        }
        
        // Fetch fresh data
        try {
            this.log('🔄 Fetching fresh tool list from proxy...');
            const response = await axios.get(`${this.proxyUrl}/api/tools`, {
                headers: {
                    'Authorization': `Bearer ${this.employeeToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            
            if (!response.data.success) {
                throw new Error(`Proxy error: ${response.data.error}`);
            }
            
            // Cache the result
            this.oauthStatusCache.data = response.data;
            this.oauthStatusCache.lastFetch = now;
            
            this.log(`✅ Fetched ${response.data.tools.length} tools from proxy`);
            return response.data;
            
        } catch (error) {
            this.error(`Failed to fetch tools: ${error.message}`);
            // Return cached data even if expired, or empty response
            return this.oauthStatusCache.data || { success: false, tools: [] };
        }
    }

    setupHandlers() {
        // Handle tool listing with caching
        this.server.setRequestHandler(ListToolsRequestSchema, async (request) => {
            try {
                this.log('📋 Claude Desktop requesting tool list...');
                
                // Use cached tool list to prevent repeated OAuth calls
                const response = await this.getCachedToolList();
                
                if (!response.success) {
                    this.log('⚠️ No tools available from proxy');
                    return { tools: [] };
                }
                
                // Convert proxy format to MCP format
                const mcpTools = response.tools.map(tool => ({
                    name: tool.function.name,
                    description: tool.function.description,
                    inputSchema: tool.function.parameters
                }));
                
                this.log(`✅ Returning ${mcpTools.length} tools to Claude Desktop`);
                
                return {
                    tools: mcpTools
                };
                
            } catch (error) {
                this.error(`Error listing tools: ${error.message}`);
                return { tools: [] };
            }
        });

        // Handle tool execution
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            try {
                const { name, arguments: toolArgs } = request.params;
                
                this.log(`🔧 Claude Desktop executing tool: ${name}`);
                this.log(`📝 Arguments: ${JSON.stringify(toolArgs, null, 2)}`);
                
                // Call proxy with MCP format
                const mcpRequest = {
                    jsonrpc: '2.0',
                    id: Date.now(),
                    method: 'tools/call',
                    params: {
                        name: name,
                        arguments: toolArgs || {}
                    }
                };
                
                const response = await axios.post(`${this.proxyUrl}/mcp/tools/call`, mcpRequest, {
                    headers: {
                        'Authorization': `Bearer ${this.employeeToken}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 60000 // 60 second timeout for tool execution
                });
                
                this.log(`✅ Tool execution complete: ${name}`);
                
                // Format result for Claude (clean, no debug info)
                const resultText = this.formatToolResult(name, response.data);
                
                return {
                    content: [
                        {
                            type: 'text',
                            text: resultText
                        }
                    ]
                };
                
            } catch (error) {
                this.error(`Error executing tool ${request.params.name}: ${error.message}`);
                
                // Clean error message for Claude (no debug details)
                return {
                    content: [
                        {
                            type: 'text',
                            text: `❌ Tool execution failed: ${error.message}`
                        }
                    ]
                };
            }
        });
    }

    formatToolResult(toolName, result) {
        // Handle proxy response format - clean output for Claude
        if (result.result && result.success !== false) {
            return this.formatSuccessfulResult(toolName, result.result);
        } else if (result.error) {
            return `❌ **${toolName} Error:**\n\n${result.error}`;
        } else {
            return this.formatSuccessfulResult(toolName, result);
        }
    }

    formatSuccessfulResult(toolName, data) {
        // Format based on tool type
        if (this.isCompanyTool(toolName)) {
            return this.formatCompanyToolResult(toolName, data);
        } else {
            return this.formatPersonalToolResult(toolName, data);
        }
    }

    isCompanyTool(toolName) {
        const companyTools = [
            'Transcript_Log_Query', 'Client_Table_Query', 'Slack_Post', 'Client_Agents_Tool',
            'Ignition_Maguire_Gmail_MCP_List', 'Ignition_Maguire_Gmail_MCP_Execute',
            'Ignition_Drive_MCP_List', 'Ignition_Drive_MCP_Execute',
            'Webflow_Tools', 'Webflow_Execute', 'Uru_Tools', 'Uru_Execute'
        ];
        return companyTools.includes(toolName);
    }

    formatCompanyToolResult(toolName, data) {
        // Handle different result formats from n8n
        if (typeof data === 'string') {
            return `🏢 **${toolName} Result:**\n\n${data}`;
        }
        
        if (data.result && typeof data.result === 'string') {
            return `🏢 **${toolName} Result:**\n\n${data.result}`;
        }
        
        // Format specific company tools
        if (toolName === 'Transcript_Log_Query') {
            if (data.results && Array.isArray(data.results)) {
                const transcriptList = data.results.map(result => 
                    `🎙️ **${result.client_id || 'Client'}** (Score: ${result.relevance_score || 'N/A'})\n   ${result.excerpt || result.content || 'No content'}`
                ).join('\n\n');
                return `🎙️ **Call Transcript Analysis:**\n\n${transcriptList}\n\n📊 Found ${data.results.length} relevant transcripts`;
            }
        } else if (toolName === 'Client_Table_Query') {
            if (data.clients && Array.isArray(data.clients)) {
                const clientList = data.clients.map(client => 
                    `👤 **${client.name || client.client_name || 'Unknown'}** - CFO: ${client.cfo_assigned || 'Unassigned'}`
                ).join('\n');
                return `👥 **Client Database Results:**\n\n${clientList}\n\n📊 Found ${data.clients.length} clients`;
            }
        } else if (toolName === 'Slack_Post') {
            return `💬 **Slack Message Posted**\n\n✅ ${data.message || 'Message sent successfully'}`;
        }
        
        // Default formatting for company tools
        return `🏢 **${toolName} completed**\n\n${JSON.stringify(data, null, 2)}`;
    }

    formatPersonalToolResult(toolName, data) {
        // Handle Google Drive, Gmail, Calendar results
        if (toolName.startsWith('gdrive_')) {
            if (toolName === 'gdrive_search' && data.files) {
                const fileList = data.files.map(file => 
                    `📄 ${file.name} (${file.mimeType}) - Modified: ${file.modifiedTime}`
                ).join('\n');
                return `🔍 **Google Drive Search Results:**\n\n${fileList}\n\n📊 Found ${data.files.length} files`;
            } else if (toolName === 'gdrive_read_file' && data.content) {
                return `📄 **File Content: ${data.file_info?.name || 'Unknown'}**\n\n${data.content}`;
            }
        }
        
        // Default formatting for personal tools
        return `🔧 **${toolName} completed**\n\n${JSON.stringify(data, null, 2)}`;
    }

    async start() {
        console.log('🚀 Starting Uru Claude MCP Server...');
        console.log(`🔗 Connecting to proxy at: ${this.proxyUrl}`);
        console.log(`🎭 Employee mode: ${this.employeeToken === 'anonymous' ? 'Anonymous' : 'Authenticated'}`);
        console.log(`🐛 Debug mode: ${this.debugMode ? 'ON' : 'OFF'}`);
        
        // Test proxy connection
        try {
            const healthResponse = await axios.get(`${this.proxyUrl}/health`, { timeout: 5000 });
            console.log(`✅ Proxy health check passed:`, healthResponse.data.status);
        } catch (error) {
            console.error(`⚠️ Proxy health check failed:`, error.message);
            console.log(`🔄 Continuing anyway - proxy might start later...`);
        }
        
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        
        console.log('✅ Uru Claude MCP Server ready for Claude Desktop!');
        console.log('🎯 Available: All company tools + personal tools (when OAuth connected)');
        if (!this.debugMode) {
            console.log('📱 Production mode: Clean responses for Claude Desktop');
        }
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down Uru Claude MCP Server...');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Shutting down Uru Claude MCP Server...');
    process.exit(0);
});

// Start the server
if (require.main === module) {
    const server = new ClaudeMCPServer();
    server.start().catch(error => {
        console.error('❌ Failed to start Uru Claude MCP Server:', error);
        process.exit(1);
    });
}

module.exports = ClaudeMCPServer;