# OAuth Storage Migration Guide

This guide walks you through migrating from the `composio_connections` table to the standardized `oauth_tokens` table for consistent OAuth token storage across all services.

## Overview

The migration consolidates OAuth token storage by:
- Moving data from `composio_connections` to `oauth_tokens` table
- Updating composio-service to use the standardized `oauth_tokens` table
- Maintaining data integrity and encryption
- Providing rollback capabilities

## Prerequisites

1. **Environment Variables**: Ensure these are set:
   ```bash
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   ENCRYPTION_KEY=your_encryption_key
   ```

2. **Python Dependencies**: Install required packages:
   ```bash
   cd scripts
   pip install -r requirements.txt
   ```

3. **Database Access**: Ensure you have read/write access to the Supabase database

## Migration Steps

### Step 1: Test the Migration (Recommended)

Run the test script to verify the migration works with sample data:

```bash
cd scripts
python test-oauth-migration.py
```

This will:
- Create test employee and composio_connections records
- Run the migration on test data
- Verify the results
- Clean up test data

### Step 2: Backup Current Data

The migration script automatically creates backups, but you can also manually backup:

```bash
# The migration script will create a backup file like:
# oauth_migration_backup_YYYYMMDD_HHMMSS.json
```

### Step 3: Run the Migration

Execute the migration script:

```bash
cd scripts
python migrate-oauth-storage.py
```

The script will:
1. Analyze current data
2. Create automatic backup
3. Ask for confirmation
4. Migrate data from `composio_connections` to `oauth_tokens`
5. Verify migration success

### Step 4: Update Services

The composio-service has already been updated to use `oauth_tokens`. After migration:

1. **Restart Services**: Restart all services to use the updated code
   ```bash
   docker-compose restart composio-service
   docker-compose restart integration-service
   ```

2. **Test Functionality**: Verify OAuth connections work correctly
   ```bash
   # Test service health
   curl http://localhost:8001/health
   curl http://localhost:8002/health
   
   # Test OAuth endpoints
   curl http://localhost:8002/integrations/composio/connections \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

### Step 5: Verify Migration

1. **Check Data**: Verify all data was migrated correctly
2. **Test OAuth Flows**: Test connecting new OAuth accounts
3. **Monitor Logs**: Check service logs for any issues

## Rollback Procedure

If you need to rollback the migration:

```bash
cd scripts
python rollback-oauth-migration.py
```

This will:
1. List available backups
2. Allow you to select which backup to restore
3. Remove migrated OAuth tokens
4. Restore original `composio_connections` table
5. Verify rollback success

**Note**: After rollback, you'll need to revert the composio-service code changes.

## Data Mapping

The migration maps data as follows:

| composio_connections | oauth_tokens |
|---------------------|--------------|
| employee_id | employee_id |
| app_name | provider (as "composio_{app_name}") |
| access_token | access_token (re-encrypted) |
| refresh_token | refresh_token (re-encrypted) |
| expires_at | expires_at |
| scopes | scopes |
| created_at | created_at |
| updated_at | updated_at |
| user_info, metadata | metadata (combined) |

## Troubleshooting

### Common Issues

1. **Environment Variables Missing**
   ```
   Error: Missing required environment variables
   ```
   Solution: Ensure SUPABASE_URL, SUPABASE_KEY, and ENCRYPTION_KEY are set

2. **Encryption/Decryption Errors**
   ```
   Error: Failed to decrypt data
   ```
   Solution: Verify ENCRYPTION_KEY matches the one used to encrypt original data

3. **Database Connection Issues**
   ```
   Error: Failed to connect to Supabase
   ```
   Solution: Check SUPABASE_URL and SUPABASE_KEY are correct

4. **Duplicate Records**
   ```
   Warning: Skipping record - already exists
   ```
   This is normal - the migration skips records that already exist

### Verification Queries

Check migration results with these SQL queries:

```sql
-- Count original composio_connections
SELECT COUNT(*) FROM composio_connections;

-- Count migrated oauth_tokens
SELECT COUNT(*) FROM oauth_tokens WHERE provider LIKE 'composio_%';

-- View migrated records
SELECT employee_id, provider, metadata->>'original_app_name' as app_name
FROM oauth_tokens 
WHERE provider LIKE 'composio_%';
```

## Post-Migration Cleanup

After successful migration and testing, you can optionally:

1. **Remove composio_connections table** (only after thorough testing):
   ```sql
   DROP TABLE composio_connections;
   ```

2. **Clean up backup files** (keep at least one recent backup):
   ```bash
   # Keep the most recent backup, remove older ones
   ls -la oauth_migration_backup_*.json
   ```

## Support

If you encounter issues:

1. Check the migration logs: `migration.log`
2. Review the backup files for data integrity
3. Use the rollback procedure if needed
4. Contact the development team with log files

## Files Created

- `oauth_migration_backup_YYYYMMDD_HHMMSS.json` - Data backup
- `migration.log` - Migration execution log
- `rollback.log` - Rollback execution log (if rollback is used)
- `migration_test.log` - Test execution log (if test is run)
