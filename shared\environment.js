/**
 * Shared Environment Detection and Configuration Utilities (JavaScript)
 * Provides consistent environment detection across frontend and Node.js services
 */

const Environment = {
    DEVELOPMENT: 'development',
    PRODUCTION: 'production',
    TESTING: 'testing'
};

class EnvironmentConfig {
    constructor() {
        this.environment = this._detectEnvironment();
        this.isDevelopment = this.environment === Environment.DEVELOPMENT;
        this.isProduction = this.environment === Environment.PRODUCTION;
        this.isTesting = this.environment === Environment.TESTING;
    }

    _detectEnvironment() {
        // Simple environment detection based on NODE_ENV
        const nodeEnv = this._getEnvVar('NODE_ENV', 'development').toLowerCase();

        if (nodeEnv === 'production') {
            return Environment.PRODUCTION;
        } else if (['test', 'testing'].includes(nodeEnv)) {
            return Environment.TESTING;
        } else {
            return Environment.DEVELOPMENT;
        }
    }

    _getEnvVar(name, defaultValue = '') {
        // Handle both Node.js and browser environments
        if (typeof process !== 'undefined' && process.env) {
            return process.env[name] || defaultValue;
        } else if (typeof window !== 'undefined') {
            // In browser, check for Next.js public env vars
            return window.__NEXT_DATA__?.env?.[name] || 
                   window.process?.env?.[name] || 
                   defaultValue;
        }
        return defaultValue;
    }



    getOAuthServiceUrl() {
        return this._getEnvVar('OAUTH_SERVICE_URL', 'http://localhost:8003');
    }

    getMCPProxyUrl() {
        // Always check environment variable first
        const override = this._getEnvVar('MCP_PROXY_URL');
        if (override) {
            return override;
        }

        // Only provide localhost default for development
        if (this.isDevelopment) {
            return 'http://localhost:3001';
        } else {
            // Production must set MCP_PROXY_URL environment variable
            throw new Error('MCP_PROXY_URL environment variable must be set for production deployment');
        }
    }

    getFrontendUrl() {
        // Always check environment variable first
        const override = this._getEnvVar('FRONTEND_URL');
        if (override) {
            return override;
        }

        // Only provide localhost default for development
        if (this.isDevelopment) {
            return 'http://localhost:3000';
        } else {
            // Production must set FRONTEND_URL environment variable
            throw new Error('FRONTEND_URL environment variable must be set for production deployment');
        }
    }

    getGoogleRedirectUri() {
        // Always check environment variable first
        const override = this._getEnvVar('GOOGLE_REDIRECT_URI');
        if (override) {
            return override;
        }

        // Build from OAuth service URL (which handles environment detection properly)
        try {
            const oauthUrl = this.getOAuthServiceUrl();
            return `${oauthUrl}/oauth/google/callback`;
        } catch (error) {
            // If OAuth service URL is not set, we can't build redirect URI
            throw new Error('GOOGLE_REDIRECT_URI or OAUTH_SERVICE_URL environment variable must be set for production deployment');
        }
    }

    getCorsOrigins() {
        // Always check environment variable first
        const override = this._getEnvVar('CORS_ORIGINS');
        if (override) {
            try {
                return JSON.parse(override);
            } catch (e) {
                console.warn('Failed to parse CORS_ORIGINS, using defaults');
            }
        }

        // Only provide localhost defaults for development
        if (this.isDevelopment) {
            return [
                'http://localhost:3000',
                'http://localhost:3001',
                'http://localhost:8000',
                'http://127.0.0.1:3000',
                'http://127.0.0.1:3001',
                'http://127.0.0.1:8000'
            ];
        } else {
            // Production must set CORS_ORIGINS environment variable
            throw new Error('CORS_ORIGINS environment variable must be set for production deployment');
        }
    }

    getAllCorsOrigins() {
        const developmentOrigins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:8000',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://127.0.0.1:8000'
        ];

        // In development, allow localhost origins plus any production origins from env vars
        if (this.isDevelopment) {
            // Start with development origins
            const allOrigins = [...developmentOrigins];

            // Add production origins from environment variables if available
            const override = this._getEnvVar('CORS_ORIGINS');
            if (override) {
                try {
                    const prodOrigins = JSON.parse(override);
                    allOrigins.push(...prodOrigins);
                } catch (e) {
                    // Ignore parsing errors
                }
            }

            return allOrigins;
        } else {
            // Production uses only environment variable
            return this.getCorsOrigins();
        }
    }

    getGoogleOAuthConfig() {
        return {
            clientId: this._getEnvVar('GOOGLE_CLIENT_ID'),
            clientSecret: this._getEnvVar('GOOGLE_CLIENT_SECRET'),
            redirectUri: this.getGoogleRedirectUri()
        };
    }

    getSecurityConfig() {
        return {
            jwtSecret: this._getEnvVar('JWT_SECRET', this.isDevelopment ? 'dev-secret-key' : ''),
            encryptionKey: this._getEnvVar('ENCRYPTION_KEY'),
            debug: this.isDevelopment.toString()
        };
    }

    // Frontend-specific methods
    getNextPublicConfig() {
        return {
            apiUrl: this._getEnvVar('NEXT_PUBLIC_API_URL') || this.getFrontendUrl(),
            oauthUrl: this._getEnvVar('NEXT_PUBLIC_OAUTH_URL') || this.getOAuthServiceUrl(),
            mcpUrl: this._getEnvVar('NEXT_PUBLIC_MCP_URL') || this.getMCPProxyUrl()
        };
    }

    printConfigSummary() {
        console.log(`🌍 Environment: ${this.environment}`);
        console.log(`🔗 OAuth Service: ${this.getOAuthServiceUrl()}`);
        console.log(`🔧 MCP Proxy: ${this.getMCPProxyUrl()}`);
        console.log(`🌐 Frontend: ${this.getFrontendUrl()}`);
        console.log(`🔄 Redirect URI: ${this.getGoogleRedirectUri()}`);
        console.log(`🔒 CORS Origins: ${this.getCorsOrigins().length} configured`);
        console.log(`🐛 Debug Mode: ${this.isDevelopment}`);
    }
}

// Global instance for easy importing
const envConfig = new EnvironmentConfig();

// Convenience functions for backward compatibility
function getEnvironment() {
    return envConfig.environment;
}

function isDevelopment() {
    return envConfig.isDevelopment;
}

function isProduction() {
    return envConfig.isProduction;
}

function getOAuthServiceUrl() {
    return envConfig.getOAuthServiceUrl();
}

function getMCPProxyUrl() {
    return envConfig.getMCPProxyUrl();
}

function getFrontendUrl() {
    return envConfig.getFrontendUrl();
}

function getCorsOrigins() {
    return envConfig.getAllCorsOrigins();
}

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
    // Node.js
    module.exports = {
        Environment,
        EnvironmentConfig,
        envConfig,
        getEnvironment,
        isDevelopment,
        isProduction,
        getOAuthServiceUrl,
        getMCPProxyUrl,
        getFrontendUrl,
        getCorsOrigins
    };
} else if (typeof window !== 'undefined') {
    // Browser
    window.EnvironmentConfig = EnvironmentConfig;
    window.envConfig = envConfig;
}

// Print configuration when run directly in Node.js
if (typeof require !== 'undefined' && require.main === module) {
    envConfig.printConfigSummary();
}
