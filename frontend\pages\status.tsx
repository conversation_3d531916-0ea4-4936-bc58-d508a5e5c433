import React, { useState, useEffect } from 'react';
import { GetServerSideProps } from 'next';
import { UruLogo } from '../components/shared/UruLogo';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface TestResult {
  service: string;
  status: 'success' | 'error';
  message: string;
  data?: any;
}

interface TestResponse {
  success: boolean;
  results: TestResult[];
  timestamp: string;
}

export default function StatusPage() {
  const [testResults, setTestResults] = useState<TestResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    runTests();
  }, []);

  const runTests = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-backend');
      const data = await response.json();
      setTestResults(data);
    } catch (error) {
      console.error('Failed to run tests:', error);
      setTestResults({
        success: false,
        results: [{
          service: 'Frontend',
          status: 'error',
          message: 'Failed to run backend tests'
        }],
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-600/30 bg-green-600/10';
      case 'error':
        return 'border-red-600/30 bg-red-600/10';
      default:
        return 'border-yellow-600/30 bg-yellow-600/10';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-6">
            <UruLogo size="lg" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            System Status
          </h1>
          <p className="text-gray-400">
            Backend service connectivity and health checks
          </p>
        </div>

        {/* Refresh Button */}
        <div className="flex justify-center mb-8">
          <button
            onClick={runTests}
            disabled={isLoading}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>{isLoading ? 'Testing...' : 'Refresh Status'}</span>
          </button>
        </div>

        {/* Overall Status */}
        {testResults && (
          <div className={`rounded-lg p-6 mb-8 border ${getStatusColor(testResults.success ? 'success' : 'error')}`}>
            <div className="flex items-center space-x-3">
              {getStatusIcon(testResults.success ? 'success' : 'error')}
              <div>
                <h2 className="text-xl font-semibold text-white">
                  {testResults.success ? 'All Systems Operational' : 'System Issues Detected'}
                </h2>
                <p className="text-gray-400 text-sm">
                  Last checked: {new Date(testResults.timestamp).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Service Status */}
        {testResults && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white mb-4">Service Details</h3>
            
            {testResults.results.map((result, index) => (
              <div key={index} className={`rounded-lg p-6 border ${getStatusColor(result.status)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <h4 className="text-white font-medium">{result.service}</h4>
                      <p className="text-gray-300 text-sm mt-1">{result.message}</p>
                      
                      {result.data && (
                        <details className="mt-3">
                          <summary className="text-gray-400 text-xs cursor-pointer hover:text-gray-300">
                            View Details
                          </summary>
                          <pre className="mt-2 text-xs text-gray-400 bg-gray-800 p-3 rounded overflow-x-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                  
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    result.status === 'success' 
                      ? 'bg-green-600/20 text-green-400' 
                      : 'bg-red-600/20 text-red-400'
                  }`}>
                    {result.status.toUpperCase()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Loading State */}
        {isLoading && !testResults && (
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-400">Running system tests...</p>
          </div>
        )}

        {/* Navigation */}
        <div className="text-center mt-8">
          <a 
            href="/login" 
            className="text-blue-400 hover:text-blue-300 transition-colors"
          >
            ← Back to Login
          </a>
        </div>
      </div>
    </div>
  );
}

// Prevent static generation for status page
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {}
  };
};
