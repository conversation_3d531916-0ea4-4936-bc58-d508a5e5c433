'use client';

// components/connections/AppConnectionManager.tsx

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, AlertCircle, Clock, Settings, Zap, Trash2, RefreshCw, ExternalLink } from 'lucide-react';

interface App {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  status: 'connected' | 'setup_required' | 'pending_approval' | 'error' | 'available';
  connectedUsers?: number;
  permissions?: string[];
  lastSync?: Date;
  features?: string[];
}

export const AppConnectionManager: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [apps, setApps] = useState<App[]>([
    {
      id: 'google-drive',
      name: 'Google Drive',
      description: 'File storage and document management',
      category: 'productivity',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-blue-500">
        <path d="M13.724 6.942L20.578 18.3h-7.13l-3.563-6.173zM8.397 3.6H15.6l6.854 11.874-3.427 5.938H1.543zM2.802 15.225L6.23 9.288l3.428 5.937z"/>
      </svg>,
      status: 'connected',
      connectedUsers: 12,
      permissions: ['Read files', 'Create files', 'Share files'],
      lastSync: new Date(Date.now() - 300000),
      features: ['Document search', 'Real-time collaboration', 'Version history']
    },
    {
      id: 'gmail',
      name: 'Gmail',
      description: 'Email communication and management',
      category: 'communication',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-red-500">
        <path d="M24 4.5v15c0 .825-.675 1.5-1.5 1.5H1.5C.675 21 0 20.325 0 19.5v-15c0-.428.183-.828.48-1.11L12 11.5l11.52-8.11c.297-.282.48-.682.48-1.11zM1.5 4h21L12 12 1.5 4z"/>
      </svg>,
      status: 'connected',
      connectedUsers: 8,
      permissions: ['Read emails', 'Send emails', 'Create labels'],
      lastSync: new Date(Date.now() - 180000),
      features: ['Email search', 'Smart compose', 'Thread management']
    },
    {
      id: 'google-calendar',
      name: 'Google Calendar',
      description: 'Schedule management and event planning',
      category: 'productivity',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-blue-600">
        <path d="M5.455 3.727V1.09h13.09v2.637h-13.09zM19.636 6.364V24H4.364V6.364h15.272zM12 10.182a1.09 1.09 0 1 0 0 2.182 1.09 1.09 0 0 0 0-2.182z"/>
      </svg>,
      status: 'connected',
      connectedUsers: 10,
      permissions: ['Read events', 'Create events', 'Manage attendees'],
      lastSync: new Date(Date.now() - 120000),
      features: ['Meeting scheduling', 'Availability tracking', 'Event reminders']
    },
    {
      id: 'slack',
      name: 'Slack',
      description: 'Team communication and collaboration',
      category: 'communication',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-purple-500">
        <path d="M9.34 20.318a2.91 2.91 0 0 1 0-5.817 2.91 2.91 0 0 1 0 5.817zm0-7.272a2.91 2.91 0 0 1 0-5.818 2.91 2.91 0 0 1 0 5.818zm-2.91-2.91a2.91 2.91 0 1 1-5.817 0 2.91 2.91 0 0 1 5.817 0zm7.272 0a2.91 2.91 0 1 1-5.818 0 2.91 2.91 0 0 1 5.818 0zm-2.91 2.91a2.91 2.91 0 1 1 0 5.817 2.91 2.91 0 0 1 0-5.817zm0 7.272a2.91 2.91 0 1 1 0 5.818 2.91 2.91 0 0 1 0-5.818zm2.91 2.91a2.91 2.91 0 1 1 5.817 0 2.91 2.91 0 0 1-5.817 0zm-7.272 0a2.91 2.91 0 1 1 5.818 0 2.91 2.91 0 0 1-5.818 0z"/>
      </svg>,
      status: 'connected',
      connectedUsers: 15,
      permissions: ['Read messages', 'Send messages', 'Manage channels'],
      lastSync: new Date(Date.now() - 60000),
      features: ['Channel management', 'Direct messaging', 'File sharing']
    },
    {
      id: 'webflow',
      name: 'Webflow',
      description: 'Website design and content management',
      category: 'design',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-blue-400">
        <path d="M23.94 1.637v8.941h-8.08V1.637h8.08zM15.86 12.214v10.149h8.08V12.214h-8.08zM0 16.942l7.94-1.92V0H0v16.942zM7.94 24l-7.94-1.92v-3.216l7.94 1.92V24z"/>
      </svg>,
      status: 'setup_required',
      features: ['Site management', 'Content updates', 'Analytics tracking']
    },
    {
      id: 'github',
      name: 'GitHub',
      description: 'Code repository and version control',
      category: 'development',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-gray-800">
        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.043c-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.745.084-.729.084-.729 1.205.084 1.838 1.237 1.237 1.237 1.07 1.834 2.807 1.304 3.492.997.108-.776.417-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
      </svg>,
      status: 'pending_approval',
      features: ['Repository access', 'Issue tracking', 'Pull request management']
    },
    {
      id: 'notion',
      name: 'Notion',
      description: 'Note-taking and knowledge management',
      category: 'productivity',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-gray-700">
        <path d="M3.6 2.4c0-.66.54-1.2 1.2-1.2h14.4c.66 0 1.2.54 1.2 1.2v19.2c0 .66-.54 1.2-1.2 1.2H4.8c-.66 0-1.2-.54-1.2-1.2V2.4zm1.2 18h2.09l.36-1.12h4.5l.36 1.12h2.09L9.6 4H7.2L4.8 20.4zM9 13.51l1.83-5.62h.12l1.83 5.62H9z"/>
      </svg>,
      status: 'available',
      features: ['Page management', 'Database access', 'Template sharing']
    },
    {
      id: 'hubspot',
      name: 'HubSpot',
      description: 'CRM and marketing automation',
      category: 'sales',
      icon: <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 fill-current text-orange-500">
        <path d="M12 0c6.627 0 12 5.373 12 12s-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0zm0 2.4c-5.302 0-9.6 4.298-9.6 9.6s4.298 9.6 9.6 9.6 9.6-4.298 9.6-9.6-4.298-9.6-9.6-9.6zM9.6 7.2v9.6h4.8V7.2H9.6zm2.4 4.8a2.4 2.4 0 1 1 0-4.8 2.4 2.4 0 0 1 0 4.8z"/>
      </svg>,
      status: 'available',
      features: ['Contact management', 'Deal tracking', 'Marketing campaigns']
    }
  ]);

  const categories = [
    { id: 'all', name: 'All Apps', count: apps.length },
    { id: 'productivity', name: 'Productivity', count: apps.filter(app => app.category === 'productivity').length },
    { id: 'communication', name: 'Communication', count: apps.filter(app => app.category === 'communication').length },
    { id: 'sales', name: 'Sales & CRM', count: apps.filter(app => app.category === 'sales').length },
    { id: 'design', name: 'Design', count: apps.filter(app => app.category === 'design').length },
    { id: 'development', name: 'Development', count: apps.filter(app => app.category === 'development').length }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'setup_required': return <AlertCircle className="w-4 h-4 text-orange-500" />;
      case 'pending_approval': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'available': return <Zap className="w-4 h-4 text-gray-400" />;
      default: return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return 'Connected';
      case 'setup_required': return 'Setup Required';
      case 'pending_approval': return 'Pending Approval';
      case 'error': return 'Connection Error';
      case 'available': return 'Available';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'setup_required': return 'bg-orange-100 text-orange-800';
      case 'pending_approval': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'available': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleConnect = (appId: string) => {
    setApps(prev => prev.map(app => 
      app.id === appId 
        ? { ...app, status: 'pending_approval' as const }
        : app
    ));
  };

  const handleDisconnect = (appId: string) => {
    setApps(prev => prev.map(app => 
      app.id === appId 
        ? { ...app, status: 'available' as const, connectedUsers: 0, permissions: undefined }
        : app
    ));
  };

  const filteredApps = selectedCategory === 'all' 
    ? apps 
    : apps.filter(app => app.category === selectedCategory);

  return (
    <div className="bg-gray-900 min-h-screen">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-white mb-2">App Connections</h1>
          <p className="text-gray-400">Manage your workspace integrations and data sources</p>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>

        {/* Apps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredApps.map((app) => (
            <div key={app.id} className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-colors">
              {/* App Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {app.icon}
                  <div>
                    <h3 className="text-white font-semibold">{app.name}</h3>
                    <p className="text-gray-400 text-sm">{app.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {getStatusIcon(app.status)}
                </div>
              </div>

              {/* Status Badge */}
              <div className="mb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>
                  {getStatusText(app.status)}
                </span>
              </div>

              {/* Connection Info */}
              {app.connectedUsers && (
                <div className="mb-4">
                  <p className="text-gray-400 text-sm">
                    {app.connectedUsers} users connected
                  </p>
                  {app.lastSync && (
                    <p className="text-gray-500 text-xs">
                      Last sync: {app.lastSync.toLocaleTimeString()}
                    </p>
                  )}
                </div>
              )}

              {/* Permissions */}
              {app.permissions && app.permissions.length > 0 && (
                <div className="mb-4">
                  <p className="text-gray-300 text-sm mb-2">Permissions:</p>
                  <div className="flex flex-wrap gap-1">
                    {app.permissions.map((permission, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Features */}
              {app.features && app.features.length > 0 && (
                <div className="mb-4">
                  <p className="text-gray-300 text-sm mb-2">Features:</p>
                  <ul className="text-gray-400 text-xs space-y-1">
                    {app.features.map((feature, index) => (
                      <li key={index}>• {feature}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2 mt-6">
                {app.status === 'connected' && (
                  <>
                    <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1">
                      <Settings className="w-4 h-4" />
                      <span>Manage</span>
                    </button>
                    <button 
                      onClick={() => handleDisconnect(app.id)}
                      className="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors"
                      title="Disconnect"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </>
                )}
                
                {app.status === 'setup_required' && (
                  <button className="flex-1 bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1">
                    <Settings className="w-4 h-4" />
                    <span>Setup Required</span>
                  </button>
                )}
                
                {app.status === 'pending_approval' && (
                  <button disabled className="flex-1 bg-yellow-600 text-white px-3 py-2 rounded-lg text-sm opacity-75 cursor-not-allowed flex items-center justify-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>Pending Approval</span>
                  </button>
                )}
                
                {app.status === 'available' && (
                  <button 
                    onClick={() => handleConnect(app.id)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1"
                  >
                    <Zap className="w-4 h-4" />
                    <span>Connect</span>
                  </button>
                )}
                
                {app.status === 'error' && (
                  <button className="flex-1 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1">
                    <RefreshCw className="w-4 h-4" />
                    <span>Retry</span>
                  </button>
                )}

                <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg text-sm transition-colors" title="Learn More">
                  <ExternalLink className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Add more apps section */}
        <div className="mt-12 text-center">
          <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
            <h3 className="text-white text-lg font-semibold mb-2">Need more integrations?</h3>
            <p className="text-gray-400 mb-4">
              We're constantly adding new integrations. Request a specific app or explore our marketplace.
            </p>
            <div className="flex justify-center space-x-4">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                Request Integration
              </button>
              <button className="bg-gray-700 hover:bg-gray-600 text-gray-300 px-4 py-2 rounded-lg text-sm transition-colors">
                Browse Marketplace
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};