-- ===========================================
-- RATE LIMITING TABLE CREATION
-- Persistent storage for rate limiting to prevent bypass on service restarts
-- ===========================================

-- Create rate_limit_requests table for tracking API usage
CREATE TABLE IF NOT EXISTS rate_limit_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID NOT NULL,
  workspace_id UUID,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  endpoint VARCHAR(100) NOT NULL DEFAULT 'tool_execution',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_rate_limit_employee_timestamp ON rate_limit_requests(employee_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_rate_limit_workspace_timestamp ON rate_limit_requests(workspace_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_rate_limit_timestamp ON rate_limit_requests(timestamp);
CREATE INDEX IF NOT EXISTS idx_rate_limit_endpoint ON rate_limit_requests(endpoint);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_rate_limit_employee_endpoint ON rate_limit_requests(employee_id, endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limit_workspace_endpoint ON rate_limit_requests(workspace_id, endpoint);

-- Add foreign key constraints
ALTER TABLE rate_limit_requests 
ADD CONSTRAINT fk_rate_limit_employee_id 
FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE;

-- Add check constraints for data validation
ALTER TABLE rate_limit_requests 
ADD CONSTRAINT chk_rate_limit_endpoint_not_empty 
CHECK (length(trim(endpoint)) > 0);

-- Create RLS (Row Level Security) policies for multi-tenant isolation
ALTER TABLE rate_limit_requests ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see rate limit records from their own workspace
CREATE POLICY rate_limit_workspace_isolation ON rate_limit_requests
  FOR ALL
  USING (
    workspace_id IN (
      SELECT workspace_id 
      FROM employees 
      WHERE id = auth.uid()
    )
  );

-- Policy: Service accounts can access all rate limit records (for system operations)
CREATE POLICY rate_limit_service_access ON rate_limit_requests
  FOR ALL
  TO service_role
  USING (true);

-- Create a view for rate limit monitoring
CREATE OR REPLACE VIEW rate_limit_summary AS
SELECT 
  workspace_id,
  endpoint,
  COUNT(*) as request_count,
  COUNT(DISTINCT employee_id) as unique_employees,
  MIN(timestamp) as first_request,
  MAX(timestamp) as last_request,
  DATE_TRUNC('hour', timestamp) as hour_bucket
FROM rate_limit_requests
WHERE timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY workspace_id, endpoint, DATE_TRUNC('hour', timestamp)
ORDER BY hour_bucket DESC;

-- Grant appropriate permissions
GRANT SELECT ON rate_limit_requests TO authenticated;
GRANT SELECT ON rate_limit_summary TO authenticated;
GRANT ALL ON rate_limit_requests TO service_role;

-- Create function to clean up old rate limit records (retention policy)
CREATE OR REPLACE FUNCTION cleanup_old_rate_limit_records(retention_hours INTEGER DEFAULT 24)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM rate_limit_requests 
  WHERE timestamp < NOW() - INTERVAL '1 hour' * retention_hours;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a scheduled job to run cleanup every hour (requires pg_cron extension)
-- SELECT cron.schedule('rate-limit-cleanup', '0 * * * *', 'SELECT cleanup_old_rate_limit_records(24);');

-- Create function to get current rate limit status for an employee
CREATE OR REPLACE FUNCTION get_rate_limit_status(
  p_employee_id UUID,
  p_window_hours INTEGER DEFAULT 1,
  p_limit INTEGER DEFAULT 100
)
RETURNS TABLE(
  current_count INTEGER,
  limit_value INTEGER,
  remaining INTEGER,
  window_start TIMESTAMP WITH TIME ZONE,
  window_end TIMESTAMP WITH TIME ZONE,
  is_limited BOOLEAN
) AS $$
DECLARE
  window_start_time TIMESTAMP WITH TIME ZONE;
  current_request_count INTEGER;
BEGIN
  window_start_time := NOW() - INTERVAL '1 hour' * p_window_hours;
  
  SELECT COUNT(*)
  INTO current_request_count
  FROM rate_limit_requests
  WHERE employee_id = p_employee_id
    AND timestamp >= window_start_time;
  
  RETURN QUERY SELECT
    current_request_count,
    p_limit,
    GREATEST(0, p_limit - current_request_count),
    window_start_time,
    NOW(),
    current_request_count >= p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON TABLE rate_limit_requests IS 'Persistent storage for API rate limiting with workspace isolation';
COMMENT ON COLUMN rate_limit_requests.endpoint IS 'API endpoint or operation type being rate limited';
COMMENT ON COLUMN rate_limit_requests.ip_address IS 'Client IP address for additional rate limiting dimensions';
COMMENT ON FUNCTION cleanup_old_rate_limit_records IS 'Removes rate limit records older than specified retention period';
COMMENT ON FUNCTION get_rate_limit_status IS 'Returns current rate limit status for an employee within a time window';
