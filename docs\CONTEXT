Uru Workspace Platform - Elestio Deployment Context
Platform Overview
Uru Enterprises LLC is building a multi-tenant AI-powered workspace intelligence platform that connects SMB companies (typically $1M-$50M revenue) to their data through AI agents and MCP (Model Context Protocol) tools.
Core Business Model

Target Market: SMB companies needing AI-powered business intelligence
Service Model: Consulting-to-platform transition
Value Proposition: Each company gets a workspace where employees can connect personal accounts (Gmail, Drive, Calendar) and access custom company-specific AI tools

Platform Architecture
Employee Experience:
├── Personal Tools: Gmail, Drive, Calendar (via OAuth connections)
├── Company Tools: Custom n8n workflows (call transcripts, client databases)
├── AI Chat Interface: Claude/OpenAI integration with tool access
└── Multi-channel Access: Web app, Claude Desktop, Slack, email
Elestio Deployment Infrastructure
Production Environment

Hosting Provider: Elestio cloud infrastructure
Deployment Pattern: Docker Compose multi-service architecture
Domain Structure: Multiple custom domains for different services

Service Architecture
Production Services:
├── Frontend (Next.js): app.uruenterprises.com
├── Auth Service (FastAPI): auth.uruenterprises.com
├── Integration Service (FastAPI): integrations.uruenterprises.com
├── Composio Service (FastAPI): (internal only)
├── MCP Proxy (Node.js): mcp.uruenterprises.com
├── N8N Workflows: n8n-uru-u46170.vm.elestio.app
└── Database: Supabase PostgreSQL
Environment Variables Configuration
Database & Backend Services
env# Supabase Database Configuration
SUPABASE_URL=https://sipvdxjupgnymlshsoro.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.iY-BFFxDXkbI1qiPGqK9u9Y-F5MWuYptTsmSlLuehIs
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# OAuth Service Configuration (FastAPI)
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=false
Authentication & Security
env# Google OAuth Configuration
GOOGLE_CLIENT_ID=949099265240-g5p5m29mdpr2jisrbmbkksm84f9q89bv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://oauth.uruenterprises.com/oauth/google/callback

# JWT Token Management
JWT_SECRET=uru-workspace-platform-super-secret-jwt-key-2025
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Encryption for OAuth Token Storage
ENCRYPTION_KEY=jeUI2_9ZoSiCyETMA45CyEEaJhtbmCO909bpUFqu3Ks=
AI & MCP Integration
env# OpenAI API Integration
OPENAI_API_KEY=********************************************************************************************************************************************************************

# N8N MCP Server Integration
N8N_SSE_URL=https://n8n-uru-u46170.vm.elestio.app/mcp/4caa0a7f-1251-45a8-97a5-7663841a2c9b/sse
Frontend & Service URLs
env# Frontend Configuration (Next.js)
NEXT_PUBLIC_API_URL=https://oauth.uruenterprises.com
NEXT_PUBLIC_OAUTH_URL=https://oauth.uruenterprises.com
NEXT_PUBLIC_MCP_URL=https://mcp.uruenterprises.com

# Internal Service Communication
OAUTH_SERVICE_URL=http://oauth-service:8000
FRONTEND_URL=https://app.uruenterprises.com
Multi-Tenant Architecture
Workspace Structure
Each SMB client gets their own workspace with:

Custom Domain: workspace-slug.uruenterprises.com (optional)
Employee Management: Role-based access control
Tool Configuration: Personal tools (OAuth) + company tools (custom n8n)
Data Isolation: Complete separation between client workspaces

Current Production Clients

Ignition Consultants: 60+ sub-clients, fractional CFO services
9 Custom MCP Tools: Call transcripts, client databases, document analysis
Live Implementation: Proven multi-tenant architecture

Technical Stack


Deployment & DevOps

Containerization: Docker Compose for multi-service deployment
SSL/TLS: Automatic certificate management via Elestio
Monitoring: Elestio infrastructure monitoring + custom application logs
Backup: Automated database backups via Supabase

Security & Compliance
OAuth Token Management

Encryption: All OAuth tokens encrypted at rest using ENCRYPTION_KEY
Scoping: Tokens scoped to specific workspaces and employees
Refresh: Automatic token refresh handling
Audit: Complete audit trail for all OAuth operations

Data Privacy

Workspace Isolation: Complete data separation between clients
Employee Data: Personal OAuth tokens never shared between workspaces
Company Data: Custom n8n tools operate only within workspace boundaries
Compliance: GDPR-ready data handling and deletion capabilities

Development Workflow
Environment Management

Production: Elestio-managed environment variables
Development: Local .env files mirroring production structure
Staging: Separate Elestio service with staging environment variables

Service Dependencies
yaml# Docker Compose service dependency order
1. Database (Supabase) - External service
2. OAuth Service - Depends on database
3. MCP Proxy - Depends on OAuth service + N8N
4. Frontend - Depends on OAuth service + MCP proxy
5. N8N Workflows - Independent but integrated via MCP proxy
Current Implementation Status
✅ Production Ready

Multi-tenant workspace management
Google OAuth integration for employee personal accounts
Custom n8n MCP tools for company-specific data
Real-time AI chat interface with tool access
Complete frontend application with admin dashboards

🔄 In Development

Setup automation for OAuth app creation
Additional personal tool integrations (potentially via Composio)
Enhanced permission management
Multi-provider AI support (OpenAI + Anthropic)

📋 Planned Features

SSO integration for enterprise clients
Advanced analytics and usage tracking
API access for third-party integrations
White-label deployment options

This Elestio deployment represents a production-grade multi-tenant AI platform serving real SMB clients with measurable business value through intelligent automation and data access.