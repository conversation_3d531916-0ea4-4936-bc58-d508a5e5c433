# Uru Workspace Platform Environment Configuration
# Copy this file to .env for local development

# ===========================================
# ENVIRONMENT DETECTION
# ===========================================
NODE_ENV=development
ENVIRONMENT=development
DEBUG=true

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
# Replace with your actual Supabase credentials
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# ===========================================
# AUTHENTICATION & SECURITY
# ===========================================
# Use secure values even for development
JWT_SECRET=dev-jwt-secret-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=480
ENCRYPTION_KEY=dev-encryption-key-change-this-in-production

# ===========================================
# GOOGLE OAUTH CONFIGURATION
# ===========================================
# Replace with your actual Google OAuth credentials
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret

# Development redirect URIs (localhost)
GOOGLE_REDIRECT_URI_AUTH=http://localhost:8003/oauth/google/callback
GOOGLE_REDIRECT_URI_INTEGRATIONS=http://localhost:8002/oauth/google/callback

# ===========================================
# COMPOSIO INTEGRATION
# ===========================================
URU_COMPOSIO_API_KEY=your-composio-api-key
URU_COMPOSIO_BASE_URL=https://backend.composio.dev/api

# ===========================================
# SERVICE URLS (LOCALHOST DEFAULTS)
# ===========================================
# These will be automatically used for development
FRONTEND_URL=http://localhost:3000
AUTH_SERVICE_URL=http://localhost:8003
INTEGRATIONS_SERVICE_URL=http://localhost:8002
COMPOSIO_SERVICE_URL=http://localhost:8001
MCP_PROXY_URL=http://localhost:3001

# ===========================================
# FRONTEND CONFIGURATION
# ===========================================
# Next.js public environment variables for development
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_AUTH_URL=http://localhost:8003
NEXT_PUBLIC_INTEGRATIONS_URL=http://localhost:8002
NEXT_PUBLIC_MCP_URL=http://localhost:3001

# ===========================================
# CORS CONFIGURATION (DEVELOPMENT)
# ===========================================
# Localhost origins for development - New Architecture
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8001,http://localhost:8002,http://localhost:8003,http://127.0.0.1:3000,http://127.0.0.1:3001,http://127.0.0.1:8001,http://127.0.0.1:8002,http://127.0.0.1:8003

# ===========================================
# EXTERNAL INTEGRATIONS
# ===========================================
# Replace with your actual endpoints
N8N_SSE_URL=your-n8n-sse-endpoint-url
OPENAI_API_KEY=your-openai-api-key

# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
APP_HOST=0.0.0.0
PORT=3000
HOSTNAME=0.0.0.0

# ===========================================
# DEVELOPMENT SETUP INSTRUCTIONS
# ===========================================
# 1. Copy this file to .env in the root directory
# 2. Replace placeholder values with your actual credentials
# 3. Ensure Google OAuth app is configured with localhost redirect URIs:
#    - http://localhost:8003/oauth/google/callback (auth service)
#    - http://localhost:8002/oauth/google/callback (integration service)
# 4. Run: docker-compose -f docker-compose.dev.yml up --build
# 5. Access frontend at: http://localhost:3000

# ===========================================
# GOOGLE OAUTH SETUP FOR DEVELOPMENT
# ===========================================
# In Google Cloud Console, add these redirect URIs:
# - http://localhost:8003/oauth/google/callback (auth service)
# - http://localhost:8002/oauth/google/callback (integration service)
# - https://auth.uruenterprises.com/oauth/google/callback (production)
# - https://integrations.uruenterprises.com/oauth/google/callback (production)
#
# Required scopes:
# - https://www.googleapis.com/auth/gmail.readonly
# - https://www.googleapis.com/auth/gmail.send
# - https://www.googleapis.com/auth/drive.readonly
# - https://www.googleapis.com/auth/drive.metadata.readonly
# - https://www.googleapis.com/auth/calendar.readonly
# - https://www.googleapis.com/auth/calendar.events

# ===========================================
# PRODUCTION CONFIGURATION REFERENCE
# ===========================================
# These values are set in Elestio environment variables for production
# Do not modify these for local development

# NODE_ENV=production
# ENVIRONMENT=production
# DEBUG=false
# FRONTEND_URL=https://app.uruenterprises.com
# AUTH_SERVICE_URL=https://auth.uruenterprises.com
# INTEGRATIONS_SERVICE_URL=https://integrations.uruenterprises.com
# MCP_PROXY_URL=https://mcp.uruenterprises.com
# COMPOSIO_SERVICE_URL=http://composio-service:8001
# NEXT_PUBLIC_API_URL=https://app.uruenterprises.com
# NEXT_PUBLIC_AUTH_URL=https://auth.uruenterprises.com
# NEXT_PUBLIC_INTEGRATIONS_URL=https://integrations.uruenterprises.com
# NEXT_PUBLIC_MCP_URL=https://mcp.uruenterprises.com
# CORS_ORIGINS=https://app.uruenterprises.com,https://auth.uruenterprises.com,https://integrations.uruenterprises.com,https://mcp.uruenterprises.com

# ===========================================
# SECURITY NOTES
# ===========================================
# - This file contains localhost URLs only for development
# - Never commit actual production credentials to version control
# - Use different JWT_SECRET and ENCRYPTION_KEY for production
# - Production URLs must be set in Elestio environment variables
# - The environment detection system will automatically use these localhost defaults
