#!/usr/bin/env python3
"""
OAuth Migration Test Script
Tests the migration by creating sample data and verifying the migration process
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List
from supabase import create_client, Client
from cryptography.fernet import Fernet
import uuid
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Setup logging with UTF-8 encoding for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration_test.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OAuthMigrationTest:
    def __init__(self):
        """Initialize test with Supabase connection and encryption"""
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.encryption_key = os.getenv("ENCRYPTION_KEY")
        
        if not all([self.supabase_url, self.supabase_key, self.encryption_key]):
            raise ValueError("Missing required environment variables: SUPABASE_URL, SUPABASE_KEY, ENCRYPTION_KEY")
        
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        self.cipher_suite = Fernet(self.encryption_key.encode())
        
        # Test data tracking
        self.test_employee_ids = []
        self.test_records_created = []
        
        logger.info("🔧 Migration test initialized successfully")
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt data using Fernet encryption"""
        return self.cipher_suite.encrypt(data.encode()).decode()
    
    def create_test_employee(self) -> str:
        """Create a test employee for migration testing"""
        try:
            test_employee = {
                "id": str(uuid.uuid4()),
                "email": f"test-migration-{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
                "name": "Migration Test User",
                "workspace_id": str(uuid.uuid4()),
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            # Insert test employee
            result = self.supabase.table('employees').insert(test_employee).execute()
            
            if result.data:
                employee_id = result.data[0]['id']
                self.test_employee_ids.append(employee_id)
                logger.info(f"✅ Created test employee: {employee_id}")
                return employee_id
            else:
                raise Exception("Failed to create test employee")
                
        except Exception as e:
            logger.error(f"❌ Failed to create test employee: {e}")
            raise
    
    def create_test_composio_connections(self, employee_id: str, count: int = 3) -> List[Dict]:
        """Create test composio_connections records"""
        try:
            test_apps = ['gmail', 'drive', 'calendar']
            created_records = []
            
            for i, app_name in enumerate(test_apps[:count]):
                record = {
                    "id": str(uuid.uuid4()),
                    "employee_id": employee_id,
                    "app_name": app_name,
                    "status": "active",
                    "access_token": self.encrypt_data(f"test_access_token_{app_name}_{i}"),
                    "refresh_token": self.encrypt_data(f"test_refresh_token_{app_name}_{i}"),
                    "expires_at": (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat(),
                    "user_info": {
                        "id": f"google_user_{i}",
                        "email": f"user{i}@gmail.com",
                        "name": f"Test User {i}"
                    },
                    "scopes": [f"https://www.googleapis.com/auth/{app_name}"],
                    "metadata": {
                        "test_record": True,
                        "created_for_migration_test": True,
                        "app_name": app_name
                    },
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                # Insert record
                result = self.supabase.table('composio_connections').insert(record).execute()
                
                if result.data:
                    created_records.append(result.data[0])
                    self.test_records_created.append(('composio_connections', result.data[0]['id']))
                    logger.info(f"✅ Created test composio_connections record: {app_name}")
                else:
                    logger.error(f"❌ Failed to create composio_connections record for {app_name}")
            
            return created_records
            
        except Exception as e:
            logger.error(f"❌ Failed to create test composio_connections: {e}")
            raise
    
    def run_migration_test(self) -> bool:
        """Run the actual migration on test data"""
        try:
            logger.info("🔄 Running migration on test data...")
            
            # Import and run the migration
            sys.path.append(os.path.dirname(__file__))
            import migrate_oauth_storage

            migration = migrate_oauth_storage.OAuthMigration()
            
            # Run migration
            migrated_count = migration.migrate_composio_to_oauth()
            
            logger.info(f"✅ Migration completed: {migrated_count} records migrated")
            return migrated_count > 0
            
        except Exception as e:
            logger.error(f"❌ Migration test failed: {e}")
            return False
    
    def verify_migration_results(self, employee_id: str, expected_count: int) -> bool:
        """Verify that migration results are correct"""
        try:
            logger.info("🔍 Verifying migration results...")
            
            # Check oauth_tokens for migrated records
            oauth_result = self.supabase.table('oauth_tokens').select("*").eq(
                'employee_id', employee_id
            ).like('provider', 'composio_%').execute()
            
            migrated_records = oauth_result.data
            
            logger.info(f"📊 Migration verification:")
            logger.info(f"   - Expected records: {expected_count}")
            logger.info(f"   - Migrated records: {len(migrated_records)}")
            
            # Verify each migrated record
            for record in migrated_records:
                app_name = record['provider'].replace('composio_', '')
                logger.info(f"   - Migrated {app_name}: ✅")
                
                # Verify metadata contains migration info
                metadata = record.get('metadata', {})
                if metadata.get('migrated_from') != 'composio_connections':
                    logger.warning(f"   - Missing migration metadata for {app_name}")
                    return False
            
            success = len(migrated_records) == expected_count
            
            if success:
                logger.info("✅ Migration verification passed")
            else:
                logger.error("❌ Migration verification failed")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Migration verification failed: {e}")
            return False
    
    def cleanup_test_data(self):
        """Clean up test data created during testing"""
        try:
            logger.info("🧹 Cleaning up test data...")
            
            # Delete test records
            for table, record_id in self.test_records_created:
                try:
                    self.supabase.table(table).delete().eq('id', record_id).execute()
                    logger.info(f"✅ Deleted test record from {table}: {record_id}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to delete test record {record_id}: {e}")
            
            # Delete migrated oauth_tokens for test employees
            for employee_id in self.test_employee_ids:
                try:
                    self.supabase.table('oauth_tokens').delete().eq(
                        'employee_id', employee_id
                    ).like('provider', 'composio_%').execute()
                    logger.info(f"✅ Deleted migrated oauth_tokens for employee: {employee_id}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to delete oauth_tokens for {employee_id}: {e}")
            
            # Delete test employees
            for employee_id in self.test_employee_ids:
                try:
                    self.supabase.table('employees').delete().eq('id', employee_id).execute()
                    logger.info(f"✅ Deleted test employee: {employee_id}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to delete test employee {employee_id}: {e}")
            
            logger.info("🧹 Test data cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Test data cleanup failed: {e}")

def main():
    """Main test function"""
    logger.info("🧪 Starting OAuth Migration Test...")
    
    test = None
    try:
        test = OAuthMigrationTest()
        
        # Step 1: Create test employee
        logger.info("👤 Creating test employee...")
        employee_id = test.create_test_employee()
        
        # Step 2: Create test composio_connections
        logger.info("📝 Creating test composio_connections...")
        test_records = test.create_test_composio_connections(employee_id, 3)
        expected_count = len(test_records)
        
        # Step 3: Run migration test
        logger.info("🔄 Running migration test...")
        migration_success = test.run_migration_test()
        
        if not migration_success:
            logger.error("❌ Migration test failed")
            return False
        
        # Step 4: Verify results
        logger.info("🔍 Verifying migration results...")
        verification_success = test.verify_migration_results(employee_id, expected_count)
        
        if verification_success:
            logger.info("🎉 Migration test completed successfully!")
            print("\n✅ Test Results:")
            print(f"   - Test employee created: {employee_id}")
            print(f"   - Test records created: {expected_count}")
            print(f"   - Migration successful: ✅")
            print(f"   - Verification passed: ✅")
            print("\n📝 The migration is ready for production use!")
            return True
        else:
            logger.error("❌ Migration test verification failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Migration test failed: {e}")
        return False
        
    finally:
        # Always cleanup test data
        if test:
            test.cleanup_test_data()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
