'use client';

import React, { useState, useEffect } from 'react';
import { 
  Zap, 
  <PERSON>ting<PERSON>, 
  CheckCircle, 
  AlertCircle, 
  ExternalLink, 
  Loader2,
  Search,
  Filter,
  Grid,
  List,
  RefreshCw,
  Plus,
  Trash2
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  description: string;
  category: string;
  auth_type: string;
  tier: number;
  capabilities: string[];
  logo_url: string;
  is_enabled: boolean;
  documentation_url?: string;
}

interface Connection {
  id: string;
  name: string;
  category: string;
  logo_url: string;
  is_connected: boolean;
  is_expired: boolean;
  connected_at?: string;
  scopes: string[];
  metadata: any;
}

interface IntegrationHubProps {
  className?: string;
}

export const IntegrationHub: React.FC<IntegrationHubProps> = ({ className = '' }) => {
  const [integrations, setIntegrations] = useState<Record<string, Integration[]>>({});
  const [connections, setConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectingId, setConnectingId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showConnectedOnly, setShowConnectedOnly] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load available integrations and current connections in parallel
      const composioUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
      const token = localStorage.getItem('auth_token');

      const [integrationsResponse, connectionsResponse] = await Promise.all([
        fetch(`${composioUrl}/api/uru/integrations/available`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${composioUrl}/api/uru/integrations/connections`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (!integrationsResponse.ok || !connectionsResponse.ok) {
        throw new Error('Failed to load integration data');
      }

      const integrationsData = await integrationsResponse.json();
      const connectionsData = await connectionsResponse.json();

      if (integrationsData.success) {
        setIntegrations(integrationsData.categories);
      }

      if (connectionsData.success) {
        setConnections(connectionsData.connections);
      }

    } catch (err) {
      console.error('Failed to load integration data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load integrations');
    } finally {
      setLoading(false);
    }
  };

  const connectIntegration = async (integrationId: string) => {
    try {
      setConnectingId(integrationId);
      setError(null);

      const composioUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
      const token = localStorage.getItem('auth_token');

      const response = await fetch(`${composioUrl}/api/uru/integrations/connect/${integrationId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          redirect_url: `${window.location.origin}/app/settings`
        })
      });

      const result = await response.json();

      if (result.success && result.authorization_url) {
        // Redirect to OAuth authorization
        window.location.href = result.authorization_url;
      } else if (result.already_connected) {
        // Already connected, refresh data
        await loadData();
      } else {
        throw new Error(result.message || 'Failed to connect integration');
      }

    } catch (err) {
      console.error(`Failed to connect ${integrationId}:`, err);
      setError(err instanceof Error ? err.message : 'Failed to connect integration');
    } finally {
      setConnectingId(null);
    }
  };

  const disconnectIntegration = async (integrationId: string) => {
    try {
      setError(null);

      const composioUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
      const token = localStorage.getItem('auth_token');

      const response = await fetch(`${composioUrl}/api/uru/integrations/disconnect/${integrationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        await loadData(); // Refresh data
      } else {
        throw new Error(result.message || 'Failed to disconnect integration');
      }

    } catch (err) {
      console.error(`Failed to disconnect ${integrationId}:`, err);
      setError(err instanceof Error ? err.message : 'Failed to disconnect integration');
    }
  };

  const isConnected = (integrationId: string): Connection | null => {
    return connections.find(conn => conn.id === integrationId) || null;
  };

  const getFilteredIntegrations = () => {
    const allIntegrations: Integration[] = [];
    
    Object.values(integrations).forEach(categoryIntegrations => {
      allIntegrations.push(...categoryIntegrations);
    });

    return allIntegrations.filter(integration => {
      const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           integration.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || integration.category === selectedCategory;
      
      const matchesConnectionFilter = !showConnectedOnly || isConnected(integration.id);

      return matchesSearch && matchesCategory && matchesConnectionFilter;
    });
  };

  const getCategories = () => {
    return ['all', ...Object.keys(integrations)];
  };

  const getTierColor = (tier: number) => {
    switch (tier) {
      case 1: return 'text-green-400 bg-green-900/20 border-green-500/30';
      case 2: return 'text-blue-400 bg-blue-900/20 border-blue-500/30';
      case 3: return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30';
      case 4: return 'text-purple-400 bg-purple-900/20 border-purple-500/30';
      case 5: return 'text-gray-400 bg-gray-900/20 border-gray-500/30';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-500/30';
    }
  };

  const getTierLabel = (tier: number) => {
    switch (tier) {
      case 1: return 'Essential';
      case 2: return 'Business';
      case 3: return 'Productivity';
      case 4: return 'Specialized';
      case 5: return 'Advanced';
      default: return 'Standard';
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <Loader2 className="w-8 h-8 animate-spin text-blue-400" />
        <span className="ml-3 text-gray-300">Loading integrations...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Zap className="w-6 h-6 text-blue-400" />
          <h2 className="text-2xl font-bold text-white">Integration Hub</h2>
        </div>
        <button
          onClick={loadData}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
            <span className="text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="bg-gray-800 rounded-lg p-4 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search integrations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Category Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="pl-10 pr-8 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {getCategories().map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          {/* View Mode Toggle */}
          <div className="flex bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Connected Only Filter */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="connected-only"
            checked={showConnectedOnly}
            onChange={(e) => setShowConnectedOnly(e.target.checked)}
            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
          />
          <label htmlFor="connected-only" className="text-gray-300">
            Show connected only
          </label>
        </div>
      </div>

      {/* Integration Grid/List */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
        {getFilteredIntegrations().map(integration => {
          const connection = isConnected(integration.id);
          const isConnecting = connectingId === integration.id;

          if (viewMode === 'grid') {
            return (
              <div
                key={integration.id}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors"
              >
                {/* Integration Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <img
                      src={integration.logo_url}
                      alt={integration.name}
                      className="w-10 h-10 rounded-lg"
                      onError={(e) => {
                        e.currentTarget.src = '/api/placeholder/40/40';
                      }}
                    />
                    <div>
                      <h3 className="text-lg font-semibold text-white">{integration.name}</h3>
                      <span className={`inline-block px-2 py-1 text-xs rounded-full border ${getTierColor(integration.tier)}`}>
                        {getTierLabel(integration.tier)}
                      </span>
                    </div>
                  </div>

                  {/* Connection Status */}
                  <div className="flex items-center space-x-2">
                    {connection ? (
                      <div className="flex items-center space-x-1">
                        {connection.is_expired ? (
                          <div title="Connection expired">
                            <AlertCircle className="w-4 h-4 text-yellow-400" />
                          </div>
                        ) : (
                          <div title="Connected">
                            <CheckCircle className="w-4 h-4 text-green-400" />
                          </div>
                        )}
                      </div>
                    ) : null}
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-400 text-sm mb-4 line-clamp-2">{integration.description}</p>

                {/* Capabilities */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {integration.capabilities.slice(0, 3).map(capability => (
                      <span
                        key={capability}
                        className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded"
                      >
                        {capability.replace(/_/g, ' ')}
                      </span>
                    ))}
                    {integration.capabilities.length > 3 && (
                      <span className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded">
                        +{integration.capabilities.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {integration.documentation_url && (
                      <a
                        href={integration.documentation_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-white transition-colors"
                        title="Documentation"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {connection ? (
                      <>
                        {connection.is_expired && (
                          <button
                            onClick={() => connectIntegration(integration.id)}
                            disabled={isConnecting}
                            className="px-3 py-1 text-sm bg-yellow-600 hover:bg-yellow-700 text-white rounded transition-colors disabled:opacity-50"
                          >
                            {isConnecting ? <Loader2 className="w-3 h-3 animate-spin" /> : 'Reconnect'}
                          </button>
                        )}
                        <button
                          onClick={() => disconnectIntegration(integration.id)}
                          className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                          title="Disconnect"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => connectIntegration(integration.id)}
                        disabled={isConnecting || !integration.is_enabled}
                        className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors disabled:opacity-50 flex items-center space-x-1"
                      >
                        {isConnecting ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Plus className="w-3 h-3" />
                        )}
                        <span>Connect</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          } else {
            // List view
            return (
              <div
                key={integration.id}
                className="bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <img
                      src={integration.logo_url}
                      alt={integration.name}
                      className="w-8 h-8 rounded"
                      onError={(e) => {
                        e.currentTarget.src = '/api/placeholder/32/32';
                      }}
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-semibold text-white">{integration.name}</h3>
                        <span className={`px-2 py-1 text-xs rounded-full border ${getTierColor(integration.tier)}`}>
                          {getTierLabel(integration.tier)}
                        </span>
                        {connection && (
                          <div className="flex items-center space-x-1">
                            {connection.is_expired ? (
                              <div title="Connection expired">
                                <AlertCircle className="w-4 h-4 text-yellow-400" />
                              </div>
                            ) : (
                              <div title="Connected">
                                <CheckCircle className="w-4 h-4 text-green-400" />
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      <p className="text-gray-400 text-sm">{integration.description}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {integration.documentation_url && (
                      <a
                        href={integration.documentation_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-white transition-colors"
                        title="Documentation"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    )}

                    {connection ? (
                      <>
                        {connection.is_expired && (
                          <button
                            onClick={() => connectIntegration(integration.id)}
                            disabled={isConnecting}
                            className="px-3 py-1 text-sm bg-yellow-600 hover:bg-yellow-700 text-white rounded transition-colors disabled:opacity-50"
                          >
                            {isConnecting ? <Loader2 className="w-3 h-3 animate-spin" /> : 'Reconnect'}
                          </button>
                        )}
                        <button
                          onClick={() => disconnectIntegration(integration.id)}
                          className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                          title="Disconnect"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => connectIntegration(integration.id)}
                        disabled={isConnecting || !integration.is_enabled}
                        className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors disabled:opacity-50 flex items-center space-x-1"
                      >
                        {isConnecting ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Plus className="w-3 h-3" />
                        )}
                        <span>Connect</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          }
        })}
      </div>

      {/* Empty State */}
      {getFilteredIntegrations().length === 0 && (
        <div className="text-center py-12">
          <Zap className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-400 mb-2">No integrations found</h3>
          <p className="text-gray-500">
            {searchTerm || selectedCategory !== 'all' || showConnectedOnly
              ? 'Try adjusting your filters to see more integrations.'
              : 'No integrations are available at the moment.'}
          </p>
        </div>
      )}
    </div>
  );
};
