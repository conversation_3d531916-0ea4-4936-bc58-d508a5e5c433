#!/usr/bin/env python3
"""
Setup Migration Environment
Creates a .env file in the scripts directory with the necessary environment variables
"""

import os
import sys

def setup_migration_env():
    """Setup environment variables for migration scripts"""
    print("🔧 Setting up migration environment...")
    
    # Check if parent .env file exists
    parent_env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    scripts_env_path = os.path.join(os.path.dirname(__file__), '.env')
    
    if os.path.exists(parent_env_path):
        print(f"📄 Found parent .env file: {parent_env_path}")
        
        # Read parent .env file
        env_vars = {}
        with open(parent_env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
        
        # Extract required variables
        required_vars = ['SUPABASE_URL', 'SUPABASE_KEY', 'ENCRYPTION_KEY']
        migration_env = {}
        
        for var in required_vars:
            if var in env_vars:
                migration_env[var] = env_vars[var]
                print(f"✅ Found {var}")
            else:
                print(f"❌ Missing {var}")
        
        if len(migration_env) == len(required_vars):
            # Write scripts/.env file
            with open(scripts_env_path, 'w') as f:
                f.write("# Migration Environment Variables\n")
                f.write("# Auto-generated from parent .env file\n\n")
                for key, value in migration_env.items():
                    f.write(f"{key}={value}\n")
            
            print(f"✅ Created migration .env file: {scripts_env_path}")
            print("🎉 Migration environment setup complete!")
            return True
        else:
            print("❌ Missing required environment variables in parent .env file")
            return False
    
    else:
        print(f"❌ Parent .env file not found: {parent_env_path}")
        print("\n📝 Please create a .env file in the project root with:")
        print("   SUPABASE_URL=your_supabase_url")
        print("   SUPABASE_KEY=your_supabase_key") 
        print("   ENCRYPTION_KEY=your_encryption_key")
        return False

if __name__ == "__main__":
    success = setup_migration_env()
    sys.exit(0 if success else 1)
