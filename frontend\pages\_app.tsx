import type { AppProps } from 'next/app';
import { Inter } from 'next/font/google';
import '../styles/globals.css';
import { SimpleErrorBoundary } from '../components/ui/SimpleErrorBoundary';
import { AuthProvider } from '../components/auth/AuthContext';

const inter = Inter({ subsets: ['latin'] });

export default function App({ Component, pageProps }: AppProps) {
  return (
    <div className={inter.className}>
      <SimpleErrorBoundary>
        <AuthProvider>
          <div className="bg-gray-900 text-white min-h-screen">
            <Component {...pageProps} />
          </div>
        </AuthProvider>
      </SimpleErrorBoundary>
    </div>
  );
}
