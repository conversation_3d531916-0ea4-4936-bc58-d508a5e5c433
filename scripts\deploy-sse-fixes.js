#!/usr/bin/env node

/**
 * SSE Fixes Deployment Script
 * Deploys and validates SSE improvements across environments
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class SSEFixesDeployer {
    constructor() {
        this.environment = this.detectEnvironment();
        this.deploymentSteps = [];

        // Load local environment if needed
        if (this.environment !== 'production') {
            this.loadLocalEnvironment();
        }

        console.log(`🚀 SSE Fixes Deployment Script`);
        console.log(`📍 Target Environment: ${this.environment}`);
    }

    loadLocalEnvironment() {
        try {
            const loadLocalEnv = require('./load-local-env.js');
            console.log(`🔧 Loaded local environment variables`);
        } catch (error) {
            console.warn(`⚠️ Could not load local environment: ${error.message}`);
        }
    }

    detectEnvironment() {
        if (process.argv.includes('--production') || process.env.ENVIRONMENT === 'production') {
            return 'production';
        }
        if (process.argv.includes('--docker')) {
            return 'docker';
        }
        return 'local';
    }

    async deploy() {
        console.log(`\n🔧 Starting SSE fixes deployment...`);
        
        try {
            // 1. Pre-deployment validation
            await this.preDeploymentValidation();
            
            // 2. Configure environment
            await this.configureEnvironment();
            
            // 3. Deploy services
            await this.deployServices();
            
            // 4. Post-deployment validation
            await this.postDeploymentValidation();
            
            // 5. Generate deployment report
            this.generateDeploymentReport();
            
            console.log(`\n✅ SSE fixes deployment completed successfully!`);
            
        } catch (error) {
            console.error(`\n❌ Deployment failed:`, error.message);
            process.exit(1);
        }
    }

    async preDeploymentValidation() {
        console.log(`\n📋 Pre-deployment validation...`);
        
        // Check required files
        const requiredFiles = [
            'mcp-proxy/smart_proxy_js.js',
            'mcp-proxy/server.js',
            'nginx-new-architecture.conf',
            'docker-compose.yml',
            'docker-compose.sse-optimized.yml'
        ];
        
        for (const file of requiredFiles) {
            if (fs.existsSync(path.join(process.cwd(), file))) {
                console.log(`   ✅ ${file}: Found`);
            } else {
                throw new Error(`Required file missing: ${file}`);
            }
        }
        
        // Validate environment variables
        const requiredEnvVars = ['N8N_SSE_URL'];
        for (const envVar of requiredEnvVars) {
            if (process.env[envVar]) {
                console.log(`   ✅ ${envVar}: Set`);
            } else {
                console.warn(`   ⚠️ ${envVar}: Not set (may be configured in deployment platform)`);
            }
        }
    }

    async configureEnvironment() {
        console.log(`\n🔧 Configuring environment for ${this.environment}...`);
        
        // Run environment configuration script
        await this.runScript('configure-sse-environment.js');
        
        // Run optimization script
        await this.runScript('optimize-sse-docker.js');
        
        console.log(`   ✅ Environment configuration completed`);
    }

    async deployServices() {
        console.log(`\n🚀 Deploying services...`);
        
        if (this.environment === 'production') {
            await this.deployToProduction();
        } else {
            await this.deployToLocal();
        }
    }

    async deployToProduction() {
        console.log(`   🌐 Deploying to production (Elestio)...`);
        
        // For Elestio, we need to push changes to trigger deployment
        console.log(`   📝 Production deployment steps:`);
        console.log(`   1. Commit all SSE improvements to git`);
        console.log(`   2. Push to main branch to trigger Elestio auto-deployment`);
        console.log(`   3. Monitor deployment through Elestio dashboard`);
        console.log(`   4. Verify services restart with new configuration`);
        
        // Check if we're in a git repository
        if (fs.existsSync('.git')) {
            console.log(`   ✅ Git repository detected`);
            console.log(`   💡 Run: git add . && git commit -m "feat: SSE reliability improvements" && git push`);
        } else {
            console.warn(`   ⚠️ Not in a git repository - manual deployment required`);
        }
    }

    async deployToLocal() {
        console.log(`   🏠 Deploying to local environment...`);
        
        // Stop existing services
        console.log(`   🛑 Stopping existing services...`);
        await this.runCommand('docker-compose', ['down']);
        
        // Build and start with SSE optimizations
        console.log(`   🔨 Building and starting services with SSE optimizations...`);
        
        const composeFiles = [
            '-f', 'docker-compose.yml',
            '-f', 'docker-compose.sse-optimized.yml'
        ];
        
        await this.runCommand('docker-compose', [...composeFiles, 'build', '--no-cache', 'mcp-proxy']);
        await this.runCommand('docker-compose', [...composeFiles, 'up', '-d']);
        
        // Wait for services to be ready
        console.log(`   ⏳ Waiting for services to be ready...`);
        await new Promise(resolve => setTimeout(resolve, 30000));
        
        console.log(`   ✅ Local deployment completed`);
    }

    async postDeploymentValidation() {
        console.log(`\n🧪 Post-deployment validation...`);
        
        // Run SSE implementation tests
        await this.runScript('test-sse-implementation.js', [`--${this.environment}`]);
        
        // Additional health checks
        await this.performHealthChecks();
    }

    async performHealthChecks() {
        console.log(`   🏥 Performing health checks...`);
        
        const baseUrl = this.environment === 'production' 
            ? 'https://mcp.uruenterprises.com'
            : 'http://localhost:3001';
        
        const healthChecks = [
            { name: 'Service Health', endpoint: '/health' },
            { name: 'SSE Status', endpoint: '/api/sse/status' },
            { name: 'SSE Diagnostics', endpoint: '/api/sse/diagnostics' }
        ];
        
        for (const check of healthChecks) {
            try {
                const axios = require('axios');
                const response = await axios.get(`${baseUrl}${check.endpoint}`, {
                    timeout: 10000
                });
                
                if (response.status === 200) {
                    console.log(`   ✅ ${check.name}: Healthy`);
                } else {
                    console.log(`   ⚠️ ${check.name}: Status ${response.status}`);
                }
            } catch (error) {
                console.log(`   ❌ ${check.name}: ${error.message}`);
            }
        }
    }

    generateDeploymentReport() {
        console.log(`\n📊 Deployment Report:`);
        console.log(`=====================`);
        console.log(`Environment: ${this.environment}`);
        console.log(`Timestamp: ${new Date().toISOString()}`);
        console.log(`\n🔧 Deployed Components:`);
        console.log(`• Enhanced SSE connection management`);
        console.log(`• Exponential backoff retry logic`);
        console.log(`• Connection health monitoring`);
        console.log(`• Diagnostic endpoints`);
        console.log(`• Client-side reconnection logic`);
        console.log(`• Environment-specific optimizations`);
        
        console.log(`\n📋 Next Steps:`);
        if (this.environment === 'production') {
            console.log(`• Monitor SSE health: https://mcp.uruenterprises.com/api/sse/status`);
            console.log(`• Check diagnostics: https://mcp.uruenterprises.com/api/sse/diagnostics`);
            console.log(`• Set up monitoring alerts for health score < 70`);
        } else {
            console.log(`• Monitor SSE health: http://localhost:3001/api/sse/status`);
            console.log(`• Test reconnection: curl -X POST http://localhost:3001/api/sse/reconnect`);
            console.log(`• Check logs: docker-compose logs -f mcp-proxy`);
        }
        
        console.log(`\n🎯 Success Metrics to Monitor:`);
        console.log(`• SSE connection health score > 80`);
        console.log(`• Reconnection attempts < 5 per hour`);
        console.log(`• Connection uptime > 95%`);
        console.log(`• Response time < 2 seconds for diagnostics`);
    }

    async runScript(scriptName, args = []) {
        const scriptPath = path.join(__dirname, scriptName);
        if (fs.existsSync(scriptPath)) {
            console.log(`   🔧 Running ${scriptName}...`);
            await this.runCommand('node', [scriptPath, ...args]);
        } else {
            console.warn(`   ⚠️ Script not found: ${scriptName}`);
        }
    }

    async runCommand(command, args = []) {
        return new Promise((resolve, reject) => {
            const process = spawn(command, args, {
                stdio: 'inherit',
                shell: true
            });
            
            process.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`Command failed with code ${code}: ${command} ${args.join(' ')}`));
                }
            });
            
            process.on('error', (error) => {
                reject(error);
            });
        });
    }
}

// Run deployment if called directly
if (require.main === module) {
    const deployer = new SSEFixesDeployer();
    deployer.deploy().catch(console.error);
}

module.exports = SSEFixesDeployer;
