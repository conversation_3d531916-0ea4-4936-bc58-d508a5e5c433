#!/usr/bin/env python3
"""
Critical Security Fixes Application Script
Applies the most critical security fixes to all FastAPI services
"""

import os
import sys
import shutil
from pathlib import Path

def apply_security_middleware_to_service(service_path: str, service_name: str):
    """Apply security middleware to a FastAPI service"""
    main_py_path = Path(service_path) / "main.py"
    
    if not main_py_path.exists():
        print(f"❌ {service_name}: main.py not found at {main_py_path}")
        return False
    
    print(f"🔧 Applying security fixes to {service_name}...")
    
    # Read current content
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if already applied
    if "SecurityHeadersMiddleware" in content:
        print(f"✅ {service_name}: Security middleware already applied")
        return True
    
    # Find FastAPI app creation
    if "app = FastAPI(" not in content:
        print(f"❌ {service_name}: FastAPI app creation not found")
        return False
    
    # Add imports at the top
    import_additions = """
# Security enhancements
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
import uuid
import time
import re
from datetime import datetime
"""
    
    # Add security middleware classes
    middleware_classes = """
# ===========================================
# SECURITY MIDDLEWARE
# ===========================================

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    \"\"\"Add security headers to all responses\"\"\"
    
    async def dispatch(self, request, call_next):
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        
        # HSTS for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # CSP for API endpoints
        response.headers["Content-Security-Policy"] = "default-src 'none'; frame-ancestors 'none';"
        
        return response

class RequestIDMiddleware(BaseHTTPMiddleware):
    \"\"\"Add unique request ID for tracing\"\"\"
    
    async def dispatch(self, request, call_next):
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        
        return response

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    \"\"\"Sanitize error responses for production\"\"\"
    
    async def dispatch(self, request, call_next):
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Log full error details
            request_id = getattr(request.state, 'request_id', 'unknown')
            import logging
            logging.error(f"Request {request_id} failed: {str(e)}", exc_info=True)
            
            # Return sanitized error response
            from fastapi import HTTPException
            if isinstance(e, HTTPException):
                return JSONResponse(
                    status_code=e.status_code,
                    content={
                        "error": e.detail,
                        "request_id": request_id,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )
            else:
                # Generic error for unexpected exceptions
                return JSONResponse(
                    status_code=500,
                    content={
                        "error": "Internal server error",
                        "request_id": request_id,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )

class InputValidator:
    \"\"\"Input validation and sanitization\"\"\"
    
    SQL_INJECTION_PATTERNS = [
        r"(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\\b)",
        r"(--|#|/\\*|\\*/)",
        r"(\\bUNION\\s+SELECT\\b)"
    ]
    
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\\w+\\s*=",
        r"<iframe[^>]*>.*?</iframe>"
    ]
    
    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 1000) -> str:
        if not isinstance(value, str):
            raise ValueError("Input must be a string")
        
        if len(value) > max_length:
            raise ValueError(f"Input too long (max {max_length} characters)")
        
        # Check for malicious patterns
        for pattern in cls.SQL_INJECTION_PATTERNS + cls.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError("Potentially malicious input detected")
        
        return value.strip()

"""
    
    # Add middleware setup after app creation
    middleware_setup = """
# Apply security middleware
app.add_middleware(ErrorHandlingMiddleware)
app.add_middleware(RequestIDMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
"""
    
    # Apply modifications
    lines = content.split('\n')
    new_lines = []
    app_created = False
    imports_added = False
    middleware_added = False
    
    for i, line in enumerate(lines):
        new_lines.append(line)
        
        # Add imports after existing imports
        if not imports_added and line.startswith('from fastapi') and 'FastAPI' in line:
            new_lines.append(import_additions)
            imports_added = True
        
        # Add middleware classes before app creation
        if not middleware_added and 'app = FastAPI(' in line:
            new_lines.insert(-1, middleware_classes)
            middleware_added = True
        
        # Add middleware setup after CORS middleware
        if not app_created and ('add_middleware' in line and 'CORSMiddleware' in line):
            # Look for the closing parenthesis of CORS middleware
            j = i + 1
            while j < len(lines) and ')' not in lines[j]:
                j += 1
            if j < len(lines):
                new_lines.append(middleware_setup)
                app_created = True
    
    # Write back the modified content
    with open(main_py_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"✅ {service_name}: Security middleware applied successfully")
    return True

def main():
    """Apply critical security fixes to all services"""
    print("🚀 Applying critical security fixes to Uru Workspace Platform...")
    
    # Services to update
    services = [
        ("auth-service", "Auth Service"),
        ("integration-service", "Integration Service"),
        ("composio-service", "Composio Service")
    ]
    
    success_count = 0
    
    for service_dir, service_name in services:
        if apply_security_middleware_to_service(service_dir, service_name):
            success_count += 1
    
    print(f"\n🎉 Security fixes applied to {success_count}/{len(services)} services")
    
    if success_count == len(services):
        print("✅ All critical security fixes applied successfully!")
        print("\n📋 Next steps:")
        print("1. Test the services locally")
        print("2. Update API keys in Elestio environment variables")
        print("3. Deploy to production")
        print("4. Verify health endpoints")
    else:
        print("⚠️ Some services failed to update. Please review the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
