# Uru Workspace Platform - Integration System Testing

This document provides comprehensive instructions for testing the expanded integration system locally before deploying to production.

## Overview

The integration system has been expanded to support multiple Composio-powered business productivity tools including:

- **Tier 1**: Slack, Microsoft Teams, Notion, Airtable, Asana
- **Tier 2**: HubSpot, Salesforce, Pipedrive, Intercom, Zendesk
- **Tier 3**: Dropbox, OneDrive, Google Docs, Google Sheets
- **Tier 4**: Calendly, Zoom, Trello, ClickUp, Linear
- **Tier 5**: Mailchimp, Canva, Twitter, LinkedIn

## Prerequisites

1. **Docker & Docker Compose**: Ensure Docker and Docker Compose are installed
2. **Environment Variables**: Create a `.env` file with required configuration
3. **Supabase Access**: Valid Supabase URL and service key
4. **OAuth Credentials**: Optional OAuth credentials for testing specific integrations

## Required Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# Required - Core Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_service_key
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_32_character_encryption_key

# Optional - OAuth Credentials for Testing
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
SLACK_CLIENT_ID=your_slack_client_id
SLACK_CLIENT_SECRET=your_slack_client_secret
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
NOTION_CLIENT_ID=your_notion_client_id
NOTION_CLIENT_SECRET=your_notion_client_secret
AIRTABLE_CLIENT_ID=your_airtable_client_id
AIRTABLE_CLIENT_SECRET=your_airtable_client_secret
ASANA_CLIENT_ID=your_asana_client_id
ASANA_CLIENT_SECRET=your_asana_client_secret
HUBSPOT_CLIENT_ID=your_hubspot_client_id
HUBSPOT_CLIENT_SECRET=your_hubspot_client_secret
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret

# Optional - N8N Configuration
N8N_SSE_URL=your_n8n_sse_endpoint
N8N_TOKEN=your_n8n_token

# Optional - Test Credentials
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=testpassword123
```

## Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <repository>
   cd uru-workspace-platform
   cp .env.example .env  # Edit with your values
   ```

2. **Run Automated Tests**:
   ```bash
   chmod +x scripts/run-local-tests.sh
   ./scripts/run-local-tests.sh
   ```

3. **Manual Testing**:
   ```bash
   # Start services
   docker-compose -f docker-compose.local-test.yml up -d
   
   # Run database setup
   python3 scripts/setup-integration-system.py
   
   # Run tests
   python3 scripts/test-integration-system.py
   ```

## Test Script Options

The `run-local-tests.sh` script supports several options:

```bash
# Basic test run
./scripts/run-local-tests.sh

# Skip cleanup of previous containers
./scripts/run-local-tests.sh --skip-cleanup

# Show service logs after tests
./scripts/run-local-tests.sh --show-logs

# Run tests in container (more isolated)
./scripts/run-local-tests.sh --containerized

# Show help
./scripts/run-local-tests.sh --help
```

## What Gets Tested

### 1. Service Health Checks
- Auth Service (port 8000)
- Composio Service (port 8001)
- MCP Proxy (port 3001)
- Frontend (port 3000)

### 2. Authentication System
- JWT token generation
- Employee authentication
- Token validation

### 3. Integration Management
- Available integrations endpoint
- Integration categories and metadata
- User connection status

### 4. MCP Tool System
- Dynamic tool generation
- Integration-specific tools
- Tool execution framework

### 5. Frontend Integration
- Settings page accessibility
- Integration hub component
- OAuth flow initiation

### 6. Database Schema
- Integration definitions table
- Employee integrations table
- OAuth tokens storage

## Manual Testing Steps

### 1. Access the Frontend
Navigate to `http://localhost:3000/app/settings` to see the Integration Hub interface.

### 2. Test Integration Discovery
- View available integrations by category
- Check integration descriptions and capabilities
- Verify tier-based organization

### 3. Test OAuth Flow (if credentials configured)
- Click "Connect" on an integration
- Complete OAuth authorization
- Verify connection status updates

### 4. Test MCP Tools
- Check available tools at `http://localhost:3001/api/tools`
- Verify tools are generated based on connected integrations
- Test tool execution (mock responses)

### 5. Test API Endpoints
```bash
# Get available integrations
curl -H "Authorization: Bearer <token>" \
     http://localhost:8001/api/uru/integrations/available

# Get user connections
curl -H "Authorization: Bearer <token>" \
     http://localhost:8001/api/uru/integrations/connections

# Execute integration tool
curl -X POST \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"tool_name":"slack_send_message","parameters":{"channel":"#general","message":"test"}}' \
     http://localhost:8001/api/uru/integrations/execute
```

## Troubleshooting

### Common Issues

1. **Services Not Starting**
   - Check Docker is running
   - Verify .env file exists and has required variables
   - Check port conflicts (8000, 8001, 3000, 3001)

2. **Database Connection Errors**
   - Verify Supabase URL and key are correct
   - Check network connectivity to Supabase
   - Ensure service key has proper permissions

3. **Authentication Failures**
   - Verify JWT_SECRET is set
   - Check ENCRYPTION_KEY is 32 characters
   - Ensure test user exists in database

4. **Integration Tests Failing**
   - OAuth credentials may be missing (expected for some tests)
   - Check service logs for detailed error messages
   - Verify integration definitions are populated

### Viewing Logs

```bash
# View all service logs
docker-compose -f docker-compose.local-test.yml logs

# View specific service logs
docker-compose -f docker-compose.local-test.yml logs composio-service

# Follow logs in real-time
docker-compose -f docker-compose.local-test.yml logs -f
```

### Cleanup

```bash
# Stop all services
docker-compose -f docker-compose.local-test.yml down

# Remove all containers and volumes
docker-compose -f docker-compose.local-test.yml down -v

# Clean up Docker system
docker system prune -f
```

## Expected Test Results

A successful test run should show:

- ✅ All services healthy
- ✅ Authentication working
- ✅ 25+ integrations available across 5+ categories
- ✅ Integration tools generated dynamically
- ✅ Frontend accessible with Integration Hub
- ✅ Mock tool execution working

## Next Steps

After successful local testing:

1. **Review Results**: Ensure all tests pass and functionality works as expected
2. **Production Deployment**: Deploy to Elestio using the production environment
3. **OAuth Configuration**: Configure production OAuth credentials for each integration
4. **User Testing**: Test with real users and gather feedback
5. **Monitoring**: Set up monitoring and alerting for the integration system

## Support

If you encounter issues during testing:

1. Check the troubleshooting section above
2. Review service logs for detailed error messages
3. Verify environment configuration
4. Test individual components separately
5. Consult the main project documentation
