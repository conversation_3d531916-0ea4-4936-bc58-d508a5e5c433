'use client';

import { useState } from 'react';
import { Users, CheckCircle, Clock, UserPlus } from 'lucide-react';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'member' | 'viewer';
  status: 'active' | 'pending' | 'inactive';
  avatar: string;
}

export const TeamManagement: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([
    { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'admin', status: 'active', avatar: 'JM' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'member', status: 'active', avatar: 'AM' },
    { id: '3', name: '<PERSON>', email: '<EMAIL>', role: 'member', status: 'pending', avatar: 'CG' },
    { id: '4', name: '<PERSON>', email: '<EMAIL>', role: 'viewer', status: 'active', avatar: 'CF' },
    { id: '5', name: '<PERSON>', email: '<EMAIL>', role: 'member', status: 'inactive', avatar: 'DP' },
  ]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'member': return 'bg-blue-100 text-blue-800';
      case 'viewer': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return null;
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Team Members</h1>
          <p className="text-gray-400">Manage your workspace members and their roles.</p>
        </div>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2">
          <UserPlus className="w-4 h-4" />
          <span>Invite Member</span>
        </button>
      </div>

      <div className="bg-gray-800 rounded-xl border border-gray-700">
        <table className="w-full">
          <thead className="border-b border-gray-700">
            <tr>
              <th className="text-left text-sm font-semibold text-gray-400 p-4">Name</th>
              <th className="text-left text-sm font-semibold text-gray-400 p-4">Status</th>
              <th className="text-left text-sm font-semibold text-gray-400 p-4">Role</th>
              <th className="text-right text-sm font-semibold text-gray-400 p-4">Actions</th>
            </tr>
          </thead>
          <tbody>
            {teamMembers.map((member) => (
              <tr key={member.id} className="border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50">
                <td className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {member.avatar}
                    </div>
                    <div>
                      <p className="font-medium text-white">{member.name}</p>
                      <p className="text-sm text-gray-400">{member.email}</p>
                    </div>
                  </div>
                </td>
                <td className="p-4">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(member.status)}
                    <span className="text-gray-300 capitalize">{member.status}</span>
                  </div>
                </td>
                <td className="p-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(member.role)} capitalize`}>
                    {member.role}
                  </span>
                </td>
                <td className="p-4 text-right">
                  <button className="text-blue-400 hover:text-blue-300">Edit</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}; 