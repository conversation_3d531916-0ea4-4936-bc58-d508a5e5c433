/**
 * Comprehensive error handling utilities for production deployment
 */

export interface ErrorDetails {
  message: string;
  type: 'network' | 'authentication' | 'validation' | 'server' | 'unknown';
  statusCode?: number;
  originalError?: any;
}

export class AppError extends Error {
  public type: ErrorDetails['type'];
  public statusCode?: number;
  public originalError?: any;

  constructor(message: string, type: ErrorDetails['type'], statusCode?: number, originalError?: any) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode;
    this.originalError = originalError;
  }
}

/**
 * Parse and categorize errors for better user feedback
 */
export function parseError(error: any): ErrorDetails {
  // Handle AppError instances
  if (error instanceof AppError) {
    return {
      message: error.message,
      type: error.type,
      statusCode: error.statusCode,
      originalError: error.originalError
    };
  }

  // Handle fetch/network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return {
      message: 'Network connection failed. Please check your internet connection and try again.',
      type: 'network',
      originalError: error
    };
  }

  // Handle HTTP errors
  if (error.message && typeof error.message === 'string') {
    const message = error.message.toLowerCase();
    
    // Authentication errors
    if (message.includes('unauthorized') || message.includes('401')) {
      return {
        message: 'Authentication failed. Please log in again.',
        type: 'authentication',
        statusCode: 401,
        originalError: error
      };
    }
    
    // Forbidden errors
    if (message.includes('forbidden') || message.includes('403')) {
      return {
        message: 'You do not have permission to perform this action.',
        type: 'authentication',
        statusCode: 403,
        originalError: error
      };
    }
    
    // Not found errors
    if (message.includes('not found') || message.includes('404')) {
      return {
        message: 'The requested resource was not found.',
        type: 'server',
        statusCode: 404,
        originalError: error
      };
    }
    
    // Server errors
    if (message.includes('500') || message.includes('internal server error')) {
      return {
        message: 'Server error occurred. Please try again later.',
        type: 'server',
        statusCode: 500,
        originalError: error
      };
    }
    
    // CORS errors
    if (message.includes('cors') || message.includes('cross-origin')) {
      return {
        message: 'Connection error. Please ensure the backend services are accessible.',
        type: 'network',
        originalError: error
      };
    }
    
    // Validation errors
    if (message.includes('duplicate') || message.includes('already exists')) {
      return {
        message: error.message,
        type: 'validation',
        originalError: error
      };
    }
    
    if (message.includes('invalid') || message.includes('required')) {
      return {
        message: error.message,
        type: 'validation',
        originalError: error
      };
    }
  }

  // Default error
  return {
    message: error.message || 'An unexpected error occurred. Please try again.',
    type: 'unknown',
    originalError: error
  };
}

/**
 * Get user-friendly error message based on error type and context
 */
export function getUserFriendlyMessage(error: ErrorDetails, context?: string): string {
  const contextPrefix = context ? `${context}: ` : '';
  
  switch (error.type) {
    case 'network':
      return `${contextPrefix}Network connection failed. Please check your internet connection and try again.`;
    
    case 'authentication':
      return `${contextPrefix}Authentication failed. Please log in again.`;
    
    case 'validation':
      return error.message; // Validation messages are usually already user-friendly
    
    case 'server':
      if (error.statusCode === 404) {
        return `${contextPrefix}The requested resource was not found.`;
      }
      return `${contextPrefix}Server error occurred. Please try again later.`;
    
    default:
      return `${contextPrefix}${error.message}`;
  }
}

/**
 * Log error details for debugging in production
 */
export function logError(error: ErrorDetails, context?: string): void {
  const logData = {
    timestamp: new Date().toISOString(),
    context: context || 'Unknown',
    type: error.type,
    message: error.message,
    statusCode: error.statusCode,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server',
    url: typeof window !== 'undefined' ? window.location.href : 'Server',
    originalError: error.originalError
  };
  
  // In production, you might want to send this to an error tracking service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error tracking service
    // errorTrackingService.captureError(logData);
  }
}

/**
 * Handle errors consistently across the application
 */
export function handleError(error: any, context?: string): ErrorDetails {
  const parsedError = parseError(error);
  logError(parsedError, context);
  return parsedError;
}
